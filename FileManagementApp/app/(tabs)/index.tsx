import { Image } from 'expo-image';
import { Platform, StyleSheet, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';

import { HelloWave } from '@/components/HelloWave';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function HomeScreen() {
  const colorScheme = useColorScheme();

  const navigateToFiles = () => {
    router.push('/(tabs)/files');
  };

  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
      headerImage={
        <IconSymbol
          name="building.2.fill"
          size={120}
          color="rgba(255,255,255,0.8)"
          style={styles.headerIcon}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">Classroom File Manager</ThemedText>
        <HelloWave />
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Welcome to Interactive Learning</ThemedText>
        <ThemedText>
          This classroom file management system allows teachers to quickly access their cloud files
          on interactive displays. Simply scan your QR code to get started.
        </ThemedText>
      </ThemedView>

      <TouchableOpacity
        style={[styles.quickAccessButton, { backgroundColor: Colors[colorScheme ?? 'light'].tint }]}
        onPress={navigateToFiles}
      >
        <IconSymbol name="folder.fill" size={24} color="white" />
        <ThemedText style={styles.quickAccessText}>Access My Files</ThemedText>
        <IconSymbol name="arrow.right" size={20} color="white" />
      </TouchableOpacity>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Features</ThemedText>
        <ThemedView style={styles.featureList}>
          <ThemedView style={styles.featureItem}>
            <IconSymbol name="qrcode" size={20} color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.featureText}>QR Code Authentication</ThemedText>
          </ThemedView>
          <ThemedView style={styles.featureItem}>
            <IconSymbol name="icloud.fill" size={20} color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.featureText}>Cloud Storage Integration</ThemedText>
          </ThemedView>
          <ThemedView style={styles.featureItem}>
            <IconSymbol name="doc.fill" size={20} color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.featureText}>File Preview & Management</ThemedText>
          </ThemedView>
          <ThemedView style={styles.featureItem}>
            <IconSymbol name="lock.fill" size={20} color={Colors[colorScheme ?? 'light'].tint} />
            <ThemedText style={styles.featureText}>Secure Session Management</ThemedText>
          </ThemedView>
        </ThemedView>
      </ThemedView>

      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Getting Started</ThemedText>
        <ThemedText>
          1. Tap "Access My Files" above{'\n'}
          2. Scan your teacher QR code{'\n'}
          3. Browse and access your cloud files{'\n'}
          4. Logout when finished to secure your session
        </ThemedText>
      </ThemedView>
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stepContainer: {
    gap: 8,
    marginBottom: 24,
  },
  headerIcon: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  quickAccessButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    marginVertical: 16,
    gap: 12,
  },
  quickAccessText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  featureList: {
    gap: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureText: {
    fontSize: 16,
  },
});
