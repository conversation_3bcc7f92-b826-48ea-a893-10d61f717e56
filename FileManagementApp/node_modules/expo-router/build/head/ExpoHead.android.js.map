{"version": 3, "file": "ExpoHead.android.js", "sourceRoot": "", "sources": ["../../src/head/ExpoHead.android.tsx"], "names": [], "mappings": ";;;;;AAEA,oBAEC;AAJD,kDAAsD;AAEtD,SAAgB,IAAI,CAAC,KAAwB;IAC3C,OAAO,IAAI,CAAC;AACd,CAAC;AAED,IAAI,CAAC,QAAQ,GAAG,eAAK,CAAC,QAAQ,CAAC", "sourcesContent": ["import React, { type PropsWithChildren } from 'react';\n\nexport function Head(props: PropsWithChildren) {\n  return null;\n}\n\nHead.Provider = React.Fragment;\n"]}