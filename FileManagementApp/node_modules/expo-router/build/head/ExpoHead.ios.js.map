{"version": 3, "file": "ExpoHead.ios.js", "sourceRoot": "", "sources": ["../../src/head/ExpoHead.ios.tsx"], "names": [], "mappings": ";;;;;;AAAA,qDAAwD;AACxD,kDAAmC;AAEnC,qDAA0D;AAC1D,+BAAmD;AACnD,oCAAiG;AAEjG,SAAS,OAAO,CAAC,GAAW;IAC1B,OAAO,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,cAAc,CAAC,IAAY;IAClC,uBAAuB;IACvB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;IAChD,OAAO,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC;AAED,+FAA+F;AAE/F,SAAS,kBAAkB;IACzB,MAAM,QAAQ,GAAG,IAAA,6BAAqB,GAAE,CAAC;IACzC,MAAM,MAAM,GAAG,IAAA,4BAAoB,GAAO,CAAC;IAC3C,MAAM,GAAG,GAAG,IAAA,gCAA0B,EAAC,QAAQ,CAAC,CAAC;IACjD,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;AACnC,CAAC;AAMD,SAAS,eAAe,CAAC,QAAyB;IAChD,OAAO,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QACxB,MAAM,kBAAkB,GAAsB,EAAE,CAAC;QACjD,MAAM,YAAY,GAAe,EAAE,CAAC;QAEpC,eAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;YACzC,IAAI,CAAC,eAAK,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;gBACjC,OAAO;YACT,CAAC;YACD,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACnC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,EAAE,QAAQ,EAAE,kBAAkB,EAAE,YAAY,EAAE,CAAC;IACxD,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjB,CAAC;AAOD,SAAS,sBAAsB,CAAC,IAAgB;IAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;IAE1F,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QAC7B,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC3B,OAAO;gBACL,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE;oBACL,QAAQ,EACN,KAAK,CAAC,KAAK;wBACX,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ;wBAC/B,UAAU,IAAI,KAAK,CAAC,KAAK;wBACzB,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ;wBACtC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ;wBACtB,CAAC,CAAC,SAAS;iBAChB;aACF,CAAC;QACJ,CAAC;QACD,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE;gBACL,QAAQ,EACN,KAAK,CAAC,KAAK;oBACX,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ;oBAC/B,UAAU,IAAI,KAAK,CAAC,KAAK;oBACzB,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ;oBACtC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ;oBACtB,CAAC,CAAC,SAAS;gBACf,OAAO,EACL,KAAK,CAAC,KAAK;oBACX,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ;oBAC/B,SAAS,IAAI,KAAK,CAAC,KAAK;oBACxB,OAAO,KAAK,CAAC,KAAK,CAAC,OAAO,KAAK,QAAQ;oBACrC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;oBACrB,CAAC,CAAC,SAAS;aAChB;SACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,2BAA2B,CAAC,IAAgB;IACnD,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,kBAAkB,EAAE,CAAC;IAErD,MAAM,YAAY,GAAG,eAAK,CAAC,MAAM,CAAmB,EAAE,CAAC,CAAC;IACxD,MAAM,cAAc,GAAG,eAAK,CAAC,MAAM,CAAwB,EAAE,CAAC,CAAC;IAE/D,MAAM,UAAU,GAAG,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAE7E,MAAM,GAAG,GAAG,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QAC7B,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAC7B,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,CACtE,CAAC;QAEF,IAAI,OAAO,EAAE,CAAC;YACZ,uDAAuD;YACvD,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3C,OAAO,IAAA,gCAA0B,EAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC3D,CAAC;YACD,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;QAC/B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;IAEvB,MAAM,KAAK,GAAG,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QAC/B,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;QACpE,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC;QACvC,CAAC;QACD,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAC/B,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,CACxE,CAAC;QACF,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,SAAS,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;QACvC,CAAC;QAED,OAAO,cAAc,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE3B,MAAM,QAAQ,GAAG,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QAClC,IACE,CAAC,CAAC,YAAY,CAAC,OAAO;YACtB,CAAC,CAAC,cAAc,CAAC,OAAO;YACxB,iBAAiB,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC,EACnD,CAAC;YACD,OAAO,cAAc,CAAC,OAAO,CAAC;QAChC,CAAC;QACD,YAAY,CAAC,OAAO,GAAG,UAAU,CAAC;QAElC,MAAM,YAAY,GAA0B,EAAE,CAAC;QAE/C,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC3B;YACE,WAAW;YACX,KAAK,CAAC,IAAI,KAAK,MAAM,EACrB,CAAC;gBACD,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC;gBAE1C,QAAQ,QAAQ,EAAE,CAAC;oBACjB,KAAK,gBAAgB;wBACnB,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC;wBACnC,MAAM;oBACR,oBAAoB;oBACpB,KAAK,cAAc;wBACjB,YAAY,CAAC,oBAAoB,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;wBACtD,MAAM;oBACR,KAAK,gBAAgB;wBACnB,YAAY,CAAC,mBAAmB,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;wBACrD,MAAM;gBACV,CAAC;gBAED,oDAAoD;gBACpD,qCAAqC;gBACrC,mDAAmD;gBACnD,gBAAgB;gBAChB,4BAA4B;gBAC5B,IAAI;YACN,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,cAAc,CAAC,OAAO,GAAG,YAAY,CAAC;QACtC,OAAO,YAAY,CAAC;IACtB,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;IAE3B,MAAM,cAAc,GAAiB;QACnC,QAAQ,EAAE,CAAC,KAAK,CAAC;QACjB,GAAG,QAAQ;QACX,KAAK;QACL,UAAU,EAAE,GAAG;QACf,YAAY,EAAE,yBAAS,CAAC,UAAU,CAAC,aAAa;QAChD,QAAQ,EAAE;YACR,0FAA0F;YAC1F,IAAI;SACL;KACF,CAAC;IAEF,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,QAAQ,CAAC,KAAU;IAC1B,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,UAAU,CAAC,KAAqC;IACvD,MAAM,SAAS,GAAG,IAAA,qBAAY,GAAE,CAAC;IACjC,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,OAAO,CAAC,aAAa,CAAC,AAAD,EAAG,CAAC;IAC3B,CAAC;IACD,OAAO,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,EAAG,CAAC;AACpC,CAAC;AAED,SAAS,aAAa,CAAC,KAAqC;IAC1D,MAAM,EAAE,QAAQ,EAAE,GAAG,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACrD,OAAO,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC;AACzB,CAAC;AAED,SAAS,WAAW,CAAC,KAAqC;IACxD,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACnE,MAAM,QAAQ,GAAG,2BAA2B,CAAC,YAAY,CAAC,CAAC;IAC3D,0BAA0B,CAAC,QAAQ,CAAC,CAAC;IACrC,OAAO,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC;AACzB,CAAC;AAED,uBAAuB;AACvB,MAAM,UAAU,GAA8B,IAAI,GAAG,EAAE,CAAC;AAExD,SAAS,0BAA0B,CAAC,QAAsB;IACxD,kGAAkG;IAClG,mEAAmE;IACnE,MAAM,UAAU,GAAG,OAAO,CAAC,IAAA,mBAAW,GAAE,IAAI,GAAG,CAAC,CAAC;IACjD,MAAM,WAAW,GAAG,OAAO,CAAC,IAAA,mBAAW,GAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;IAC5D,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;IAClD,MAAM,iBAAiB,GAAiB,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QACzD,wFAAwF;QACxF,+IAA+I;QAC/I,MAAM,iBAAiB,GAAG,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC;YACnD,CAAC,CAAC;gBACE,GAAG,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC;gBAC9B,GAAG,QAAQ;gBACX,EAAE,EAAE,UAAU;aACf;YACH,CAAC,CAAC;gBACE,GAAG,QAAQ;gBACX,EAAE,EAAE,UAAU;aACf,CAAC;QACN,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;QAE/C,OAAO,iBAAiB,CAAC;IAC3B,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;IAErD,MAAM,gBAAgB,GAAG,eAAK,CAAC,MAAM,CAAsB,IAAI,CAAC,CAAC;IAEjE,eAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,OAAO,GAAG,EAAE,GAAE,CAAC,CAAC;QAClB,CAAC;QACD,IACE,CAAC,CAAC,gBAAgB,CAAC,OAAO;YAC1B,iBAAiB,CAAC,gBAAgB,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAC9D,CAAC;YACD,OAAO,GAAG,EAAE,GAAE,CAAC,CAAC;QAClB,CAAC;QAED,gBAAgB,CAAC,OAAO,GAAG,iBAAiB,CAAC;QAC7C,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,iEAAiE;QACjE,IAAI,iBAAiB,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;YACpF,yBAAQ,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,GAAG,EAAE,GAAE,CAAC,CAAC;IAClB,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC;IAExB,eAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,OAAO,GAAG,EAAE;YACV,IAAI,UAAU,EAAE,CAAC;gBACf,yBAAQ,EAAE,eAAe,CAAC,UAAU,CAAC,CAAC;YACxC,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;AACnB,CAAC;AAED,SAAS,iBAAiB,CAAC,CAAM,EAAE,CAAM;IACvC,IAAI,OAAO,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;QAC1B,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;gBAC1B,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC;QACD,cAAc;QACd,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YAC7B,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC;QACD,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IACD,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB,CAAC;AAED,UAAU,CAAC,QAAQ,GAAG,eAAK,CAAC,QAAQ,CAAC;AAErC,SAAS,QAAQ,CAAC,KAA8B;IAC9C,OAAO,IAAI,CAAC;AACd,CAAC;AAED,QAAQ,CAAC,QAAQ,GAAG,eAAK,CAAC,QAAQ,CAAC;AAEnC,gDAAgD;AACnC,QAAA,IAAI,GAEb,yBAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC", "sourcesContent": ["import { useIsFocused } from '@react-navigation/native';\nimport React, { JSX } from 'react';\n\nimport { ExpoHead, UserActivity } from './ExpoHeadModule';\nimport { getStaticUrlFromExpoRouter } from './url';\nimport { useLocalSearchParams, useUnstableGlobalHref, usePathname, useSegments } from '../hooks';\n\nfunction urlToId(url: string) {\n  return url.replace(/[^a-zA-Z0-9]/g, '-');\n}\n\nfunction getLastSegment(path: string) {\n  // Remove the extension\n  const lastSegment = path.split('/').pop() ?? '';\n  return lastSegment.replace(/\\.[^/.]+$/, '').split('?')[0];\n}\n\n// TODO: Use Head Provider to collect all props so only one Head is rendered for a given route.\n\nfunction useAddressableLink() {\n  const pathname = useUnstableGlobalHref();\n  const params = useLocalSearchParams<any>();\n  const url = getStaticUrlFromExpoRouter(pathname);\n  return { url, pathname, params };\n}\n\ntype MetaNode =\n  | React.ReactPortal\n  | React.ReactElement<unknown, string | React.JSXElementConstructor<any>>;\n\nfunction useMetaChildren(children: React.ReactNode) {\n  return React.useMemo(() => {\n    const renderableChildren: React.ReactNode[] = [];\n    const metaChildren: MetaNode[] = [];\n\n    React.Children.forEach(children, (child) => {\n      if (!React.isValidElement(child)) {\n        return;\n      }\n      if (typeof child.type === 'string') {\n        metaChildren.push(child);\n      } else {\n        renderableChildren.push(child);\n      }\n    });\n\n    return { children: renderableChildren, metaChildren };\n  }, [children]);\n}\n\ntype SerializedMeta = {\n  type: string;\n  props: Record<string, string | undefined>;\n};\n\nfunction serializedMetaChildren(meta: MetaNode[]): SerializedMeta[] {\n  const validMeta = meta.filter((child) => child.type === 'meta' || child.type === 'title');\n\n  return validMeta.map((child) => {\n    if (child.type === 'title') {\n      return {\n        type: 'title',\n        props: {\n          children:\n            child.props &&\n            typeof child.props === 'object' &&\n            'children' in child.props &&\n            typeof child.props.children === 'string'\n              ? child.props.children\n              : undefined,\n        },\n      };\n    }\n    return {\n      type: 'meta',\n      props: {\n        property:\n          child.props &&\n          typeof child.props === 'object' &&\n          'property' in child.props &&\n          typeof child.props.property === 'string'\n            ? child.props.property\n            : undefined,\n        content:\n          child.props &&\n          typeof child.props === 'object' &&\n          'content' in child.props &&\n          typeof child.props.content === 'string'\n            ? child.props.content\n            : undefined,\n      },\n    };\n  });\n}\n\nfunction useActivityFromMetaChildren(meta: MetaNode[]) {\n  const { url: href, pathname } = useAddressableLink();\n\n  const previousMeta = React.useRef<SerializedMeta[]>([]);\n  const cachedActivity = React.useRef<Partial<UserActivity>>({});\n\n  const sortedMeta = React.useMemo(() => serializedMetaChildren(meta), [meta]);\n\n  const url = React.useMemo(() => {\n    const urlMeta = sortedMeta.find(\n      (child) => child.type === 'meta' && child.props.property === 'og:url'\n    );\n\n    if (urlMeta) {\n      // Support =`/foo/bar` -> `https://example.com/foo/bar`\n      if (urlMeta.props.content?.startsWith('/')) {\n        return getStaticUrlFromExpoRouter(urlMeta.props.content);\n      }\n      return urlMeta.props.content;\n    }\n    return href;\n  }, [sortedMeta, href]);\n\n  const title = React.useMemo(() => {\n    const titleTag = sortedMeta.find((child) => child.type === 'title');\n    if (titleTag) {\n      return titleTag.props.children ?? '';\n    }\n    const titleMeta = sortedMeta.find(\n      (child) => child.type === 'meta' && child.props.property === 'og:title'\n    );\n    if (titleMeta) {\n      return titleMeta.props.content ?? '';\n    }\n\n    return getLastSegment(pathname);\n  }, [sortedMeta, pathname]);\n\n  const activity = React.useMemo(() => {\n    if (\n      !!previousMeta.current &&\n      !!cachedActivity.current &&\n      deepObjectCompare(previousMeta.current, sortedMeta)\n    ) {\n      return cachedActivity.current;\n    }\n    previousMeta.current = sortedMeta;\n\n    const userActivity: Partial<UserActivity> = {};\n\n    sortedMeta.forEach((child) => {\n      if (\n        // <meta />\n        child.type === 'meta'\n      ) {\n        const { property, content } = child.props;\n\n        switch (property) {\n          case 'og:description':\n            userActivity.description = content;\n            break;\n          // Custom properties\n          case 'expo:handoff':\n            userActivity.isEligibleForHandoff = isTruthy(content);\n            break;\n          case 'expo:spotlight':\n            userActivity.isEligibleForSearch = isTruthy(content);\n            break;\n        }\n\n        // // <meta name=\"keywords\" content=\"foo,bar,baz\" />\n        // if ([\"keywords\"].includes(name)) {\n        //   userActivity.keywords = Array.isArray(content)\n        //     ? content\n        //     : content.split(\",\");\n        // }\n      }\n    });\n\n    cachedActivity.current = userActivity;\n    return userActivity;\n  }, [meta, pathname, href]);\n\n  const parsedActivity: UserActivity = {\n    keywords: [title],\n    ...activity,\n    title,\n    webpageURL: url,\n    activityType: ExpoHead!.activities.INDEXED_ROUTE,\n    userInfo: {\n      // TODO: This may need to be  versioned in the future, e.g. `_v1` if we change the format.\n      href,\n    },\n  };\n\n  return parsedActivity;\n}\n\nfunction isTruthy(value: any): boolean {\n  return [true, 'true'].includes(value);\n}\n\nfunction HeadNative(props: { children?: React.ReactNode }) {\n  const isFocused = useIsFocused();\n  if (!isFocused) {\n    return <UnfocusedHead />;\n  }\n  return <FocusedHead {...props} />;\n}\n\nfunction UnfocusedHead(props: { children?: React.ReactNode }): JSX.Element {\n  const { children } = useMetaChildren(props.children);\n  return <>{children}</>;\n}\n\nfunction FocusedHead(props: { children?: React.ReactNode }): JSX.Element {\n  const { metaChildren, children } = useMetaChildren(props.children);\n  const activity = useActivityFromMetaChildren(metaChildren);\n  useRegisterCurrentActivity(activity);\n  return <>{children}</>;\n}\n\n// segments => activity\nconst activities: Map<string, UserActivity> = new Map();\n\nfunction useRegisterCurrentActivity(activity: UserActivity) {\n  // ID is tied to Expo Router and agnostic of URLs to ensure dynamic parameters are not considered.\n  // Using all segments ensures that cascading routes are considered.\n  const activityId = urlToId(usePathname() || '/');\n  const cascadingId = urlToId(useSegments().join('-') || '-');\n  const activityIds = Array.from(activities.keys());\n  const cascadingActivity: UserActivity = React.useMemo(() => {\n    // Get all nested activities together, then update the id to match the current pathname.\n    // This enables cases like `/user/[name]/post/[id]` to match all nesting, while still having a URL-specific ID, i.e. `/user/evanbacon/post/123`\n    const cascadingActivity = activities.has(cascadingId)\n      ? {\n          ...activities.get(cascadingId),\n          ...activity,\n          id: activityId,\n        }\n      : {\n          ...activity,\n          id: activityId,\n        };\n    activities.set(cascadingId, cascadingActivity);\n\n    return cascadingActivity;\n  }, [cascadingId, activityId, activity, activityIds]);\n\n  const previousActivity = React.useRef<UserActivity | null>(null);\n\n  React.useEffect(() => {\n    if (!cascadingActivity) {\n      return () => {};\n    }\n    if (\n      !!previousActivity.current &&\n      deepObjectCompare(previousActivity.current, cascadingActivity)\n    ) {\n      return () => {};\n    }\n\n    previousActivity.current = cascadingActivity;\n    if (!cascadingActivity.id) {\n      throw new Error('Activity must have an ID');\n    }\n\n    // If no features are enabled, then skip registering the activity\n    if (cascadingActivity.isEligibleForHandoff || cascadingActivity.isEligibleForSearch) {\n      ExpoHead?.createActivity(cascadingActivity);\n    }\n\n    return () => {};\n  }, [cascadingActivity]);\n\n  React.useEffect(() => {\n    return () => {\n      if (activityId) {\n        ExpoHead?.suspendActivity(activityId);\n      }\n    };\n  }, [activityId]);\n}\n\nfunction deepObjectCompare(a: any, b: any): boolean {\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'object') {\n    if (Array.isArray(a) !== Array.isArray(b)) {\n      return false;\n    }\n    if (Array.isArray(a)) {\n      if (a.length !== b.length) {\n        return false;\n      }\n      return a.every((item, index) => deepObjectCompare(item, b[index]));\n    }\n    // handle null\n    if (a === null || b === null) {\n      return a === b;\n    }\n    const aKeys = Object.keys(a);\n    const bKeys = Object.keys(b);\n    if (aKeys.length !== bKeys.length) {\n      return false;\n    }\n    return aKeys.every((key) => deepObjectCompare(a[key], b[key]));\n  }\n  return a === b;\n}\n\nHeadNative.Provider = React.Fragment;\n\nfunction HeadShim(props: React.PropsWithChildren) {\n  return null;\n}\n\nHeadShim.Provider = React.Fragment;\n\n// Native Head is only enabled in bare iOS apps.\nexport const Head: ((props: React.PropsWithChildren) => React.ReactNode) & {\n  Provider: React.ComponentType;\n} = ExpoHead ? HeadNative : HeadShim;\n"]}