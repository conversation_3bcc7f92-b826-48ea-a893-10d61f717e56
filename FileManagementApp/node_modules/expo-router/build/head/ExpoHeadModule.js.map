{"version": 3, "file": "ExpoHeadModule.js", "sourceRoot": "", "sources": ["../../src/head/ExpoHeadModule.ts"], "names": [], "mappings": ";AAAA,0BAA0B;AAC1B,sGAAsG;;;AA+BtG,IAAI,QAAQ,GASD,IAAI,CAAC;AAOP,4BAAQ;AALjB,yBAAyB;AACzB,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACrE,mBAAA,QAAQ,GAAG,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC;AAChD,CAAC", "sourcesContent": ["// isEligibleForPrediction\n// https://developer.apple.com/documentation/foundation/nsuseractivity/2980674-iseligibleforprediction\n\nexport type UserActivity = {\n  id?: string;\n  /**\n   * The activity title should be clear and concise. This text describes the content of the link, like “Photo taken on July 27, 2020” or “Conversation with <PERSON><PERSON>. Use nouns for activity titles.\n   */\n  title?: string;\n  description?: string;\n  webpageURL?: string;\n  keywords?: string[];\n  // TODO: Get this automatically somehow\n  activityType: string;\n  // TODO: Maybe something like robots.txt?\n  phrase?: string;\n\n  thumbnailURL?: string;\n\n  userInfo?: Record<string, string>;\n\n  isEligibleForHandoff?: boolean;\n  isEligibleForPrediction?: boolean;\n  isEligibleForSearch?: boolean;\n\n  /** Local file path for an image */\n  imageUrl?: string;\n  darkImageUrl?: string;\n  dateModified?: Date;\n  expirationDate?: Date;\n};\n\nlet ExpoHead: {\n  activities: {\n    INDEXED_ROUTE: string;\n  };\n  getLaunchActivity(): UserActivity;\n  createActivity(userActivity: UserActivity): void;\n  clearActivitiesAsync(ids: string[]): Promise<void>;\n  suspendActivity(id: string): void;\n  revokeActivity(id: string): void;\n} | null = null;\n\n// If running in Expo Go.\nif (typeof expo !== 'undefined' && !globalThis.expo?.modules?.ExpoGo) {\n  ExpoHead = globalThis.expo?.modules?.ExpoHead;\n}\n\nexport { ExpoHead };\n"]}