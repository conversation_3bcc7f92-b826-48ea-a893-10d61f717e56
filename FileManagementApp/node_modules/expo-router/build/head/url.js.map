{"version": 3, "file": "url.js", "sourceRoot": "", "sources": ["../../src/head/url.tsx"], "names": [], "mappings": ";;;;;AAqEA,wDAmBC;AAeD,gEAIC;AA3GD,oEAAuC;AAEvC,MAAM,qBAAqB,GAAG,2DAA2D,CAAC;AAE1F,uBAAuB;AACvB,SAAS,OAAO,CAAoC,EAAK;IACvD,MAAM,KAAK,GAAwB,EAAE,CAAC;IACtC,OAAO,CAAC,CAAC,GAAG,IAAW,EAAE,EAAE;QACzB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;YACf,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC;QACpB,CAAC;QACD,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QAC3B,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;QACpB,OAAO,MAAM,CAAC;IAChB,CAAC,CAAQ,CAAC;AACZ,CAAC;AAED,SAAS,WAAW,CAAC,GAAW;IAC9B,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAE5B,wCAAwC;IACxC,MAAM,aAAa,GACjB,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,KAAK,OAAO,IAAI,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC;IAElF,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,YAAY,CACV,kDAAkD,MAAM,CAAC,QAAQ,6BAA6B,qBAAqB,GAAG,CACvH,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;IACnB,MAAM,CAAC,IAAI,GAAG,EAAE,CAAC;IACjB,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC;IAE7B,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC9C,CAAC;AAED,MAAM,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;AAE7C,SAAS,0BAA0B;IACjC,0DAA0D;IAC1D,MAAM,QAAQ,GAAG,wBAAS,CAAC,UAAU,CAAC;IAEtC,MAAM,MAAM,GACV,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU;QACnC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM;QAC/B,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC;IAE3C,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,YAAY,CACV,kGAAkG,qBAAqB,uCAAuC,CAC/J,CAAC;QACF,kDAAkD;QAClD,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,uEAAuE;IACvE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;QACpC,OAAO,CAAC,IAAI,CACV,sBAAsB,MAAM,yCAAyC,qBAAqB,GAAG,CAC9F,CAAC;IACJ,CAAC;IAED,kFAAkF;IAClF,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC;AACjC,CAAC;AAED,SAAgB,sBAAsB;IACpC,0DAA0D;IAC1D,MAAM,QAAQ,GAAG,wBAAS,CAAC,UAAU,CAAC;IAEtC,MAAM,MAAM,GACV,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU;QACnC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM;QAC/B,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC;IAE3C,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,YAAY,CACV,yFAAyF,qBAAqB,uCAAuC,CACtJ,CAAC;QACF,kDAAkD;QAClD,OAAO,uBAAuB,CAAC;IACjC,CAAC;IAED,kFAAkF;IAClF,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,YAAY,CAAC,GAAW;IAC/B,4DAA4D;IAC5D;IACE,iCAAiC;IACjC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EACrC,CAAC;QACD,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnB,KAAK,CAAC,GAAG,CAAC,CAAC;IACb,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IACvB,CAAC;AACH,CAAC;AAED,SAAgB,0BAA0B,CAAC,QAAgB;IACzD,kCAAkC;IAClC,sCAAsC;IACtC,OAAO,0BAA0B,EAAE,GAAG,QAAQ,CAAC;AACjD,CAAC", "sourcesContent": ["import Constants from 'expo-constants';\n\nconst protocolWarningString = `{ plugins: [[\"expo-router\", { origin: \"...<URL>...\" }]] }`;\n\n/** `lodash.memoize` */\nfunction memoize<T extends (...args: any[]) => any>(fn: T): T {\n  const cache: Record<string, any> = {};\n  return ((...args: any[]) => {\n    const key = JSON.stringify(args);\n    if (cache[key]) {\n      return cache[key];\n    }\n    const result = fn(...args);\n    cache[key] = result;\n    return result;\n  }) as any;\n}\n\nfunction sanitizeUrl(url: string): string {\n  const parsed = new URL(url);\n\n  // Allow empty protocol, http, and https\n  const validProtocol =\n    !parsed.protocol || parsed.protocol === 'http:' || parsed.protocol === 'https:';\n\n  if (!validProtocol) {\n    throwOrAlert(\n      `Expo Head: Native origin has invalid protocol \"${parsed.protocol}\" for URL in Expo Config: ${protocolWarningString}.`\n    );\n  }\n\n  parsed.pathname = '';\n  parsed.search = '';\n  parsed.hash = '';\n  parsed.protocol ??= 'https:';\n\n  return parsed.toString().replace(/\\/$/, '');\n}\n\nconst memoSanitizeUrl = memoize(sanitizeUrl);\n\nfunction getHeadOriginFromConstants(): string | null {\n  // This will require a rebuild in bare-workflow to update.\n  const manifest = Constants.expoConfig;\n\n  const origin =\n    manifest?.extra?.router?.headOrigin ??\n    manifest?.extra?.router?.origin ??\n    manifest?.extra?.router?.generatedOrigin;\n\n  if (!origin) {\n    throwOrAlert(\n      `Expo Head: Add the handoff origin to the Expo Config (requires rebuild). Add the Config Plugin ${protocolWarningString}, where \\`origin\\` is the hosted URL.`\n    );\n    // Fallback value that shouldn't be used for real.\n    return 'https://expo.dev';\n  }\n\n  // Without this, the URL will go to an IP address which is not allowed.\n  if (!origin.match(/^http(s)?:\\/\\//)) {\n    console.warn(\n      `Expo Head: origin \"${origin}\" is missing a \\`https://\\` protocol. ${protocolWarningString}.`\n    );\n  }\n\n  // Return the development URL last so the user gets all production warnings first.\n  return memoSanitizeUrl(origin);\n}\n\nexport function getOriginFromConstants(): string | null {\n  // This will require a rebuild in bare-workflow to update.\n  const manifest = Constants.expoConfig;\n\n  const origin =\n    manifest?.extra?.router?.headOrigin ??\n    manifest?.extra?.router?.origin ??\n    manifest?.extra?.router?.generatedOrigin;\n\n  if (!origin) {\n    throwOrAlert(\n      `Expo RSC: Add the origin to the Expo Config (requires rebuild). Add the Config Plugin ${protocolWarningString}, where \\`origin\\` is the hosted URL.`\n    );\n    // Fallback value that shouldn't be used for real.\n    return 'http://localhost:3000';\n  }\n\n  // Return the development URL last so the user gets all production warnings first.\n  return memoSanitizeUrl(origin);\n}\n\nfunction throwOrAlert(msg: string) {\n  // Production apps fatally crash which is often not helpful.\n  if (\n    // @ts-ignore: process is defined\n    process.env.NODE_ENV === 'production'\n  ) {\n    console.error(msg);\n    alert(msg);\n  } else {\n    throw new Error(msg);\n  }\n}\n\nexport function getStaticUrlFromExpoRouter(pathname: string) {\n  // const host = \"https://expo.io\";\n  // Append the URL we'd find in context\n  return getHeadOriginFromConstants() + pathname;\n}\n"]}