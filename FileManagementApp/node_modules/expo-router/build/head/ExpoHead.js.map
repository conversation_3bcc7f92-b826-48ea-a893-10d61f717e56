{"version": 3, "file": "ExpoHead.js", "sourceRoot": "", "sources": ["../../src/head/ExpoHead.tsx"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAE1B,6DAA6E;AAEtE,MAAM,IAAI,GAEb,CAAC,EAAE,QAAQ,EAAsB,EAAE,EAAE;IACvC,OAAO,CAAC,YAAM,CAAC,CAAC,QAAQ,CAAC,EAAE,YAAM,CAAC,CAAC;AACrC,CAAC,CAAC;AAJW,QAAA,IAAI,QAIf;AAEF,YAAI,CAAC,QAAQ,GAAG,oBAAc,CAAC", "sourcesContent": ["import React from 'react';\n\nimport { Helmet, HelmetProvider } from '../../vendor/react-helmet-async/lib';\n\nexport const Head: React.FC<{ children?: React.ReactNode }> & {\n  Provider: typeof HelmetProvider;\n} = ({ children }: { children?: any }) => {\n  return <Helmet>{children}</Helmet>;\n};\n\nHead.Provider = HelmetProvider;\n"]}