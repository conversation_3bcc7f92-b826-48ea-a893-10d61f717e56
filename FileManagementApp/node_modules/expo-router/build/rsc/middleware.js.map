{"version": 3, "file": "middleware.js", "sourceRoot": "", "sources": ["../../src/rsc/middleware.ts"], "names": [], "mappings": ";;;;;AA8EA,8DAqEC;AAED,wCAeC;AA3JD,oEAAuC;AACvC,0DAA6B;AAE7B,iDAA2C;AAI3C,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,0BAA0B,CAAC,CAAC;AAE3D,kEAAkE;AAClE,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAe,CAAC;AAEhD,SAAS,aAAa,CAAU,GAAG,sBAAgC;IACjE,kGAAkG;IAClG,MAAM,QAAQ,GAAG,mBAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,sBAAsB,CAAC,CAAC;IACjE,OAAO,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACtC,CAAC;AAED,SAAS,mBAAmB,CAAC,QAAgB;IAC3C,0GAA0G;IAC1G,IAAI,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;QACnC,OAAO,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;IACzC,CAAC;IAED,MAAM,OAAO,GAAG,EAAE,CAAC;IAEnB,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACxC,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,uBAAuB,CAC9B,WAAmB,EACnB,QAAgB;IAWhB,MAAM,QAAQ,GAAG,aAAa,QAAQ,qBAAqB,CAAC;IAC5D,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,cAAc,CACrB,WAAmB,EACnB,QAAgB;IAWhB,MAAM,QAAQ,GAAG,aAAa,QAAQ,kBAAkB,CAAC;IACzD,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAC;AACjC,CAAC;AAOM,KAAK,UAAU,yBAAyB,CAC7C,UAAkB,EAClB,OAAkB,EAClB,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAiB;IAE5F,UAAU,CAAC,sBAAsB,GAAG,QAAQ,CAAC;IAC7C,IAAI,MAAM,KAAK,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;IAC1F,CAAC;IAED,MAAM,OAAO,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC9C,OAAO,CAAC,uBAAuB,CAAC,GAAG,OAAO,CAAC;IAE3C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,EAAE,CAAC;IACtC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC7B,SAAS,EAAE,wBAAS,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS;QACzD,QAAQ,EAAE,wBAAS,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ;KACxD,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACzD,MAAM,cAAc,GAAG,uBAAuB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACrE,OAAO,IAAA,wBAAS,EACd;QACE,IAAI,EAAE,IAAI,IAAI,SAAS;QACvB,OAAO;QACP,MAAM;QACN,KAAK;QACL,WAAW;QACX,WAAW,EAAE,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC;KAC/C,EACD;QACE,WAAW,EAAE,IAAI;QAEjB,kBAAkB,CAAC,IAAY,EAAE,QAAiB;YAChD,KAAK,CAAC,oBAAoB,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAEhD,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,CAAC,CAAC,IAAI,IAAI,cAAc,CAAC,EAAE,CAAC;oBAC9B,MAAM,IAAI,KAAK,CACb,kDAAkD,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,CAC5F,CAAC;gBACJ,CAAC;gBAED,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;gBACzC,OAAO;oBACL,EAAE;oBACF,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;iBAC7B,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,CAAC,IAAI,IAAI,WAAW,CAAC,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,wCAAwC,IAAI,EAAE,CAAC,CAAC;YAClE,CAAC;YAED,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YACtC,OAAO;gBACL,EAAE;gBACF,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;aAC7B,CAAC;QACJ,CAAC;QACD,KAAK,CAAC,mBAAmB,CAAC,IAAI;YAC5B,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;YACnC,4FAA4F;YAC5F,OAAO,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,EAAE,OAAQ;KAClB,CACF,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,cAAc,CAClC,UAAkB,EAClB,IAAmB;IAEnB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC/B,OAAO,yBAAyB,CAC9B,UAAU,EACV;QACE,MAAM,EAAE,GAAG,EAAE;YACX,4FAA4F;YAC5F,OAAO,aAAa,CAAC,aAAa,QAAQ,YAAY,CAAC,CAAC;QAC1D,CAAC;KACF,EACD,IAAI,CACL,CAAC;AACJ,CAAC", "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n// This module is bundled with Metro in web/react-server mode and redirects to platform specific renderers.\nimport type { RenderRscArgs } from '@expo/server/build/middleware/rsc';\nimport Constants from 'expo-constants';\nimport path from 'node:path';\n\nimport { renderRsc } from './rsc-renderer';\n\ndeclare const $$require_external: typeof require;\n\nconst debug = require('debug')('expo:server:rsc-renderer');\n\n// Tracking the implementation in expo/cli's MetroBundlerDevServer\nconst rscRenderContext = new Map<string, any>();\n\nfunction serverRequire<T = any>(...targetOutputModulePath: string[]): T {\n  // NOTE(@kitten): This `__dirname` will be located in the output file system, e.g. `dist/server/*`\n  const filePath = path.join(__dirname, ...targetOutputModulePath);\n  return $$require_external(filePath);\n}\n\nfunction getRscRenderContext(platform: string) {\n  // NOTE(EvanBacon): We memoize this now that there's a persistent server storage cache for Server Actions.\n  if (rscRenderContext.has(platform)) {\n    return rscRenderContext.get(platform)!;\n  }\n\n  const context = {};\n\n  rscRenderContext.set(platform, context);\n  return context;\n}\n\nfunction getServerActionManifest(\n  _distFolder: string,\n  platform: string\n): Record<\n  // Input ID\n  string,\n  [\n    // Metro ID\n    string,\n    // Chunk location.\n    string,\n  ]\n> {\n  const filePath = `../../rsc/${platform}/action-manifest.js`;\n  return serverRequire(filePath);\n}\n\nfunction getSSRManifest(\n  _distFolder: string,\n  platform: string\n): Record<\n  // Input ID\n  string,\n  [\n    // Metro ID\n    string,\n    // Chunk location.\n    string,\n  ]\n> {\n  const filePath = `../../rsc/${platform}/ssr-manifest.js`;\n  return serverRequire(filePath);\n}\n\n// The import map allows us to use external modules from different bundling contexts.\ntype ImportMap = {\n  router: () => Promise<typeof import('./router/expo-definedRouter')>;\n};\n\nexport async function renderRscWithImportsAsync(\n  distFolder: string,\n  imports: ImportMap,\n  { body, platform, searchParams, config, method, input, contentType, headers }: RenderRscArgs\n): Promise<ReadableStream<any>> {\n  globalThis.__expo_platform_header = platform;\n  if (method === 'POST' && !body) {\n    throw new Error('Server request must be provided when method is POST (server actions)');\n  }\n\n  const context = getRscRenderContext(platform);\n  context['__expo_requestHeaders'] = headers;\n\n  const router = await imports.router();\n  const entries = router.default({\n    redirects: Constants.expoConfig?.extra?.router?.redirects,\n    rewrites: Constants.expoConfig?.extra?.router?.rewrites,\n  });\n\n  const ssrManifest = getSSRManifest(distFolder, platform);\n  const actionManifest = getServerActionManifest(distFolder, platform);\n  return renderRsc(\n    {\n      body: body ?? undefined,\n      context,\n      config,\n      input,\n      contentType,\n      decodedBody: searchParams.get('x-expo-params'),\n    },\n    {\n      isExporting: true,\n\n      resolveClientEntry(file: string, isServer: boolean) {\n        debug('resolveClientEntry', file, { isServer });\n\n        if (isServer) {\n          if (!(file in actionManifest)) {\n            throw new Error(\n              `Could not find file in server action manifest: ${file}. ${JSON.stringify(actionManifest)}`\n            );\n          }\n\n          const [id, chunk] = actionManifest[file];\n          return {\n            id,\n            chunks: chunk ? [chunk] : [],\n          };\n        }\n\n        if (!(file in ssrManifest)) {\n          throw new Error(`Could not find file in SSR manifest: ${file}`);\n        }\n\n        const [id, chunk] = ssrManifest[file];\n        return {\n          id,\n          chunks: chunk ? [chunk] : [],\n        };\n      },\n      async loadServerModuleRsc(file) {\n        debug('loadServerModuleRsc', file);\n        // NOTE(@kitten): [WORKAROUND] Assumes __dirname is at `dist/server/_expo/functions/_flight`\n        return serverRequire('../../../', file);\n      },\n\n      entries: entries!,\n    }\n  );\n}\n\nexport async function renderRscAsync(\n  distFolder: string,\n  args: RenderRscArgs\n): Promise<ReadableStream<any>> {\n  const platform = args.platform;\n  return renderRscWithImportsAsync(\n    distFolder,\n    {\n      router: () => {\n        // NOTE(@kitten): [WORKAROUND] Assumes __dirname is at `dist/server/_expo/functions/_flight`\n        return serverRequire(`../../rsc/${platform}/router.js`);\n      },\n    },\n    args\n  );\n}\n"]}