{"version": 3, "file": "exports.js", "sourceRoot": "", "sources": ["../../src/rsc/exports.tsx"], "names": [], "mappings": ";;;AAMA,kCAGC;AAGD,oDAGC;AAED,sDAGC;AAED,oBAEC;AAED,sBAGC;AAED,oBAGC;AAED,8BAEC;AAKD,4BAIC;AAED,4BAEC;AAED,wCAEC;AAED,sCAEC;AAED,8CAEC;AACD,8DAEC;AAED,kCAEC;AACD,8CAEC;AACD,wDAEC;AACD,sDAEC;AA9ED,4CAAqD;AAgFtB,0FAhFtB,2BAAkB,OAgFa;AA/ExC,wCAAyC;AAEzC,0CAAuC;AAA9B,8FAAA,IAAI,OAAA;AAEb,SAAgB,WAAW;IACzB,MAAM,MAAM,GAAG,IAAA,2BAAkB,GAAE,CAAC;IACpC,OAAO,MAAM,CAAC,IAAI,CAAC;AACrB,CAAC;AAED,2DAA2D;AAC3D,SAAgB,oBAAoB;IAClC,MAAM,MAAM,GAAG,IAAA,2BAAkB,GAAE,CAAC;IACpC,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAC9E,CAAC;AAED,SAAgB,qBAAqB;IACnC,MAAM,MAAM,GAAG,IAAA,2BAAkB,GAAE,CAAC;IACpC,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAC9E,CAAC;AAED,SAAgB,IAAI;IAClB,OAAO,CAAC,eAAQ,CAAC,AAAD,EAAG,CAAC;AACtB,CAAC;AAED,SAAgB,KAAK;IACnB,OAAO,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;IACxE,OAAO,CAAC,eAAQ,CAAC,AAAD,EAAG,CAAC;AACtB,CAAC;AAED,SAAgB,IAAI;IAClB,OAAO,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;IACvE,OAAO,CAAC,eAAQ,CAAC,AAAD,EAAG,CAAC;AACtB,CAAC;AAED,SAAgB,SAAS;IACvB,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;AACjF,CAAC;AAED;;GAEG;AACH,SAAgB,QAAQ,CAAC,EAAE,IAAI,EAAkB;IAC/C,MAAM,MAAM,GAAG,IAAA,2BAAkB,GAAE,CAAC;IACpC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrB,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,QAAQ;IACtB,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;AAChF,CAAC;AAED,SAAgB,cAAc;IAC5B,OAAO,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;AACnF,CAAC;AAED,SAAgB,aAAa;IAC3B,OAAO,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;AAClF,CAAC;AAED,SAAgB,iBAAiB;IAC/B,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;AACzF,CAAC;AACD,SAAgB,yBAAyB;IACvC,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;AACjG,CAAC;AAED,SAAgB,WAAW;IACzB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;AACnF,CAAC;AACD,SAAgB,iBAAiB;IAC/B,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;AACzF,CAAC;AACD,SAAgB,sBAAsB;IACpC,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;AAC9F,CAAC;AACD,SAAgB,qBAAqB;IACnC,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;AAC7F,CAAC;AAID,oBAAoB;AACpB,gDAA+C;AAAtC,sGAAA,SAAS,OAAA;AAElB,wDAAuD;AAA9C,8GAAA,aAAa,OAAA;AAET,QAAA,MAAM,GAAG,IAAI,KAAK,CAC7B,EAAE,EACF;IACE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ;QACxB,MAAM,IAAI,KAAK,CACb,gGAAgG,CACjG,CAAC;IACJ,CAAC;CACF,CACF,CAAC;AAEF,QAAQ;AACR,0CAA0C", "sourcesContent": ["import type { Href } from '../types';\nimport { useRouter_UNSTABLE } from './router/client';\nimport { Children } from './router/host';\n\nexport { Link } from './router/client';\n\nexport function usePathname() {\n  const router = useRouter_UNSTABLE();\n  return router.path;\n}\n\n// TODO: This doesn't work the same as the classic version.\nexport function useLocalSearchParams() {\n  const router = useRouter_UNSTABLE();\n  return Object.fromEntries([...new URLSearchParams(router.query).entries()]);\n}\n\nexport function useGlobalSearchParams() {\n  const router = useRouter_UNSTABLE();\n  return Object.fromEntries([...new URLSearchParams(router.query).entries()]);\n}\n\nexport function Slot() {\n  return <Children />;\n}\n\nexport function Stack() {\n  console.warn('Stack is not implemented in React Server Components yet');\n  return <Children />;\n}\n\nexport function Tabs() {\n  console.warn('Tabs is not implemented in React Server Components yet');\n  return <Children />;\n}\n\nexport function Navigator() {\n  throw new Error('Navigator is not implemented in React Server Components yet');\n}\n\n/**\n * Redirects to the `href` as soon as the component is mounted.\n */\nexport function Redirect({ href }: { href: Href }) {\n  const router = useRouter_UNSTABLE();\n  router.replace(href);\n  return null;\n}\n\nexport function ExpoRoot() {\n  throw new Error('ExpoRoot is not implemented in React Server Components yet');\n}\n\nexport function useFocusEffect() {\n  console.warn('useFocusEffect is not implemented in React Server Components yet');\n}\n\nexport function useNavigation() {\n  console.warn('useNavigation is not implemented in React Server Components yet');\n}\n\nexport function withLayoutContext() {\n  throw new Error('withLayoutContext is not implemented in React Server Components yet');\n}\nexport function useNavigationContainerRef() {\n  throw new Error('useNavigationContainerRef is not implemented in React Server Components yet');\n}\n\nexport function useSegments() {\n  throw new Error('useSegments is not implemented in React Server Components yet');\n}\nexport function useRootNavigation() {\n  throw new Error('useRootNavigation is not implemented in React Server Components yet');\n}\nexport function useRootNavigationState() {\n  throw new Error('useRootNavigationState is not implemented in React Server Components yet');\n}\nexport function useUnstableGlobalHref() {\n  throw new Error('useUnstableGlobalHref is not implemented in React Server Components yet');\n}\n\nexport { useRouter_UNSTABLE as useRouter };\n\n// Expo Router Views\nexport { Unmatched } from '../views/Unmatched';\nexport { ErrorBoundaryProps } from '../views/Try';\nexport { ErrorBoundary } from '../views/ErrorBoundary';\n\nexport const router = new Proxy(\n  {},\n  {\n    get(target, prop, receiver) {\n      throw new Error(\n        `The router object is not available in React Server Components. Use the useRouter hook instead.`\n      );\n    },\n  }\n);\n\n// TODO:\n// export { Redirect } from './link/Link';\n"]}