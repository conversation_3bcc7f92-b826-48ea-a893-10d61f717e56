{"version": 3, "file": "create-pages.d.ts", "sourceRoot": "", "sources": ["../../../src/rsc/router/create-pages.ts"], "names": [], "mappings": "AACA;;;;;;;;GAQG;AAGH,OAAO,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAE1D,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAI9C,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAmBhD;;;;;;;;;GASG;AACH,KAAK,KAAK,CAAC,GAAG,SAAS,MAAM,EAAE,GAAG,SAAS,MAAM,GAAG,MAAM,IAAI,MAAM,SAAS,GAAG,GAC5E,MAAM,EAAE,GACR,EAAE,SAAS,GAAG,GACZ,EAAE,GACF,GAAG,SAAS,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,EAAE,GACtC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GACrB,CAAC,GAAG,CAAC,CAAC;AAEd,sDAAsD;AACtD,KAAK,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,SAAS,IAAI,GAAG,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;AAC/F;;GAEG;AACH,MAAM,MAAM,qBAAqB,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,GACrE,eAAe,CAAC,CAAC,CAAC,SAAS,IAAI,GAC7B,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,GAC9B,KAAK,GACP,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE,GACrB,eAAe,CAAC,CAAC,CAAC,GAClB,KAAK,CAAC;AACZ,yDAAyD;AACzD,MAAM,MAAM,aAAa,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC,EAAE,GAC3E,IAAI,GACJ,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,GAChC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,GACzB,CAAC,SAAS,KAAK,CAAC,GAAG,GACjB,IAAI,GACJ,KAAK,CAAC;AAEd,MAAM,MAAM,iBAAiB,CAAC,CAAC,IAAI,CAAC,SAAS,QAAQ,MAAM,KAAK,MAAM,EAAE,GACpE,IAAI,GACJ,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,GAChC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,GAC1B,CAAC,SAAS,QAAQ,MAAM,GAAG,GACzB,IAAI,GACJ,KAAK,CAAC;AAEd,MAAM,MAAM,YAAY,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,IAC1C,qBAAqB,CAAC,CAAC,CAAC,SAAS,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC;AAEjG,KAAK,SAAS,CACZ,KAAK,SAAS,MAAM,EACpB,UAAU,SAAS,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EAC/C,MAAM,SAAS,MAAM,EAAE,GAAG,EAAE,IAC1B,UAAU,SAAS,EAAE,GACrB,MAAM,GACN,UAAU,SAAS,CAAC,GAAG,MAAM,SAAS,EAAE,EAAE,GAAG,MAAM,IAAI,CAAC,GACtD,IAAI,SAAS,MAAM,EAAE,GACnB,SAAS,SAAS,IAAI,MAAM,IAAI,GAAG,GACjC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,GAAG,MAAM,EAAE,IAAI,CAAC,CAAC,GACzC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,GAChC,KAAK,GACP,MAAM,CAAC;AAEb,MAAM,MAAM,QAAQ,CAAC,KAAK,SAAS,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;AAE9D,MAAM,MAAM,yBAAyB,CACnC,CAAC,SAAS,MAAM,EAChB,KAAK,SAAS,OAAO,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,EACrC,MAAM,SAAS,MAAM,EAAE,GAAG,EAAE,IAC1B,KAAK,SAAS,EAAE,GAChB,MAAM,GACN,KAAK,SAAS,CAAC,MAAM,CAAC,EAAE,GAAG,MAAM,IAAI,CAAC,GACpC,yBAAyB,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,MAAM,EAAE,MAAM,CAAC,CAAC,GACvD,KAAK,CAAC;AAEZ,KAAK,oBAAoB,CAAC,CAAC,SAAS,MAAM,IACxC,iBAAiB,CAAC,CAAC,CAAC,SAAS,IAAI,GAC7B,MAAM,EAAE,GAAG,MAAM,EAAE,EAAE,GACrB,yBAAyB,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,GAC3C,MAAM,EAAE,GACR,yBAAyB,CAAC,CAAC,CAAC,EAAE,CAAC;AAEvC,MAAM,MAAM,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,GAC1C,CAAC,GACD,qBAAqB,CAAC,CAAC,CAAC,SAAS,IAAI,GACnC,aAAa,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,IAAI,GACnC,KAAK,GACL,CAAC,GACH,KAAK,CAAC;AAEZ,KAAK,mBAAmB,CAAC,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,GAAG,GACtD,CAAC,GACD,qBAAqB,CAAC,CAAC,CAAC,SAAS,IAAI,GACnC,CAAC,GACD,KAAK,CAAC;AAEZ,MAAM,MAAM,gBAAgB,CAC1B,IAAI,EACJ,OAAO,SAAS,MAAM,EACtB,WAAW,SAAS,MAAM,IACxB,YAAY,CAAC,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW,EAAE,CAAC,CAAC;AAEtD,MAAM,MAAM,UAAU,GAAG,CAAC,IAAI,SAAS,MAAM,EAAE,OAAO,SAAS,MAAM,EAAE,WAAW,SAAS,MAAM,EAC/F,IAAI,EAAE,CACF;IACE,MAAM,EAAE,QAAQ,CAAC;IACjB,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,CAAC;IAC5B,SAAS,EAAE,iBAAiB,CAAC,UAAU,CAAC,CAAC;CAC1C,GACD;IACE,MAAM,EAAE,QAAQ,CAAC;IACjB,IAAI,EAAE,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAChC,WAAW,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACxC,SAAS,EAAE,iBAAiB,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;CACpE,GACD;IACE,MAAM,EAAE,SAAS,CAAC;IAClB,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,CAAC;IAC5B,SAAS,EAAE,iBAAiB,CAAC,UAAU,CAAC,CAAC;CAC1C,GACD;IACE,MAAM,EAAE,SAAS,CAAC;IAClB,IAAI,EAAE,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IACnD,SAAS,EAAE,iBAAiB,CAC1B,UAAU,GAAG,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC,CACrE,CAAC;CACH,CACJ,GAAG;IAAE,mBAAmB,CAAC,EAAE,OAAO,CAAA;CAAE,KAClC,IAAI,CAAC;AAEV,MAAM,MAAM,YAAY,GAAG,CAAC,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE;IACpD,MAAM,EAAE,QAAQ,GAAG,SAAS,CAAC;IAC7B,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;IACzB,SAAS,EAAE,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,GAAG;QAAE,QAAQ,EAAE,SAAS,CAAA;KAAE,CAAC,CAAC;CAC1F,KAAK,IAAI,CAAC;AAEX,wBAAgB,WAAW,CACzB,EAAE,EAAE,CACF,GAAG,EAAE;IACH,UAAU,EAAE,UAAU,CAAC;IACvB,YAAY,EAAE,YAAY,CAAC;IAC3B,qBAAqB,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC;CAC9D,EACD,IAAI,EAAE;IACJ,oBAAoB,EAAE,WAAW,GAAG,SAAS,CAAC;CAC/C,KACE,OAAO,CAAC,IAAI,CAAC;;;;EA+NnB"}