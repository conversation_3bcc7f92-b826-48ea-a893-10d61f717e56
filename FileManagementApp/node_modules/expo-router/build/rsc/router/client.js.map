{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../../src/rsc/router/client.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;AAEH,YAAY,CAAC;AAVb;;;;;;;;GAQG;;;AAqRH,gDAgGC;AAoED,wBAUC;AASD,oCAgBC;AAxdD,qDAAyD;AACzD,iCAae;AAUf,+CAAoC;AAEpC,2CAA8E;AAE9E,uCAAgE;AAIhE,0CAA8C;AAC9C,0DAA4E;AAG5E,MAAM,kBAAkB,GAAG,CAAC,IAAY,EAAE,EAAE;IAC1C,KAAK,MAAM,MAAM,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC,EAAE,CAAC;QAC1C,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;QAC9C,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,GAAQ,EAAc,EAAE;IAC1C,IAAK,UAAkB,CAAC,mBAAmB,EAAE,CAAC;QAC5C,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IAC/C,CAAC;IACD,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC;IAC7C,IAAI,YAAY,CAAC,GAAG,CAAC,0BAAc,CAAC,EAAE,CAAC;QACrC,OAAO,CAAC,IAAI,CAAC,qBAAqB,0BAAc,eAAe,CAAC,CAAC;IACnE,CAAC;IACD,OAAO;QACL,IAAI,EAAE,kBAAkB,CAAC,QAAQ,CAAC;QAClC,KAAK,EAAE,YAAY,CAAC,QAAQ,EAAE;QAC9B,IAAI;KACL,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,OAAO,GAAG,GAAG,EAAE,CACnB,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK;IAC3B,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI;IACtB,CAAC,CAAC,gEAAgE;QAChE,wBAAwB,CAAC;AAY/B,MAAM,eAAe,GAAG,CAAC,CAAa,EAAE,CAAa,EAAE,EAAE;IACvD,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,IAAA,qBAAa,EAIzB,IAAI,CAAC,CAAC;AAEhB,MAAM,WAAW,GAAG,CAAC,EAAE,UAAU,EAA8B,EAAE,EAAE;IACjE,MAAM,OAAO,GAAG,IAAA,oBAAU,GAAE,CAAC;IAE7B,MAAM,eAAe,GAAG,IAAA,cAAM,EAAa,IAAI,CAAC,CAAC;IACjD,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QAC7B,eAAe,CAAC,OAAO,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IACD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,IAAA,gBAAQ,EAAC,GAAG,EAAE,CAAC,CAAC;QACxC,4DAA4D;QAC5D,4DAA4D;QAC5D,6CAA6C;QAC7C,kFAAkF;QAClF,GAAG,eAAe,CAAC,OAAQ;QAC3B,IAAI,EAAE,EAAE;KACT,CAAC,CAAC,CAAC;IAEJ,0DAA0D;IAC1D,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,eAAe,CAAC,OAAQ,CAAC;QAC9C,QAAQ,CAAC,CAAC,IAAI,EAAE,EAAE;YAChB,IACE,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI;gBAC/B,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC,KAAK;gBACjC,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,EAC/B,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,YAAY,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,YAAY,GAAG,IAAA,2BAAe,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAEjD,gCAAgC;IAChC,gDAAgD;IAChD,4CAA4C;IAC5C,+BAA+B;IAC/B,4DAA4D;IAC5D,KAAK;IACL,6DAA6D;IAE7D,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,IAAA,gBAAQ,EAA6B,GAAG,EAAE;QACpE,OAAO,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;IACH,MAAM,SAAS,GAAG,IAAA,cAAM,EAAC,MAAM,CAAC,CAAC;IACjC,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,SAAS,CAAC,OAAO,GAAG,MAAM,CAAC;IAC7B,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,MAAM,WAAW,GAAgB,IAAA,mBAAW,EAC1C,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;QACjB,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;QAClD,IAAA,uBAAe,EAAC,GAAG,EAAE;YACnB,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QACH,MAAM,YAAY,GAAG,IAAA,2BAAe,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACjD,IACE,UAAU;YACV,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE;gBACxB,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACxC,OAAO,SAAS,IAAI,eAAe,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACxD,CAAC,CAAC,EACF,CAAC;YACD,OAAO,CAAC,uBAAuB;QACjC,CAAC;QACD,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YAClD,OAAO,CAAC,wBAAwB;QAClC,CAAC;QACD,MAAM,KAAK,GAAG,IAAA,0BAAc,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAC/D,CAAC;QACD,IAAA,uBAAe,EAAC,GAAG,EAAE;YACnB,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBACnB,GAAG,IAAI;gBACP,GAAG,MAAM,CAAC,WAAW,CACnB,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CACvE;aACF,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC,EACD,CAAC,OAAO,EAAE,UAAU,CAAC,CACtB,CAAC;IAEF,MAAM,aAAa,GAAkB,IAAA,mBAAW,EAC9C,CAAC,KAAK,EAAE,EAAE;QACR,MAAM,YAAY,GAAG,IAAA,2BAAe,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACjD,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YAClD,OAAO,CAAC,uBAAuB;QACjC,CAAC;QACD,MAAM,KAAK,GAAG,IAAA,0BAAc,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzC,IAAA,qBAAW,EAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAChE,UAAkB,CAAC,wBAAwB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC,EACD,CAAC,UAAU,CAAC,CACb,CAAC;IAEF,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,MAAM,QAAQ,GAAG,GAAG,EAAE;YACpB,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC7C,WAAW,CAAC,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;QAC3C,CAAC,CAAC;QACF,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAC5B,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC9C,OAAO,GAAG,EAAE;gBACV,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACnD,CAAC,CAAC;QACJ,CAAC;QACD,OAAO,GAAG,EAAE,GAAE,CAAC,CAAC;IAClB,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElB,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,MAAM,QAAQ,GAAG,CAAC,QAAgB,EAAE,kBAA0B,EAAE,EAAE;YAChE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/B,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACxB,GAAG,CAAC,MAAM,GAAG,kBAAkB,CAAC;YAChC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC;YACd,UAAU,EAAE,CAAC,SAAS,CACpB;gBACE,GAAG,UAAU,EAAE,CAAC,KAAK;gBACrB,aAAa,EAAE,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,QAAQ;aACzD,EACD,EAAE,EACF,GAAG,CACJ,CAAC;YACF,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;QACtD,CAAC,CAAC;QACF,2CAA2C;QAC3C,MAAM,SAAS,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;QAChD,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxB,OAAO,GAAG,EAAE;YACV,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC;IAE9B,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC;QACjC,MAAM,EAAE,KAAK,EAAE,GAAG,UAAU,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,IAAI,IAAI,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,MAAM,CAAC,QAAQ,CAAC;gBACd,IAAI,EAAE,CAAC;gBACP,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACvE,QAAQ,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM;aACpD,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,eAAe;YACf,mDAAmD;QACrD,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,QAAQ,GAAG,YAAY,CAAC,WAAW,CACvC,CAAC,GAAc,EAAE,EAAE,EAAE,EAAE,CACrB,IAAA,qBAAa,EAAC,UAAU,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE,GAAG,CAAC,EACrF,IAAI,CACL,CAAC;IAEF,OAAO,IAAA,qBAAa,EAClB,aAAa,CAAC,QAAQ,EACtB,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,EAAE,EAChD,QAAQ,CACT,CAAC;AACJ,CAAC,CAAC;AAEF,SAAS,UAAU;IACjB,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;QAClC,OAAO,MAAM,CAAC,OAAO,CAAC;IACxB,CAAC;IACD,cAAc;IACd,OAAO;QACL,SAAS,EAAE,GAAG,EAAE,GAAE,CAAC;QACnB,YAAY,EAAE,GAAG,EAAE,GAAE,CAAC;QACtB,IAAI,EAAE,GAAG,EAAE,GAAE,CAAC;QACd,OAAO,EAAE,GAAG,EAAE,GAAE,CAAC;QACjB,KAAK,EAAE,EAAE;KACV,CAAC;AACJ,CAAC;AAED,SAAgB,kBAAkB;IAKhC,MAAM,MAAM,GAAG,IAAA,WAAG,EAAC,aAAa,CAAC,CAAC;IAClC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACpC,CAAC;IACD,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,MAAM,CAAC;IACrD,MAAM,IAAI,GAAkC,IAAA,mBAAW,EACrD,CAAC,IAAU,EAAE,OAA2B,EAAE,EAAE;QAC1C,IAAI,OAAO,EAAE,CAAC;YACZ,iCAAiC;YACjC,OAAO,CAAC,IAAI,CACV,+EAA+E,CAChF,CAAC;QACJ,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;QAClD,UAAU,EAAE,CAAC,SAAS,CACpB;YACE,GAAG,UAAU,EAAE,CAAC,KAAK;YACrB,aAAa,EAAE,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,QAAQ;SACzD,EACD,EAAE,EACF,GAAG,CACJ,CAAC;QACF,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/B,CAAC,EACD,CAAC,WAAW,CAAC,CACd,CAAC;IACF,MAAM,OAAO,GAAqC,IAAA,mBAAW,EAC3D,CAAC,IAAU,EAAE,OAA2B,EAAE,EAAE;QAC1C,IAAI,OAAO,EAAE,CAAC;YACZ,iCAAiC;YACjC,OAAO,CAAC,IAAI,CACV,kFAAkF,CACnF,CAAC;QACJ,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;QAClD,UAAU,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;QACvD,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/B,CAAC,EACD,CAAC,WAAW,CAAC,CACd,CAAC;IACF,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE;QAC9B,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/B,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAClB,MAAM,IAAI,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE;QAC5B,yBAAyB;QACzB,UAAU,EAAE,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,MAAM,OAAO,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE;QAC/B,yBAAyB;QACzB,UAAU,EAAE,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,MAAM,QAAQ,GAAG,IAAA,mBAAW,EAC1B,CAAC,IAAU,EAAE,EAAE;QACb,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;QAClD,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IACjC,CAAC,EACD,CAAC,aAAa,CAAC,CAChB,CAAC;IACF,OAAO;QACL,GAAG,KAAK;QACR,UAAU;YACR,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;QACzF,CAAC;QACD,SAAS;YACP,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;QACxF,CAAC;QACD,OAAO;YACL,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;QACtF,CAAC;QACD,SAAS;YACP,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;QACxF,CAAC;QACD,UAAU;YACR,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;QACzF,CAAC;QACD,SAAS;YACP,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;QACxF,CAAC;QAED,qDAAqD;QACrD,QAAQ,EAAE,IAAI;QACd,IAAI;QACJ,OAAO;QACP,MAAM;QACN,IAAI;QACJ,OAAO;QACP,QAAQ;KACT,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,GAAG,CAAC,EAClB,KAAK,EACL,UAAU,EACV,SAAS,EACT,EAAE,EACF,QAAQ,EACR,QAAQ,GAQT,EAAE,EAAE;IACH,yDAAyD;IACzD,sCAAsC;IACtC,0EAA0E;IAC1E,4BAA4B;IAC5B,KAAK;IACL,OAAO,IAAA,qBAAa,EAAC,cAAI,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAClB,UAAkC,EAClC,YAA+B,EAC/B,KAAiB,EACjB,MAAkC,EACxB,EAAE;IACZ,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;IAC3D,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE;QAChC,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QAC7B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;QACD,MAAM,WAAW,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;QACtC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,WAAW,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,WAAW,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAiBF,MAAM,mBAAmB,GAAe,EAAE,CAAC;AAE3C,SAAgB,MAAM,CAAC,EAAE,UAAU,GAAG,mBAAmB,EAAE;IACzD,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAC7C,MAAM,YAAY,GAAG,IAAA,0BAAc,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAChD,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;IAC7D,MAAM,oBAAoB,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;IACtC,OAAO,IAAA,qBAAa,EAClB,cAAwE,EACxE,EAAE,YAAY,EAAE,aAAa,EAAE,oBAAoB,EAAE,EACrD,IAAA,qBAAa,EAAC,WAAW,EAAE,EAAE,UAAU,EAAE,CAAC,CAC3C,CAAC;AACJ,CAAC;AAED,MAAM,oBAAoB,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,GAAG,EAAE;IAClD,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,uBAAuB,CAAC,CAAC;AAClD,CAAC,CAAC;AACF;;;GAGG;AACH,SAAgB,YAAY,CAAC,EAAE,QAAQ,EAAE,KAAK,EAA8C;IAC1F,OAAO,IAAA,qBAAa,EAClB,gBAAQ,EACR,IAAI,EACJ,IAAA,qBAAa,EACX,aAAa,CAAC,QAAQ,EACtB;QACE,KAAK,EAAE;YACL,KAAK;YACL,WAAW,EAAE,oBAAoB,CAAC,aAAa,CAAC;YAChD,aAAa,EAAE,oBAAoB,CAAC,eAAe,CAAC;SACrD;KACF,EACD,QAAQ,CACT,CACF,CAAC;AACJ,CAAC;AAWY,QAAA,IAAI,GAAG,IAAA,kBAAU,EAAC,cAAc,CAA6B,CAAC;AAE3E,YAAI,CAAC,WAAW,GAAG,kBAAW,CAAC;AAE/B,SAAS,cAAc,CACrB,EACE,IAAI,EACJ,OAAO,EACP,IAAI;AACJ,yDAAyD;AACzD,mBAAmB,EACnB,OAAO,EACP,GAAG,EACH,MAAM,EACN,QAAQ;AACR,aAAa;AACb,cAAc;AACd,4BAA4B;AAC5B,2BAA2B;AAC3B,QAAQ,EACR,GAAG,KAAK,EACE,EACZ,GAAuB;IAEvB,qDAAqD;IACrD,MAAM,KAAK,GAAG,IAAA,kCAAmB,EAAC,KAAK,CAAC,CAAC;IAEzC,+GAA+G;IAC/G,MAAM,SAAS,GAAG,IAAA,2BAAY,EAAC,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;IAEnE,MAAM,YAAY,GAAG,IAAA,eAAO,EAAC,GAAG,EAAE;QAChC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QACD,OAAO,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC;IAC3B,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAEX,MAAM,MAAM,GAAG,IAAA,WAAG,EAAC,aAAa,CAAC,CAAC;IAClC,MAAM,WAAW,GAAG,MAAM;QACxB,CAAC,CAAC,MAAM,CAAC,WAAW;QACpB,CAAC,CAAC,GAAG,EAAE;YACH,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACpC,CAAC,CAAC;IACN,MAAM,aAAa,GAAG,MAAM;QAC1B,CAAC,CAAC,MAAM,CAAC,aAAa;QACtB,CAAC,CAAC,GAAG,EAAE;YACH,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACpC,CAAC,CAAC;IACN,4DAA4D;IAC5D,MAAM,CAAC,EAAE,eAAe,CAAC,GAAG,IAAA,qBAAa,GAAE,CAAC;IAC5C,kDAAkD;IAElD,oBAAoB;IACpB,mFAAmF;IACnF,iDAAiD;IACjD,uBAAuB;IACvB,uCAAuC;IACvC,wCAAwC;IACxC,4DAA4D;IAC5D,sDAAsD;IACtD,+CAA+C;IAC/C,6CAA6C;IAC7C,gBAAgB;IAChB,cAAc;IACd,cAAc;IACd,WAAW;IACX,2BAA2B;IAC3B,SAAS;IAET,qCAAqC;IAErC,qBAAqB;IACrB,+BAA+B;IAC/B,SAAS;IACT,MAAM;IACN,qBAAqB;IACrB,uDAAuD;IAEvD,MAAM,OAAO,GAAG,CAAC,KAAoC,EAAE,EAAE;QACvD,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;QAC7C,kDAAkD;QAClD,gCAAgC;QAChC,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QAC9B,aAAa,CAAC,KAAK,CAAC,CAAC;QACrB,eAAe,CAAC,GAAG,EAAE;YACnB,UAAU,EAAE,CAAC,SAAS,CACpB;gBACE,GAAG,UAAU,EAAE,CAAC,KAAK;gBACrB,aAAa,EAAE,GAAG,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,QAAQ;aACzD,EACD,EAAE,EACF,GAAG,CACJ,CAAC;YACF,WAAW,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;QACH,IAAI;QACJ,KAAK,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC,CAAC;IACF,gDAAgD;IAChD,kDAAkD;IAClD,4CAA4C;IAC5C,sCAAsC;IACtC,yCAAyC;IACzC,gCAAgC;IAChC,UAAU;IACV,qCAAqC;IACrC,QAAQ;IACR,0BAA0B;IAE1B,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,iBAAS,CAAC,CAAC,CAAC,mBAAI,CAAC;IAE3C,MAAM,GAAG,GAAG,IAAA,qBAAa;IACvB,mBAAmB;IACnB,OAAO,EACP;QACE,GAAG,SAAS;QACZ,GAAG,KAAK;QACR,KAAK;QACL,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,OAAO;QAChB,gBAAgB;QAChB,GAAG;KACJ,EACD,QAAQ,CACT,CAAC;IACF,4CAA4C;IAC5C,wDAAwD;IACxD,IAAI;IACJ,gDAAgD;IAChD,2DAA2D;IAC3D,IAAI;IACJ,OAAO,GAAG,CAAC;AACb,CAAC", "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n * Copyright © 2024 2023 <PERSON><PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * https://github.com/dai-shi/waku/blob/3d1cc7d714b67b142c847e879c30f0724fc457a7/packages/waku/src/router/client.ts#L1\n */\n\n'use client';\n\nimport { Slot as ReactSlot } from '@radix-ui/react-slot';\nimport {\n  startTransition,\n  useCallback,\n  use,\n  useEffect,\n  useRef,\n  useTransition,\n  createElement,\n  createContext,\n  useState,\n  Fragment,\n  forwardRef,\n  useMemo,\n} from 'react';\nimport type {\n  ComponentProps,\n  FunctionComponent,\n  ReactNode,\n  MutableRefObject,\n  AnchorHTMLAttributes,\n  MouseEvent,\n  ForwardedRef,\n} from 'react';\nimport { Text } from 'react-native';\n\nimport { PARAM_KEY_SKIP, getComponentIds, getInputString } from './common.js';\nimport type { RouteProps } from './common.js';\nimport { prefetchRSC, Root, Slot, useRefetch } from './host.js';\nimport type { NavigationOptions } from '../../global-state/routing.js';\nimport type { Router as ClassicExpoRouterType } from '../../imperative-api';\nimport type { LinkProps as ClassicLinkProps, LinkComponent } from '../../link/Link.js';\nimport { resolveHref } from '../../link/href';\nimport { useInteropClassName, useHrefAttrs } from '../../link/useLinkHooks';\nimport type { Href } from '../../types.js';\n\nconst normalizeRoutePath = (path: string) => {\n  for (const suffix of ['/', '/index.html']) {\n    if (path.endsWith(suffix)) {\n      return path.slice(0, -suffix.length) || '/';\n    }\n  }\n  return path;\n};\n\nconst parseRoute = (url: URL): RouteProps => {\n  if ((globalThis as any).__EXPO_ROUTER_404__) {\n    return { path: '/404', query: '', hash: '' };\n  }\n  const { pathname, searchParams, hash } = url;\n  if (searchParams.has(PARAM_KEY_SKIP)) {\n    console.warn(`The search param \"${PARAM_KEY_SKIP}\" is reserved`);\n  }\n  return {\n    path: normalizeRoutePath(pathname),\n    query: searchParams.toString(),\n    hash,\n  };\n};\nconst getHref = () =>\n  process.env.EXPO_OS === 'web'\n    ? window.location.href\n    : // TODO: This is hardcoded on native to simplify the initial PR.\n      'http://localhost:8081/';\n\ntype ChangeRoute = (\n  route: RouteProps,\n  options?: {\n    checkCache?: boolean;\n    skipRefetch?: boolean;\n  }\n) => void;\n\ntype PrefetchRoute = (route: RouteProps) => void;\n\nconst equalRouteProps = (a: RouteProps, b: RouteProps) => {\n  if (a.path !== b.path) {\n    return false;\n  }\n  if (a.query !== b.query) {\n    return false;\n  }\n  return true;\n};\n\nconst RouterContext = createContext<{\n  route: RouteProps;\n  changeRoute: ChangeRoute;\n  prefetchRoute: PrefetchRoute;\n} | null>(null);\n\nconst InnerRouter = ({ routerData }: { routerData: RouterData }) => {\n  const refetch = useRefetch();\n\n  const initialRouteRef = useRef<RouteProps>(null);\n  if (!initialRouteRef.current) {\n    initialRouteRef.current = parseRoute(new URL(getHref()));\n  }\n  const [route, setRoute] = useState(() => ({\n    // This is the first initialization of the route, and it has\n    // to ignore the hash, because on server side there is none.\n    // Otherwise there will be a hydration error.\n    // The client side route, including the hash, will be updated in the effect below.\n    ...initialRouteRef.current!,\n    hash: '',\n  }));\n\n  // Update the route post-load to include the current hash.\n  useEffect(() => {\n    const initialRoute = initialRouteRef.current!;\n    setRoute((prev) => {\n      if (\n        prev.path === initialRoute.path &&\n        prev.query === initialRoute.query &&\n        prev.hash === initialRoute.hash\n      ) {\n        return prev;\n      }\n      return initialRoute;\n    });\n  }, []);\n\n  const componentIds = getComponentIds(route.path);\n\n  //  const refetchRoute = () => {\n  //   const loc = parseRoute(new URL(getHref()));\n  //   const input = getInputString(loc.path);\n  //   refetch(input, loc.query);\n  //   refetch(input, JSON.stringify({ query: route.query }));\n  // };\n  // globalThis.__EXPO_REFETCH_ROUTE_NO_CACHE__ = refetchRoute;\n\n  const [cached, setCached] = useState<Record<string, RouteProps>>(() => {\n    return Object.fromEntries(componentIds.map((id) => [id, route]));\n  });\n  const cachedRef = useRef(cached);\n  useEffect(() => {\n    cachedRef.current = cached;\n  }, [cached]);\n\n  const changeRoute: ChangeRoute = useCallback(\n    (route, options) => {\n      const { checkCache, skipRefetch } = options || {};\n      startTransition(() => {\n        setRoute(route);\n      });\n      const componentIds = getComponentIds(route.path);\n      if (\n        checkCache &&\n        componentIds.every((id) => {\n          const cachedLoc = cachedRef.current[id];\n          return cachedLoc && equalRouteProps(cachedLoc, route);\n        })\n      ) {\n        return; // everything is cached\n      }\n      const shouldSkip = routerData[0];\n      const skip = getSkipList(shouldSkip, componentIds, route, cachedRef.current);\n      if (componentIds.every((id) => skip.includes(id))) {\n        return; // everything is skipped\n      }\n      const input = getInputString(route.path);\n      if (!skipRefetch) {\n        refetch(input, JSON.stringify({ query: route.query, skip }));\n      }\n      startTransition(() => {\n        setCached((prev) => ({\n          ...prev,\n          ...Object.fromEntries(\n            componentIds.flatMap((id) => (skip.includes(id) ? [] : [[id, route]]))\n          ),\n        }));\n      });\n    },\n    [refetch, routerData]\n  );\n\n  const prefetchRoute: PrefetchRoute = useCallback(\n    (route) => {\n      const componentIds = getComponentIds(route.path);\n      const shouldSkip = routerData[0];\n      const skip = getSkipList(shouldSkip, componentIds, route, cachedRef.current);\n      if (componentIds.every((id) => skip.includes(id))) {\n        return; // everything is cached\n      }\n      const input = getInputString(route.path);\n      prefetchRSC(input, JSON.stringify({ query: route.query, skip }));\n      (globalThis as any).__EXPO_ROUTER_PREFETCH__?.(route.path);\n    },\n    [routerData]\n  );\n\n  useEffect(() => {\n    const callback = () => {\n      const route = parseRoute(new URL(getHref()));\n      changeRoute(route, { checkCache: true });\n    };\n    if (window.addEventListener) {\n      window.addEventListener('popstate', callback);\n      return () => {\n        window.removeEventListener('popstate', callback);\n      };\n    }\n    return () => {};\n  }, [changeRoute]);\n\n  useEffect(() => {\n    const callback = (pathname: string, searchParamsString: string) => {\n      const url = new URL(getHref());\n      url.pathname = pathname;\n      url.search = searchParamsString;\n      url.hash = '';\n      getHistory().pushState(\n        {\n          ...getHistory().state,\n          expo_new_path: url.pathname !== window.location.pathname,\n        },\n        '',\n        url\n      );\n      changeRoute(parseRoute(url), { skipRefetch: true });\n    };\n    // eslint-disable-next-line no-multi-assign\n    const listeners = (routerData[1] ||= new Set());\n    listeners.add(callback);\n    return () => {\n      listeners.delete(callback);\n    };\n  }, [changeRoute, routerData]);\n\n  useEffect(() => {\n    const { hash } = window.location;\n    const { state } = getHistory();\n    const element = hash && document.getElementById(hash.slice(1));\n    if (window.scrollTo) {\n      window.scrollTo({\n        left: 0,\n        top: element ? element.getBoundingClientRect().top + window.scrollY : 0,\n        behavior: state?.expo_new_path ? 'instant' : 'auto',\n      });\n    } else {\n      // TODO: Native\n      // console.log('window.scrollTo is not available');\n    }\n  });\n\n  const children = componentIds.reduceRight(\n    (acc: ReactNode, id) =>\n      createElement(RouterSlot, { route, routerData, cachedRef, id, fallback: acc }, acc),\n    null\n  );\n\n  return createElement(\n    RouterContext.Provider,\n    { value: { route, changeRoute, prefetchRoute } },\n    children\n  );\n};\n\nfunction getHistory() {\n  if (process.env.EXPO_OS === 'web') {\n    return window.history;\n  }\n  // Native shim\n  return {\n    pushState: () => {},\n    replaceState: () => {},\n    back: () => {},\n    forward: () => {},\n    state: {},\n  };\n}\n\nexport function useRouter_UNSTABLE(): ClassicExpoRouterType &\n  RouteProps & {\n    forward: () => void;\n    prefetch: (href: Href) => void;\n  } {\n  const router = use(RouterContext);\n  if (!router) {\n    throw new Error('Missing Router');\n  }\n  const { route, changeRoute, prefetchRoute } = router;\n  const push: ClassicExpoRouterType['push'] = useCallback(\n    (href: Href, options?: NavigationOptions) => {\n      if (options) {\n        // TODO(Bacon): Implement options\n        console.warn(\n          'options prop of router.push() is not supported in React Server Components yet'\n        );\n      }\n\n      const url = new URL(resolveHref(href), getHref());\n      getHistory().pushState(\n        {\n          ...getHistory().state,\n          expo_new_path: url.pathname !== window.location.pathname,\n        },\n        '',\n        url\n      );\n      changeRoute(parseRoute(url));\n    },\n    [changeRoute]\n  );\n  const replace: ClassicExpoRouterType['replace'] = useCallback(\n    (href: Href, options?: NavigationOptions) => {\n      if (options) {\n        // TODO(Bacon): Implement options\n        console.warn(\n          'options prop of router.replace() is not supported in React Server Components yet'\n        );\n      }\n\n      const url = new URL(resolveHref(href), getHref());\n      getHistory().replaceState(getHistory().state, '', url);\n      changeRoute(parseRoute(url));\n    },\n    [changeRoute]\n  );\n  const reload = useCallback(() => {\n    const url = new URL(getHref());\n    changeRoute(parseRoute(url));\n  }, [changeRoute]);\n  const back = useCallback(() => {\n    // FIXME is this correct?\n    getHistory().back();\n  }, []);\n  const forward = useCallback(() => {\n    // FIXME is this correct?\n    getHistory().forward();\n  }, []);\n  const prefetch = useCallback(\n    (href: Href) => {\n      const url = new URL(resolveHref(href), getHref());\n      prefetchRoute(parseRoute(url));\n    },\n    [prefetchRoute]\n  );\n  return {\n    ...route,\n    canDismiss() {\n      throw new Error('router.canDismiss() is not supported in React Server Components yet');\n    },\n    canGoBack() {\n      throw new Error('router.canGoBack() is not supported in React Server Components yet');\n    },\n    dismiss() {\n      throw new Error('router.dismiss() is not supported in React Server Components yet');\n    },\n    dismissTo() {\n      throw new Error('router.dismissTo() is not supported in React Server Components yet');\n    },\n    dismissAll() {\n      throw new Error('router.dismissAll() is not supported in React Server Components yet');\n    },\n    setParams() {\n      throw new Error('router.setParams() is not supported in React Server Components yet');\n    },\n\n    // TODO: The behavior here is not the same as before.\n    navigate: push,\n    push,\n    replace,\n    reload,\n    back,\n    forward,\n    prefetch,\n  };\n}\n\nconst RouterSlot = ({\n  route,\n  routerData,\n  cachedRef,\n  id,\n  fallback,\n  children,\n}: {\n  route: RouteProps;\n  routerData: RouterData;\n  cachedRef: MutableRefObject<Record<string, RouteProps>>;\n  id: string;\n  fallback?: ReactNode;\n  children?: ReactNode;\n}) => {\n  // const unstable_shouldRenderPrev = (_err: unknown) => {\n  //   const shouldSkip = routerData[0];\n  //   const skip = getSkipList(shouldSkip, [id], route, cachedRef.current);\n  //   return skip.length > 0;\n  // };\n  return createElement(Slot, { id, fallback }, children);\n};\n\nconst getSkipList = (\n  shouldSkip: ShouldSkip | undefined,\n  componentIds: readonly string[],\n  route: RouteProps,\n  cached: Record<string, RouteProps>\n): string[] => {\n  const shouldSkipObj = Object.fromEntries(shouldSkip || []);\n  return componentIds.filter((id) => {\n    const prevProps = cached[id];\n    if (!prevProps) {\n      return false;\n    }\n    const shouldCheck = shouldSkipObj[id];\n    if (!shouldCheck) {\n      return false;\n    }\n    if (shouldCheck[0] && route.path !== prevProps.path) {\n      return false;\n    }\n    if (shouldCheck[0] && route.query !== prevProps.query) {\n      return false;\n    }\n    return true;\n  });\n};\n\n// TODO revisit shouldSkip API\ntype ShouldSkip = (readonly [\n  string,\n  readonly [\n    boolean, // if we compare path\n    string[], // searchParams keys to compare\n  ],\n])[];\n\n// Note: The router data must be a stable mutable object (array).\ntype RouterData = [\n  shouldSkip?: ShouldSkip,\n  locationListeners?: Set<(path: string, query: string) => void>,\n];\n\nconst DEFAULT_ROUTER_DATA: RouterData = [];\n\nexport function Router({ routerData = DEFAULT_ROUTER_DATA }) {\n  const route = parseRoute(new URL(getHref()));\n  const initialInput = getInputString(route.path);\n  const initialParams = JSON.stringify({ query: route.query });\n  const unstable_onFetchData = () => {};\n  return createElement(\n    Root as FunctionComponent<Omit<ComponentProps<typeof Root>, 'children'>>,\n    { initialInput, initialParams, unstable_onFetchData },\n    createElement(InnerRouter, { routerData })\n  );\n}\n\nconst notAvailableInServer = (name: string) => () => {\n  throw new Error(`${name} is not in the server`);\n};\n/**\n * ServerRouter for SSR\n * This is not a public API.\n */\nexport function ServerRouter({ children, route }: { children: ReactNode; route: RouteProps }) {\n  return createElement(\n    Fragment,\n    null,\n    createElement(\n      RouterContext.Provider,\n      {\n        value: {\n          route,\n          changeRoute: notAvailableInServer('changeRoute'),\n          prefetchRoute: notAvailableInServer('prefetchRoute'),\n        },\n      },\n      children\n    )\n  );\n}\n\nexport type LinkProps = ClassicLinkProps & {\n  href: string;\n  // pending?: ReactNode;\n  // notPending?: ReactNode;\n\n  // unstable_prefetchOnEnter?: boolean;\n  // unstable_prefetchOnView?: boolean;\n} & Omit<AnchorHTMLAttributes<HTMLAnchorElement>, 'href'>;\n\nexport const Link = forwardRef(ExpoRouterLink) as unknown as LinkComponent;\n\nLink.resolveHref = resolveHref;\n\nfunction ExpoRouterLink(\n  {\n    href,\n    replace,\n    push,\n    // TODO: This does not prevent default on the anchor tag.\n    relativeToDirectory,\n    asChild,\n    rel,\n    target,\n    download,\n    //   pending,\n    // notPending,\n    // unstable_prefetchOnEnter,\n    // unstable_prefetchOnView,\n    children,\n    ...props\n  }: LinkProps,\n  ref: ForwardedRef<Text>\n) {\n  // Mutate the style prop to add the className on web.\n  const style = useInteropClassName(props);\n\n  // If not passing asChild, we need to forward the props to the anchor tag using React Native Web's `hrefAttrs`.\n  const hrefAttrs = useHrefAttrs({ asChild, rel, target, download });\n\n  const resolvedHref = useMemo(() => {\n    if (href == null) {\n      throw new Error('Link: href is required');\n    }\n    return resolveHref(href);\n  }, [href]);\n\n  const router = use(RouterContext);\n  const changeRoute = router\n    ? router.changeRoute\n    : () => {\n        throw new Error('Missing Router');\n      };\n  const prefetchRoute = router\n    ? router.prefetchRoute\n    : () => {\n        throw new Error('Missing Router');\n      };\n  // TODO: Implement support for pending states in the future.\n  const [, startTransition] = useTransition();\n  // const elementRef = useRef<HTMLAnchorElement>();\n\n  // useEffect(() => {\n  //   if (unstable_prefetchOnView && process.env.EXPO_OS === 'web' && ref.current) {\n  //     const observer = new IntersectionObserver(\n  //       (entries) => {\n  //         entries.forEach((entry) => {\n  //           if (entry.isIntersecting) {\n  //             const url = new URL(resolvedHref, getHref());\n  //             if (router && url.href !== getHref()) {\n  //               const route = parseRoute(url);\n  //               router.prefetchRoute(route);\n  //             }\n  //           }\n  //         });\n  //       },\n  //       { threshold: 0.1 }\n  //     );\n\n  //     observer.observe(ref.current);\n\n  //     return () => {\n  //       observer.disconnect();\n  //     };\n  //   }\n  //   return () => {};\n  // }, [unstable_prefetchOnView, router, resolvedHref]);\n\n  const onClick = (event: MouseEvent<HTMLAnchorElement>) => {\n    event.preventDefault();\n    const url = new URL(resolvedHref, getHref());\n    // TODO: Use in-memory route for native platforms.\n    // if (url.href !== getHref()) {\n    const route = parseRoute(url);\n    prefetchRoute(route);\n    startTransition(() => {\n      getHistory().pushState(\n        {\n          ...getHistory().state,\n          expo_new_path: url.pathname !== window.location.pathname,\n        },\n        '',\n        url\n      );\n      changeRoute(route);\n    });\n    // }\n    props.onClick?.(event);\n  };\n  // const onMouseEnter = unstable_prefetchOnEnter\n  //   ? (event: MouseEvent<HTMLAnchorElement>) => {\n  //       const url = new URL(to, getHref());\n  //       if (url.href !== getHref()) {\n  //         const route = parseRoute(url);\n  //         prefetchRoute(route);\n  //       }\n  //       props.onMouseEnter?.(event);\n  //     }\n  //   : props.onMouseEnter;\n\n  const Element = asChild ? ReactSlot : Text;\n\n  const ele = createElement(\n    // @ts-expect-error\n    Element,\n    {\n      ...hrefAttrs,\n      ...props,\n      style,\n      href: resolvedHref,\n      onPress: onClick,\n      // onMouseEnter,\n      ref,\n    },\n    children\n  );\n  // if (isPending && pending !== undefined) {\n  //   return createElement(Fragment, null, ele, pending);\n  // }\n  // if (!isPending && notPending !== undefined) {\n  //   return createElement(Fragment, null, ele, notPending);\n  // }\n  return ele;\n}\n"]}