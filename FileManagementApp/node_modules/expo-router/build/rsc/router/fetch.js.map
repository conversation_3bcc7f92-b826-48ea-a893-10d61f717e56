{"version": 3, "file": "fetch.js", "sourceRoot": "", "sources": ["../../../src/rsc/router/fetch.ts"], "names": [], "mappings": ";;AAIA,sBAeC;AAnBD,sCAA2E;AAE3E,qCAAwC;AAEjC,KAAK,UAAU,KAAK,CAAC,KAAa,EAAE,IAAuB;IAChE,IAAI,CAAC;QACH,OAAO,MAAM,IAAA,aAAa,EAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,IACE,KAAK,CAAC,OAAO,CAAC,KAAK,CACjB,0GAA0G,CAC3G,EACD,CAAC;gBACD,MAAM,IAAI,qBAAY,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC", "sourcesContent": ["import { fetch as upstreamFetch, type FetchRequestInit } from 'expo/fetch';\n\nimport { NetworkError } from './errors';\n\nexport async function fetch(input: string, init?: FetchRequestInit) {\n  try {\n    return await upstreamFetch(input, init);\n  } catch (error: any) {\n    if (error instanceof Error) {\n      if (\n        error.message.match(\n          /(Network request failed|fetch failed): (The network connection was lost|Could not connect to the server)/\n        )\n      ) {\n        throw new NetworkError(error.message, input);\n      }\n    }\n    throw error;\n  }\n}\n"]}