{"version": 3, "file": "create-expo-pages.js", "sourceRoot": "", "sources": ["../../../src/rsc/router/create-expo-pages.ts"], "names": [], "mappings": ";;AAmBA,0CAQC;AA3BD,iDAA6C;AAU7C;;;;;;;;GAQG;AACH,SAAgB,eAAe,CAC7B,EAAkF;IAElF,OAAO,CAAC,eAAkC,EAAc,EAAE;QACxD,OAAO;YACL,OAAO,EAAE,IAAA,0BAAW,EAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,eAAe,EAAE,CAAC,CAAC;SACjE,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { createPages } from './create-pages';\nimport { Options as GetRoutesOptions } from '../../getRoutes';\nimport { EntriesDev } from '../server';\n\ntype CreatePagesFn = Parameters<typeof createPages>[0];\ntype CreatePagesFns = Parameters<CreatePagesFn>[0];\ntype CreatePagesOptions = Parameters<CreatePagesFn>[1] & {\n  getRouteOptions?: GetRoutesOptions;\n};\n\n/**\n * Wrapper around `createPages` to pass data from the server to the fn\n *\n * This is separated from the `createPages` function allowing us to keep the createPages\n * in sync with the original Waku implementation.\n *\n * @param fn\n * @returns\n */\nexport function createExpoPages(\n  fn: (fn: CreatePagesFns, options: CreatePagesOptions) => ReturnType<CreatePagesFn>\n) {\n  return (getRouteOptions?: GetRoutesOptions): EntriesDev => {\n    return {\n      default: createPages((a, b) => fn(a, { ...b, getRouteOptions })),\n    };\n  };\n}\n"]}