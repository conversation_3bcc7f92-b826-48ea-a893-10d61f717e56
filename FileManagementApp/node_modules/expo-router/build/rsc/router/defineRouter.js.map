{"version": 3, "file": "defineRouter.js", "sourceRoot": "", "sources": ["../../../src/rsc/router/defineRouter.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAyCH,sDAqLC;AAED,8CAaC;AA3OD,iCAAsC;AAGtC,qCAAwC;AACxC,qCAOkB;AAElB,iCAAwC;AACxC,kCAAyC;AAEzC,sCAAoD;AASpD,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,EAAE;IACrC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;gBAC5B,OAAO,GAA8B,CAAC;YACxC,CAAC;QACH,CAAC;QAAC,MAAM,CAAC;YACP,SAAS;QACX,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,SAAgB,qBAAqB,CACnC,aAQC,EACD,YAO2F;IAQ3F,IAAI,gBAA0C,CAAC;IAC/C,MAAM,eAAe,GAAG,KAAK,EAAE,WAAyB,EAAyB,EAAE;QACjF,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,WAA2B,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,aAAa,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBAChE,MAAM,KAAK,GACT,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;oBACtB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAE,CAAC,IAAI,KAAK,SAAS;oBAChC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAE,CAAC,IAAI,KAAK,KAAK,CAAC;gBAC/B,OAAO;oBACL,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,UAAU,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;iBAC5D,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,gBAAgB,CAAC;IAC1B,CAAC,CAAC;IACF,MAAM,UAAU,GAAG,KAAK,EACtB,QAAgB,EAChB,WAAoC,EACuB,EAAE;QAC7D,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,WAAW,CAAC,CAAC;QACtD,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,IAAA,qBAAc,EAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC9F,OAAO,KAAK;YACV,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK;gBACtB,CAAC,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC;gBACrB,CAAC,CAAC,CAAC,OAAO,CAAC;YACb,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,qCAAqC;gBAC3F,CAAC,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC;gBAC1B,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;IACtB,CAAC,CAAC;IACF,MAAM,aAAa,GAAkB,KAAK,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,EAAE;QAC5E,MAAM,QAAQ,GAAG,IAAA,yBAAgB,EAAC,KAAK,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,UAAU,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE,CAAC;YACjE,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,aAAa,GAEf,EAAE,CAAC;QAEP,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;QAE3C,MAAM,KAAK,GAAG,OAAO,YAAY,EAAE,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;QAChF,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,CAAE,YAAY,EAAE,IAAkB,CAAC,CAAC,CAAC,EAAE,CAAC;QACxF,MAAM,YAAY,GAAG,IAAA,wBAAe,EAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAqC,CAChD,MAAM,OAAO,CAAC,GAAG,CACf,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;YAC5B,IAAI,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;gBACvB,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,MAAM,aAAa,GAAG,CAAC,GAAqB,EAAE,EAAE;gBAC9C,IAAI,GAAG,EAAE,CAAC;oBACR,aAAa,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;gBAC1B,CAAC;qBAAM,CAAC;oBACN,OAAO,aAAa,CAAC,EAAE,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC,CAAC;YACF,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,EAAE,EAAE;gBACvC,sBAAsB,EAAE,aAAa;gBACrC,oBAAoB,EAAE,WAAW;aAClC,CAAC,CAAC;YACH,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,MAAM,OAAO,GAAG,IAAA,qBAAa,EAC3B,SAGE,EACF,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,EACvE,IAAA,qBAAa,EAAC,eAAQ,CAAC,CACxB,CAAC;YACF,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAU,CAAC;QAClC,CAAC,CAAC,CACH,CACF,CAAC,IAAI,EAAE,CAAC;QACT,OAAO,CAAC,IAAI,CAAC,CAAC,uBAAc,EAAE,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC9D,OAAO,CAAC,IAAI,CAAC,CAAC,oBAAW,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/C,OAAO,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC,CAAC;IAEF,MAAM,cAAc,GAAmB,KAAK,EAAE,6BAA6B,EAAE,EAAE;QAC7E,MAAM,UAAU,GAAG,MAAM,eAAe,EAAE,CAAC;QAC3C,MAAM,cAAc,GAA6B,EAAE,CAAC;QAEpD,KAAK,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,UAAU,EAAE,CAAC;YAChD,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,EAAE,CAAC;gBACpD,SAAS;YACX,CAAC;YAED,MAAM,QAAQ,GAAG,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClE,MAAM,KAAK,GAAG,IAAA,uBAAc,EAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,SAAS,GAAG,MAAM,6BAA6B,CAAC,KAAK,CAAC,CAAC;YAC7D,cAAc,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;QACvC,CAAC;QACD,MAAM,UAAU,GAAG;;qBAEF,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;;;;GAIhD,CAAC;QACA,MAAM,WAAW,GAAgB,EAAE,CAAC;QACpC,KAAK,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,UAAU,EAAE,CAAC;YACtE,MAAM,OAAO,GAAmC,EAAE,CAAC;YACnD,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,EAAE,CAAC;gBACrD,MAAM,QAAQ,GAAG,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClE,MAAM,KAAK,GAAG,IAAA,uBAAc,EAAC,QAAQ,CAAC,CAAC;gBACvC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;YACpC,CAAC;YACD,WAAW,CAAC,IAAI,CAAC;gBACf,QAAQ,EAAE,QAAQ;gBAClB,QAAQ;gBACR,OAAO;gBACP,UAAU;gBACV,UAAU;aACX,CAAC,CAAC;QACL,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC,CAAC;IAEF,MAAM,YAAY,GAAiB,KAAK,EAAE,QAAQ,EAAE,EAAE,YAAY,EAAE,WAAW,EAAE,EAAE,EAAE;QACnF,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAC3D,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE,CAAC;YAClC,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;gBAChC,QAAQ,GAAG,MAAM,CAAC;YACpB,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,MAAM,YAAY,GAAG,IAAA,wBAAe,EAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAG,IAAA,uBAAc,EAAC,QAAQ,CAAC,CAAC;QACvC,MAAM,IAAI,GAAG,IAAA,qBAAa,EACxB,qBAAwF,EACxF,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EACvE,YAAY,CAAC,WAAW,CACtB,CAAC,GAAc,EAAE,EAAE,EAAE,EAAE,CAAC,IAAA,qBAAa,EAAC,WAAI,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE,GAAG,CAAC,EACvE,IAAI,CACL,CACF,CAAC;QACF,OAAO;YACL,KAAK;YACL,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,YAAY,CAAC,QAAQ,EAAE,EAAE,CAAC;YAC1D,IAAI;SACL,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC;AACzD,CAAC;AAED,SAAgB,iBAAiB,CAC/B,QAAgB,EAChB,YAA8B,EAC9B,IAAe;IAEf,IAAI,IAAI,EAAE,CAAC;QACT,YAAY,GAAG,IAAI,eAAe,CAAC,YAAY,CAAC,CAAC;QACjD,KAAK,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC;YACtB,YAAY,CAAC,MAAM,CAAC,uBAAc,EAAE,EAAE,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IACD,MAAM,KAAK,GAAG,IAAA,uBAAc,EAAC,QAAQ,CAAC,CAAC;IACvC,IAAA,iBAAQ,EAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAChC,CAAC", "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n * Copyright © 2024 2023 <PERSON><PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport { createElement } from 'react';\nimport type { ComponentProps, FunctionComponent, ReactNode } from 'react';\n\nimport { ServerRouter } from './client';\nimport {\n  getComponentIds,\n  getInputString,\n  parseInputString,\n  PARAM_KEY_SKIP,\n  SHOULD_SKIP_ID,\n  LOCATION_ID,\n} from './common';\nimport type { RouteProps, ShouldSkip } from './common';\nimport { Children, Slot } from './host';\nimport { getPathMapping } from '../path';\nimport type { PathSpec } from '../path';\nimport { defineEntries, rerender } from '../server';\nimport type { BuildConfig, RenderEntries, GetBuildConfig, GetSsrConfig } from '../server';\n\ntype RoutePropsForLayout = Omit<RouteProps, 'searchParams'> & {\n  children: ReactNode;\n};\n\ntype ShouldSkipValue = ShouldSkip[number][1];\n\nconst safeJsonParse = (str: unknown) => {\n  if (typeof str === 'string') {\n    try {\n      const obj = JSON.parse(str);\n      if (typeof obj === 'object') {\n        return obj as Record<string, unknown>;\n      }\n    } catch {\n      // ignore\n    }\n  }\n  return undefined;\n};\n\nexport function unstable_defineRouter(\n  getPathConfig: () => Promise<\n    Iterable<{\n      pattern: string;\n      path: PathSpec;\n      isStatic?: boolean;\n      noSsr?: boolean;\n      data?: unknown; // For build: put in customData\n    }>\n  >,\n  getComponent: (\n    componentId: string, // \"**/layout\" or \"**/page\"\n    options: {\n      // TODO setShouldSkip API is too hard to understand\n      unstable_setShouldSkip: (val?: ShouldSkipValue) => void;\n      unstable_buildConfig: BuildConfig | undefined;\n    }\n  ) => Promise<FunctionComponent<RouteProps> | FunctionComponent<RoutePropsForLayout> | null>\n): ReturnType<typeof defineEntries> {\n  type MyPathConfig = {\n    pattern: string;\n    pathname: PathSpec;\n    isStatic?: boolean | undefined;\n    customData: { noSsr?: boolean; is404: boolean; data: unknown };\n  }[];\n  let cachedPathConfig: MyPathConfig | undefined;\n  const getMyPathConfig = async (buildConfig?: BuildConfig): Promise<MyPathConfig> => {\n    if (buildConfig) {\n      return buildConfig as MyPathConfig;\n    }\n    if (!cachedPathConfig) {\n      cachedPathConfig = Array.from(await getPathConfig()).map((item) => {\n        const is404 =\n          item.path.length === 1 &&\n          item.path[0]!.type === 'literal' &&\n          item.path[0]!.name === '404';\n        return {\n          pattern: item.pattern,\n          pathname: item.path,\n          isStatic: item.isStatic,\n          customData: { is404, noSsr: !!item.noSsr, data: item.data },\n        };\n      });\n    }\n    return cachedPathConfig;\n  };\n  const existsPath = async (\n    pathname: string,\n    buildConfig: BuildConfig | undefined\n  ): Promise<['FOUND', 'NO_SSR'?] | ['NOT_FOUND', 'HAS_404'?]> => {\n    const pathConfig = await getMyPathConfig(buildConfig);\n    const found = pathConfig.find(({ pathname: pathSpec }) => getPathMapping(pathSpec, pathname));\n    return found\n      ? found.customData.noSsr\n        ? ['FOUND', 'NO_SSR']\n        : ['FOUND']\n      : pathConfig.some(({ customData: { is404 } }) => is404) // FIXMEs should avoid re-computation\n        ? ['NOT_FOUND', 'HAS_404']\n        : ['NOT_FOUND'];\n  };\n  const renderEntries: RenderEntries = async (input, { params, buildConfig }) => {\n    const pathname = parseInputString(input);\n    if ((await existsPath(pathname, buildConfig))[0] === 'NOT_FOUND') {\n      return null;\n    }\n    const shouldSkipObj: {\n      [componentId: ShouldSkip[number][0]]: ShouldSkip[number][1];\n    } = {};\n\n    const parsedParams = safeJsonParse(params);\n\n    const query = typeof parsedParams?.query === 'string' ? parsedParams.query : '';\n    const skip = Array.isArray(parsedParams?.skip) ? (parsedParams?.skip as unknown[]) : [];\n    const componentIds = getComponentIds(pathname);\n    const entries: (readonly [string, ReactNode])[] = (\n      await Promise.all(\n        componentIds.map(async (id) => {\n          if (skip?.includes(id)) {\n            return [];\n          }\n          const setShouldSkip = (val?: ShouldSkipValue) => {\n            if (val) {\n              shouldSkipObj[id] = val;\n            } else {\n              delete shouldSkipObj[id];\n            }\n          };\n          const component = await getComponent(id, {\n            unstable_setShouldSkip: setShouldSkip,\n            unstable_buildConfig: buildConfig,\n          });\n          if (!component) {\n            return [];\n          }\n          const element = createElement(\n            component as FunctionComponent<{\n              path: string;\n              query?: string;\n            }>,\n            id.endsWith('/layout') ? { path: pathname } : { path: pathname, query },\n            createElement(Children)\n          );\n          return [[id, element]] as const;\n        })\n      )\n    ).flat();\n    entries.push([SHOULD_SKIP_ID, Object.entries(shouldSkipObj)]);\n    entries.push([LOCATION_ID, [pathname, query]]);\n    return Object.fromEntries(entries);\n  };\n\n  const getBuildConfig: GetBuildConfig = async (unstable_collectClientModules) => {\n    const pathConfig = await getMyPathConfig();\n    const path2moduleIds: Record<string, string[]> = {};\n\n    for (const { pathname: pathSpec } of pathConfig) {\n      if (pathSpec.some(({ type }) => type !== 'literal')) {\n        continue;\n      }\n\n      const pathname = '/' + pathSpec.map(({ name }) => name).join('/');\n      const input = getInputString(pathname);\n      const moduleIds = await unstable_collectClientModules(input);\n      path2moduleIds[pathname] = moduleIds;\n    }\n    const customCode = `\nglobalThis.__EXPO_ROUTER_PREFETCH__ = (path) => {\n  const path2ids = ${JSON.stringify(path2moduleIds)};\n  for (const id of path2ids[path] || []) {\n    import(id);\n  }\n};`;\n    const buildConfig: BuildConfig = [];\n    for (const { pathname: pathSpec, isStatic, customData } of pathConfig) {\n      const entries: BuildConfig[number]['entries'] = [];\n      if (pathSpec.every(({ type }) => type === 'literal')) {\n        const pathname = '/' + pathSpec.map(({ name }) => name).join('/');\n        const input = getInputString(pathname);\n        entries.push({ input, isStatic });\n      }\n      buildConfig.push({\n        pathname: pathSpec,\n        isStatic,\n        entries,\n        customCode,\n        customData,\n      });\n    }\n    return buildConfig;\n  };\n\n  const getSsrConfig: GetSsrConfig = async (pathname, { searchParams, buildConfig }) => {\n    const pathStatus = await existsPath(pathname, buildConfig);\n    if (pathStatus[1] === 'NO_SSR') {\n      return null;\n    }\n    if (pathStatus[0] === 'NOT_FOUND') {\n      if (pathStatus[1] === 'HAS_404') {\n        pathname = '/404';\n      } else {\n        return null;\n      }\n    }\n    const componentIds = getComponentIds(pathname);\n    const input = getInputString(pathname);\n    const html = createElement(\n      ServerRouter as FunctionComponent<Omit<ComponentProps<typeof ServerRouter>, 'children'>>,\n      { route: { path: pathname, query: searchParams.toString(), hash: '' } },\n      componentIds.reduceRight(\n        (acc: ReactNode, id) => createElement(Slot, { id, fallback: acc }, acc),\n        null\n      )\n    );\n    return {\n      input,\n      params: JSON.stringify({ query: searchParams.toString() }),\n      html,\n    };\n  };\n\n  return { renderEntries, getBuildConfig, getSsrConfig };\n}\n\nexport function unstable_redirect(\n  pathname: string,\n  searchParams?: URLSearchParams,\n  skip?: string[]\n) {\n  if (skip) {\n    searchParams = new URLSearchParams(searchParams);\n    for (const id of skip) {\n      searchParams.append(PARAM_KEY_SKIP, id);\n    }\n  }\n  const input = getInputString(pathname);\n  rerender(input, searchParams);\n}\n"]}