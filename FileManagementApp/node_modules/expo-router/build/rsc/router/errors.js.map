{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../../src/rsc/router/errors.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAEH,MAAa,gBAAiB,SAAQ,KAAK;IAKhC;IAJT,IAAI,GAAG,oBAAoB,CAAC;IAE5B,YACE,WAAsD,EAC/C,GAAW;QAElB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAFpB,QAAG,GAAH,GAAG,CAAQ;QAGlB,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC;QAE/B,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;YAC7B,IAAY,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;CACF;AAdD,4CAcC;AAED,MAAa,gBAAiB,SAAQ,KAAK;IAKhC;IACA;IAEA;IAPT,IAAI,GAAG,oBAAoB,CAAC;IAE5B,YACE,OAAe,EACR,GAAW,EACX,UAAkB;IACzB,wCAAwC;IACjC,OAAgB;QAEvB,KAAK,CAAC,OAAO,CAAC,CAAC;QALR,QAAG,GAAH,GAAG,CAAQ;QACX,eAAU,GAAV,UAAU,CAAQ;QAElB,YAAO,GAAP,OAAO,CAAS;QAGvB,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC;IACjC,CAAC;CACF;AAbD,4CAaC;AAED,MAAa,YAAa,SAAQ,KAAK;IAK5B;IAJT,IAAI,GAAG,eAAe,CAAC;IAEvB,YACE,OAAe,EACR,GAAW;QAElB,KAAK,CAAC,OAAO,CAAC,CAAC;QAFR,QAAG,GAAH,GAAG,CAAQ;QAGlB,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;IAC7B,CAAC;CACF;AAVD,oCAUC", "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nexport class MetroServerError extends Error {\n  code = 'METRO_SERVER_ERROR';\n\n  constructor(\n    errorObject: { message: string } & Record<string, any>,\n    public url: string\n  ) {\n    super(errorObject.message);\n    this.name = 'MetroServerError';\n\n    for (const key in errorObject) {\n      (this as any)[key] = errorObject[key];\n    }\n  }\n}\n\nexport class ReactServerError extends Error {\n  code = 'REACT_SERVER_ERROR';\n\n  constructor(\n    message: string,\n    public url: string,\n    public statusCode: number,\n    /** Response headers from the server. */\n    public headers: Headers\n  ) {\n    super(message);\n    this.name = 'ReactServerError';\n  }\n}\n\nexport class NetworkError extends Error {\n  code = 'NETWORK_ERROR';\n\n  constructor(\n    message: string,\n    public url: string\n  ) {\n    super(message);\n    this.name = 'NetworkError';\n  }\n}\n"]}