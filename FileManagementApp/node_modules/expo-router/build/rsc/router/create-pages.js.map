{"version": 3, "file": "create-pages.js", "sourceRoot": "", "sources": ["../../../src/rsc/router/create-pages.ts"], "names": [], "mappings": ";AAAA,sDAAsD;AACtD;;;;;;;;GAQG;;AAqKH,kCAyOC;AA5YD,iCAAsC;AAItC,iDAAuD;AACvD,kCAAmF;AAInF,MAAM,iBAAiB,GAAG,CAAC,MAAgB,EAAE,IAAc,EAAE,EAAE;IAC7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACvC,IACE,CAAC,IAAI,IAAI,CAAC,MAAM;YAChB,MAAM,CAAC,CAAC,CAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAE,CAAC,IAAI;YACjC,MAAM,CAAC,CAAC,CAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAE,CAAC,IAAI,EACjC,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AA6IlF,SAAgB,WAAW,CACzB,EASkB;IAElB,IAAI,UAAU,GAAG,KAAK,CAAC;IAEvB,yEAAyE;IACzE,MAAM,aAAa,GAAG,IAAI,GAAG,EAAsB,CAAC;IACpD,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAA8C,CAAC;IACjF,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAA8C,CAAC;IAClF,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAA8C,CAAC;IACnF,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAkC,CAAC;IACrE,MAAM,QAAQ,GAAG,IAAI,OAAO,EAAY,CAAC;IACzC,MAAM,YAAY,GAAG,IAAI,GAAG,EAAmB,CAAC;IAEhD,MAAM,uBAAuB,GAAG,CAAC,EAAU,EAAE,SAAiC,EAAE,EAAE;QAChF,IAAI,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,SAAS,EAAE,CAAC;YAC3E,MAAM,IAAI,KAAK,CAAC,6BAA6B,EAAE,EAAE,CAAC,CAAC;QACrD,CAAC;QACD,kBAAkB,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;IACxC,CAAC,CAAC;IAEF,MAAM,UAAU,GAAe,CAAC,IAAI,EAAE,EAAE;QACtC,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QACD,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzB,CAAC;QACD,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,CAAC,GAAG,EAAE;YACvC,IAAI,QAAQ,GAAG,CAAC,CAAC;YACjB,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;gBAC5B,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC5B,QAAQ,EAAE,CAAC;gBACb,CAAC;gBACD,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC7B,YAAY,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC;YACD,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;QACpC,CAAC,CAAC,EAAE,CAAC;QACL,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC;YAC/C,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;YACzC,MAAM,EAAE,GAAG,IAAA,eAAQ,EAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAC1D,uBAAuB,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9C,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,QAAQ,GAAG,CAAC,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;YAC7E,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAChD,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CACxD,CAAC;YACF,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;gBACrC,IAAI,UAAU,CAAC,MAAM,KAAK,QAAQ,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;oBACzD,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;gBAClE,CAAC;gBACD,MAAM,OAAO,GAAsC,EAAE,CAAC;gBACtD,IAAI,SAAS,GAAG,CAAC,CAAC;gBAClB,MAAM,SAAS,GAAa,EAAE,CAAC;gBAC/B,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;oBAClC,QAAQ,IAAI,EAAE,CAAC;wBACb,KAAK,SAAS;4BACZ,SAAS,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC;4BACtB,MAAM;wBACR,KAAK,UAAU;4BACb,OAAO,CAAC,IAAK,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;4BAC7C,UAAU,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gCAC7C,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;4BACvB,CAAC,CAAC,CAAC;4BACH,MAAM;wBACR,KAAK,OAAO;4BACV,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAE,CAAC,CAAC;4BACzC,OAAO,CAAC,IAAK,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAE,CAAC;4BAClD,MAAM;oBACV,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrF,MAAM,EAAE,GAAG,IAAA,eAAQ,EAAC,GAAG,SAAS,EAAE,MAAM,CAAC,CAAC;gBAC1C,MAAM,gBAAgB,GAAG,CAAC,KAA8B,EAAE,EAAE,CAC1D,IAAA,qBAAa,EAAC,IAAI,CAAC,SAAgB,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;gBACjE,uBAAuB,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;YAC3D,IAAI,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3D,CAAC;YACD,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAChE,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;YAC3D,IAAI,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3D,CAAC;YACD,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QACjE,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,8BAA8B,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,YAAY,GAAiB,CAAC,MAAM,EAAE,EAAE;QAC5C,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,MAAM,EAAE,GAAG,IAAA,eAAQ,EAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAC9D,uBAAuB,CAAC,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAChD,CAAC;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACvC,IAAI,oBAAoB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1C,MAAM,IAAI,KAAK,CAAC,4BAA4B,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAC7D,CAAC;YACD,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAChD,oBAAoB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;QACtE,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,qBAAqB,GAAG,CAAC,IAAY,EAAE,IAAa,EAAE,EAAE;QAC5D,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC/B,CAAC,CAAC;IAEF,IAAI,KAAgC,CAAC;IACrC,MAAM,SAAS,GAAG,KAAK,EAAE,WAAyB,EAAE,EAAE;QACpD,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,EAAE,CAAC;YAC1B,KAAK,GAAG,EAAE,CACR,EAAE,UAAU,EAAE,YAAY,EAAE,qBAAqB,EAAE,EACnD,EAAE,oBAAoB,EAAE,WAAW,EAAE,CACtC,CAAC;YACF,MAAM,KAAK,CAAC;YACZ,UAAU,GAAG,IAAI,CAAC;QACpB,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC,CAAC;IAEF,OAAO,IAAA,oCAAqB,EAC1B,KAAK,IAAI,EAAE;QACT,MAAM,SAAS,EAAE,CAAC;QAClB,MAAM,KAAK,GAML,EAAE,CAAC;QACT,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,aAAa,EAAE,CAAC;YAC7C,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,QAAQ,GAAG,CAAC,GAAG,EAAE;gBACrB,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;oBACzD,IAAI,iBAAiB,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,CAAC;wBAChD,OAAO,KAAK,CAAC;oBACf,CAAC;gBACH,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,EAAE,CAAC;YAEL,KAAK,CAAC,IAAI,CAAC;gBACT,OAAO,EAAE,IAAA,kBAAW,EAAC,IAAA,wBAAiB,EAAC,IAAI,CAAC,CAAC;gBAC7C,IAAI,EAAE,QAAQ;gBACd,QAAQ;gBACR,KAAK;gBACL,IAAI,EAAE,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;aAC7B,CAAC,CAAC;QACL,CAAC;QACD,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;YACpD,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACrC,KAAK,CAAC,IAAI,CAAC;gBACT,OAAO,EAAE,IAAA,kBAAW,EAAC,IAAA,wBAAiB,EAAC,IAAI,CAAC,CAAC;gBAC7C,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,KAAK;gBACf,KAAK;gBACL,IAAI,EAAE,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;aAC7B,CAAC,CAAC;QACL,CAAC;QACD,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;YACrD,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACrC,KAAK,CAAC,IAAI,CAAC;gBACT,OAAO,EAAE,IAAA,kBAAW,EAAC,IAAA,wBAAiB,EAAC,IAAI,CAAC,CAAC;gBAC7C,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,KAAK;gBACf,KAAK;gBACL,IAAI,EAAE,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;aAC7B,CAAC,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,EACD,KAAK,EAAE,EAAE,EAAE,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,EAAE,EAAE;QAC7D,MAAM,SAAS,CAAC,oBAAoB,CAAC,CAAC;QACtC,MAAM,eAAe,GAAG,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACnD,IAAI,eAAe,EAAE,CAAC;YACpB,sBAAsB,CAAC,EAAE,CAAC,CAAC;YAC3B,OAAO,eAAe,CAAC;QACzB,CAAC;QACD,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;YAC5D,MAAM,OAAO,GAAG,IAAA,qBAAc,EAAC,CAAC,GAAG,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACrF,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACtC,sBAAsB,EAAE,CAAC;oBACzB,OAAO,SAAS,CAAC;gBACnB,CAAC;gBACD,MAAM,gBAAgB,GAAG,CAAC,KAA8B,EAAE,EAAE,CAC1D,IAAA,qBAAa,EAAC,SAAS,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;gBACrD,sBAAsB,EAAE,CAAC;gBACzB,OAAO,gBAAgB,CAAC;YAC1B,CAAC;QACH,CAAC;QACD,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;YAC7D,MAAM,OAAO,GAAG,IAAA,qBAAc,EAAC,CAAC,GAAG,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACrF,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,gBAAgB,GAAG,CAAC,KAA8B,EAAE,EAAE,CAC1D,IAAA,qBAAa,EAAC,SAAS,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;gBACrD,sBAAsB,EAAE,CAAC;gBACzB,OAAO,gBAAgB,CAAC;YAC1B,CAAC;QACH,CAAC;QACD,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;YAC9D,MAAM,OAAO,GAAG,IAAA,qBAAc,EAAC,CAAC,GAAG,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACvF,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;oBAChC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;gBACxD,CAAC;gBACD,sBAAsB,EAAE,CAAC;gBACzB,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QACD,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,iBAAiB;QAC7C,OAAO,IAAI,CAAC,CAAC,YAAY;IAC3B,CAAC,CACF,CAAC;AACJ,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n/**\n * Copyright © 2024 650 Industries.\n * Copyright © 2024 2023 <PERSON><PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * https://github.com/dai-shi/waku/blob/3d1cc7d714b67b142c847e879c30f0724fc457a7/packages/waku/src/router/create-pages.ts#L1\n */\n\nimport { createElement } from 'react';\nimport type { FunctionComponent, ReactNode } from 'react';\n\nimport type { RouteProps } from './common.js';\nimport { unstable_defineRouter } from './defineRouter';\nimport { joinPath, parsePathWithSlug, getPathMapping, path2regexp } from '../path';\nimport type { PathSpec } from '../path';\nimport type { BuildConfig } from '../server.js';\n\nconst hasPathSpecPrefix = (prefix: PathSpec, path: PathSpec) => {\n  for (let i = 0; i < prefix.length; i++) {\n    if (\n      i >= path.length ||\n      prefix[i]!.type !== path[i]!.type ||\n      prefix[i]!.name !== path[i]!.name\n    ) {\n      return false;\n    }\n  }\n  return true;\n};\n\nconst sanitizeSlug = (slug: string) => slug.replace(/\\./g, '').replace(/ /g, '-');\n\n// createPages API (a wrapper around unstable_defineRouter)\n\n/**\n * Type version of `String.prototype.split()`. Splits the first string argument by the second string argument\n * @example\n * ```ts\n * // ['a', 'b', 'c']\n * type Case1 = Split<'abc', ''>\n * // ['a', 'b', 'c']\n * type Case2 = Split<'a,b,c', ','>\n * ```\n */\ntype Split<Str extends string, Del extends string | number> = string extends Str\n  ? string[]\n  : '' extends Str\n    ? []\n    : Str extends `${infer T}${Del}${infer U}`\n      ? [T, ...Split<U, Del>]\n      : [Str];\n\n/** Assumes that the path is a part of a slug path. */\ntype IsValidPathItem<T> = T extends `/${infer _}` ? false : T extends '[]' | '' ? false : true;\n/**\n * This is a helper type to check if a path is valid in a slug path.\n */\nexport type IsValidPathInSlugPath<T> = T extends `/${infer L}/${infer R}`\n  ? IsValidPathItem<L> extends true\n    ? IsValidPathInSlugPath<`/${R}`>\n    : false\n  : T extends `/${infer U}`\n    ? IsValidPathItem<U>\n    : false;\n/** Checks if a particular slug name exists in a path. */\nexport type HasSlugInPath<T, K extends string> = T extends `/[${K}]/${infer _}`\n  ? true\n  : T extends `/${infer _}/${infer U}`\n    ? HasSlugInPath<`/${U}`, K>\n    : T extends `/[${K}]`\n      ? true\n      : false;\n\nexport type HasWildcardInPath<T> = T extends `/[...${string}]/${string}`\n  ? true\n  : T extends `/${infer _}/${infer U}`\n    ? HasWildcardInPath<`/${U}`>\n    : T extends `/[...${string}]`\n      ? true\n      : false;\n\nexport type PathWithSlug<T, K extends string> =\n  IsValidPathInSlugPath<T> extends true ? (HasSlugInPath<T, K> extends true ? T : never) : never;\n\ntype _GetSlugs<\n  Route extends string,\n  SplitRoute extends string[] = Split<Route, '/'>,\n  Result extends string[] = [],\n> = SplitRoute extends []\n  ? Result\n  : SplitRoute extends [`${infer MaybeSlug}`, ...infer Rest]\n    ? Rest extends string[]\n      ? MaybeSlug extends `[${infer Slug}]`\n        ? _GetSlugs<Route, Rest, [...Result, Slug]>\n        : _GetSlugs<Route, Rest, Result>\n      : never\n    : Result;\n\nexport type GetSlugs<Route extends string> = _GetSlugs<Route>;\n\nexport type StaticSlugRoutePathsTuple<\n  T extends string,\n  Slugs extends unknown[] = GetSlugs<T>,\n  Result extends string[] = [],\n> = Slugs extends []\n  ? Result\n  : Slugs extends [infer _, ...infer Rest]\n    ? StaticSlugRoutePathsTuple<T, Rest, [...Result, string]>\n    : never;\n\ntype StaticSlugRoutePaths<T extends string> =\n  HasWildcardInPath<T> extends true\n    ? string[] | string[][]\n    : StaticSlugRoutePathsTuple<T> extends [string]\n      ? string[]\n      : StaticSlugRoutePathsTuple<T>[];\n\nexport type PathWithoutSlug<T> = T extends '/'\n  ? T\n  : IsValidPathInSlugPath<T> extends true\n    ? HasSlugInPath<T, string> extends true\n      ? never\n      : T\n    : never;\n\ntype PathWithStaticSlugs<T extends string> = T extends `/`\n  ? T\n  : IsValidPathInSlugPath<T> extends true\n    ? T\n    : never;\n\nexport type PathWithWildcard<\n  Path,\n  SlugKey extends string,\n  WildSlugKey extends string,\n> = PathWithSlug<Path, SlugKey | `...${WildSlugKey}`>;\n\nexport type CreatePage = <Path extends string, SlugKey extends string, WildSlugKey extends string>(\n  page: (\n    | {\n        render: 'static';\n        path: PathWithoutSlug<Path>;\n        component: FunctionComponent<RouteProps>;\n      }\n    | {\n        render: 'static';\n        path: PathWithStaticSlugs<Path>;\n        staticPaths: StaticSlugRoutePaths<Path>;\n        component: FunctionComponent<RouteProps & Record<SlugKey, string>>;\n      }\n    | {\n        render: 'dynamic';\n        path: PathWithoutSlug<Path>;\n        component: FunctionComponent<RouteProps>;\n      }\n    | {\n        render: 'dynamic';\n        path: PathWithWildcard<Path, SlugKey, WildSlugKey>;\n        component: FunctionComponent<\n          RouteProps & Record<SlugKey, string> & Record<WildSlugKey, string[]>\n        >;\n      }\n  ) & { unstable_disableSSR?: boolean }\n) => void;\n\nexport type CreateLayout = <T extends string>(layout: {\n  render: 'static' | 'dynamic';\n  path: PathWithoutSlug<T>;\n  component: FunctionComponent<Omit<RouteProps, 'searchParams'> & { children: ReactNode }>;\n}) => void;\n\nexport function createPages(\n  fn: (\n    fns: {\n      createPage: CreatePage;\n      createLayout: CreateLayout;\n      unstable_setBuildData: (path: string, data: unknown) => void;\n    },\n    opts: {\n      unstable_buildConfig: BuildConfig | undefined;\n    }\n  ) => Promise<void>\n) {\n  let configured = false;\n\n  // TODO I think there's room for improvement to refactor these structures\n  const staticPathSet = new Set<[string, PathSpec]>();\n  const dynamicPagePathMap = new Map<string, [PathSpec, FunctionComponent<any>]>();\n  const wildcardPagePathMap = new Map<string, [PathSpec, FunctionComponent<any>]>();\n  const dynamicLayoutPathMap = new Map<string, [PathSpec, FunctionComponent<any>]>();\n  const staticComponentMap = new Map<string, FunctionComponent<any>>();\n  const noSsrSet = new WeakSet<PathSpec>();\n  const buildDataMap = new Map<string, unknown>();\n\n  const registerStaticComponent = (id: string, component: FunctionComponent<any>) => {\n    if (staticComponentMap.has(id) && staticComponentMap.get(id) !== component) {\n      throw new Error(`Duplicated component for: ${id}`);\n    }\n    staticComponentMap.set(id, component);\n  };\n\n  const createPage: CreatePage = (page) => {\n    if (configured) {\n      throw new Error('no longer available');\n    }\n    const pathSpec = parsePathWithSlug(page.path);\n    if (page.unstable_disableSSR) {\n      noSsrSet.add(pathSpec);\n    }\n    const { numSlugs, numWildcards } = (() => {\n      let numSlugs = 0;\n      let numWildcards = 0;\n      for (const slug of pathSpec) {\n        if (slug.type !== 'literal') {\n          numSlugs++;\n        }\n        if (slug.type === 'wildcard') {\n          numWildcards++;\n        }\n      }\n      return { numSlugs, numWildcards };\n    })();\n    if (page.render === 'static' && numSlugs === 0) {\n      staticPathSet.add([page.path, pathSpec]);\n      const id = joinPath(page.path, 'page').replace(/^\\//, '');\n      registerStaticComponent(id, page.component);\n    } else if (page.render === 'static' && numSlugs > 0 && 'staticPaths' in page) {\n      const staticPaths = page.staticPaths.map((item) =>\n        (Array.isArray(item) ? item : [item]).map(sanitizeSlug)\n      );\n      for (const staticPath of staticPaths) {\n        if (staticPath.length !== numSlugs && numWildcards === 0) {\n          throw new Error('staticPaths does not match with slug pattern');\n        }\n        const mapping: Record<string, string | string[]> = {};\n        let slugIndex = 0;\n        const pathItems: string[] = [];\n        pathSpec.forEach(({ type, name }) => {\n          switch (type) {\n            case 'literal':\n              pathItems.push(name!);\n              break;\n            case 'wildcard':\n              mapping[name!] = staticPath.slice(slugIndex);\n              staticPath.slice(slugIndex++).forEach((slug) => {\n                pathItems.push(slug);\n              });\n              break;\n            case 'group':\n              pathItems.push(staticPath[slugIndex++]!);\n              mapping[name!] = pathItems[pathItems.length - 1]!;\n              break;\n          }\n        });\n        staticPathSet.add([page.path, pathItems.map((name) => ({ type: 'literal', name }))]);\n        const id = joinPath(...pathItems, 'page');\n        const WrappedComponent = (props: Record<string, unknown>) =>\n          createElement(page.component as any, { ...props, ...mapping });\n        registerStaticComponent(id, WrappedComponent);\n      }\n    } else if (page.render === 'dynamic' && numWildcards === 0) {\n      if (dynamicPagePathMap.has(page.path)) {\n        throw new Error(`Duplicated dynamic path: ${page.path}`);\n      }\n      dynamicPagePathMap.set(page.path, [pathSpec, page.component]);\n    } else if (page.render === 'dynamic' && numWildcards === 1) {\n      if (wildcardPagePathMap.has(page.path)) {\n        throw new Error(`Duplicated dynamic path: ${page.path}`);\n      }\n      wildcardPagePathMap.set(page.path, [pathSpec, page.component]);\n    } else {\n      throw new Error('Invalid page configuration: ' + page.path);\n    }\n  };\n\n  const createLayout: CreateLayout = (layout) => {\n    if (configured) {\n      throw new Error('no longer available');\n    }\n    if (layout.render === 'static') {\n      const id = joinPath(layout.path, 'layout').replace(/^\\//, '');\n      registerStaticComponent(id, layout.component);\n    } else if (layout.render === 'dynamic') {\n      if (dynamicLayoutPathMap.has(layout.path)) {\n        throw new Error(`Duplicated dynamic path: ${layout.path}`);\n      }\n      const pathSpec = parsePathWithSlug(layout.path);\n      dynamicLayoutPathMap.set(layout.path, [pathSpec, layout.component]);\n    } else {\n      throw new Error('Invalid layout configuration');\n    }\n  };\n\n  const unstable_setBuildData = (path: string, data: unknown) => {\n    buildDataMap.set(path, data);\n  };\n\n  let ready: Promise<void> | undefined;\n  const configure = async (buildConfig?: BuildConfig) => {\n    if (!configured && !ready) {\n      ready = fn(\n        { createPage, createLayout, unstable_setBuildData },\n        { unstable_buildConfig: buildConfig }\n      );\n      await ready;\n      configured = true;\n    }\n    await ready;\n  };\n\n  return unstable_defineRouter(\n    async () => {\n      await configure();\n      const paths: {\n        pattern: string;\n        path: PathSpec;\n        isStatic: boolean;\n        noSsr: boolean;\n        data: unknown;\n      }[] = [];\n      for (const [path, pathSpec] of staticPathSet) {\n        const noSsr = noSsrSet.has(pathSpec);\n        const isStatic = (() => {\n          for (const [_, [layoutPathSpec]] of dynamicLayoutPathMap) {\n            if (hasPathSpecPrefix(layoutPathSpec, pathSpec)) {\n              return false;\n            }\n          }\n          return true;\n        })();\n\n        paths.push({\n          pattern: path2regexp(parsePathWithSlug(path)),\n          path: pathSpec,\n          isStatic,\n          noSsr,\n          data: buildDataMap.get(path),\n        });\n      }\n      for (const [path, [pathSpec]] of dynamicPagePathMap) {\n        const noSsr = noSsrSet.has(pathSpec);\n        paths.push({\n          pattern: path2regexp(parsePathWithSlug(path)),\n          path: pathSpec,\n          isStatic: false,\n          noSsr,\n          data: buildDataMap.get(path),\n        });\n      }\n      for (const [path, [pathSpec]] of wildcardPagePathMap) {\n        const noSsr = noSsrSet.has(pathSpec);\n        paths.push({\n          pattern: path2regexp(parsePathWithSlug(path)),\n          path: pathSpec,\n          isStatic: false,\n          noSsr,\n          data: buildDataMap.get(path),\n        });\n      }\n      return paths;\n    },\n    async (id, { unstable_setShouldSkip, unstable_buildConfig }) => {\n      await configure(unstable_buildConfig);\n      const staticComponent = staticComponentMap.get(id);\n      if (staticComponent) {\n        unstable_setShouldSkip([]);\n        return staticComponent;\n      }\n      for (const [_, [pathSpec, Component]] of dynamicPagePathMap) {\n        const mapping = getPathMapping([...pathSpec, { type: 'literal', name: 'page' }], id);\n        if (mapping) {\n          if (Object.keys(mapping).length === 0) {\n            unstable_setShouldSkip();\n            return Component;\n          }\n          const WrappedComponent = (props: Record<string, unknown>) =>\n            createElement(Component, { ...props, ...mapping });\n          unstable_setShouldSkip();\n          return WrappedComponent;\n        }\n      }\n      for (const [_, [pathSpec, Component]] of wildcardPagePathMap) {\n        const mapping = getPathMapping([...pathSpec, { type: 'literal', name: 'page' }], id);\n        if (mapping) {\n          const WrappedComponent = (props: Record<string, unknown>) =>\n            createElement(Component, { ...props, ...mapping });\n          unstable_setShouldSkip();\n          return WrappedComponent;\n        }\n      }\n      for (const [_, [pathSpec, Component]] of dynamicLayoutPathMap) {\n        const mapping = getPathMapping([...pathSpec, { type: 'literal', name: 'layout' }], id);\n        if (mapping) {\n          if (Object.keys(mapping).length) {\n            throw new Error('[Bug] layout should not have slugs');\n          }\n          unstable_setShouldSkip();\n          return Component;\n        }\n      }\n      unstable_setShouldSkip([]); // negative cache\n      return null; // not found\n    }\n  );\n}\n"]}