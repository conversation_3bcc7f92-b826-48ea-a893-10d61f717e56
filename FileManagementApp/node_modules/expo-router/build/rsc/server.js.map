{"version": 3, "file": "server.js", "sourceRoot": "", "sources": ["../../src/rsc/server.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAyDH,sCAMC;AA4DD,4BAOC;AAED,gCASC;AAGD,4CAGC;AAjJD,6CAAgD;AAKnC,QAAA,eAAe,GAAG,uBAAuB,CAAC;AAkDvD,SAAgB,aAAa,CAC3B,aAA4B,EAC5B,cAA+B,EAC/B,YAA2B;IAE3B,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC;AACzD,CAAC;AAkBD,6DAA6D;AAC7D,oIAAoI;AACpI,SAAS,yBAAyB;IAChC,4FAA4F;IAC5F,mGAAmG;IACnG,sBAAsB;IACtB,MAAM,QAAQ,GAAG,UAAU,CAAC,sBAAsB,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;IAC1E,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;QACnC,UAAU,CAAC,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;IAC5C,CAAC;IAED,IAAI,UAAU,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAS,CAAC,EAAE,CAAC;QACjD,OAAO,UAAU,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAS,CAAE,CAAC;IACvD,CAAC;IAED,MAAM,WAAW,GAAG,IAAI,+BAAiB,EAAe,CAAC;IAEzD,UAAU,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAS,EAAE,WAAW,CAAC,CAAC;IAE1D,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,IAAI,mBAA4C,CAAC;AACjD,IAAI,kBAA2C,CAAC;AAEhD;;GAEG;AACI,MAAM,kBAAkB,GAAG,CAAI,WAAwB,EAAE,EAAW,EAAK,EAAE;IAChF,MAAM,aAAa,GAAG,yBAAyB,EAAE,CAAC;IAClD,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IAC5C,CAAC;IACD,mBAAmB,GAAG,kBAAkB,CAAC;IACzC,kBAAkB,GAAG,WAAW,CAAC;IACjC,IAAI,CAAC;QACH,OAAO,EAAE,EAAE,CAAC;IACd,CAAC;YAAS,CAAC;QACT,kBAAkB,GAAG,mBAAmB,CAAC;IAC3C,CAAC;AACH,CAAC,CAAC;AAZW,QAAA,kBAAkB,sBAY7B;AAEK,KAAK,UAAU,QAAQ,CAAC,KAAa,EAAE,MAAgB;IAC5D,MAAM,aAAa,GAAG,yBAAyB,EAAE,CAAC;IAClD,MAAM,WAAW,GAAG,aAAa,CAAC,QAAQ,EAAE,IAAI,kBAAkB,CAAC;IACnE,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAChE,CAAC;IACD,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACtC,CAAC;AAED,SAAgB,UAAU;IAGxB,MAAM,aAAa,GAAG,yBAAyB,EAAE,CAAC;IAClD,MAAM,WAAW,GAAG,aAAa,CAAC,QAAQ,EAAE,IAAI,kBAAkB,CAAC;IACnE,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;IACzE,CAAC;IACD,OAAO,WAAW,CAAC,OAAqB,CAAC;AAC3C,CAAC;AAED,mFAAmF;AAC5E,KAAK,UAAU,gBAAgB;IACpC,MAAM,OAAO,GAAG,CAAC,UAAU,EAAE,CAAC,uBAAe,CAAC,IAAI,EAAE,CAA2B,CAAC;IAChF,OAAO,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC;AAED,MAAM,eAAgB,SAAQ,OAAO;IACnC,GAAG;QACD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5D,CAAC;IACD,MAAM;QACJ,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5D,CAAC;IACD,MAAM;QACJ,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5D,CAAC;CACF", "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n * Copyright © 2024 2023 <PERSON><PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport { AsyncLocalStorage } from 'async_hooks';\nimport type { ReactNode } from 'react';\n\nimport type { PathSpec } from './path';\n\nexport const REQUEST_HEADERS = '__expo_requestHeaders';\n\ndeclare let globalThis: {\n  __EXPO_RSC_CACHE__?: Map<string, any>;\n  __expo_platform_header?: string;\n  __webpack_chunk_load__: (id: string) => Promise<any>;\n  __webpack_require__: (id: string) => any;\n};\n\ntype Config = any;\n\ntype Elements = Record<string, ReactNode>;\n\nexport type BuildConfig = {\n  pathname: string | PathSpec; // TODO drop support for string?\n  isStatic?: boolean | undefined;\n  entries?: {\n    input: string;\n    skipPrefetch?: boolean | undefined;\n    isStatic?: boolean | undefined;\n  }[];\n  context?: Record<string, unknown>;\n  customCode?: string; // optional code to inject TODO hope to remove this\n  customData?: unknown; // should be serializable with JSON.stringify\n}[];\n\nexport type RenderEntries = (\n  input: string,\n  options: {\n    params: unknown | undefined;\n    buildConfig: BuildConfig | undefined;\n  }\n) => Promise<Elements | null>;\n\nexport type GetBuildConfig = (\n  unstable_collectClientModules: (input: string) => Promise<string[]>\n) => Promise<BuildConfig>;\n\nexport type GetSsrConfig = (\n  pathname: string,\n  options: {\n    searchParams: URLSearchParams;\n    buildConfig?: BuildConfig | undefined;\n  }\n) => Promise<{\n  input: string;\n  searchParams?: URLSearchParams;\n  html: ReactNode;\n} | null>;\n\nexport function defineEntries(\n  renderEntries: RenderEntries,\n  getBuildConfig?: GetBuildConfig,\n  getSsrConfig?: GetSsrConfig\n) {\n  return { renderEntries, getBuildConfig, getSsrConfig };\n}\n\nexport type EntriesDev = {\n  default: ReturnType<typeof defineEntries>;\n};\n\nexport type EntriesPrd = EntriesDev & {\n  loadConfig: () => Promise<Config>;\n  loadModule: (id: string) => Promise<unknown>;\n  dynamicHtmlPaths: [pathSpec: PathSpec, htmlHead: string][];\n  publicIndexHtml: string;\n};\n\ntype RenderStore<> = {\n  rerender: (input: string, params?: unknown) => void;\n  context: Record<string, unknown>;\n};\n\n// TODO(EvanBacon): This can leak between platforms and runs.\n// We need to share this module between the server action module and the renderer module, per platform, and invalidate on refreshes.\nfunction getGlobalCacheForPlatform() {\n  // HACK: This is a workaround for the shared middleware being shared between web and native.\n  // In production the shared middleware is web-only and that causes the first version of this module\n  // to be bound to web.\n  const platform = globalThis.__expo_platform_header ?? process.env.EXPO_OS;\n  if (!globalThis.__EXPO_RSC_CACHE__) {\n    globalThis.__EXPO_RSC_CACHE__ = new Map();\n  }\n\n  if (globalThis.__EXPO_RSC_CACHE__.has(platform!)) {\n    return globalThis.__EXPO_RSC_CACHE__.get(platform!)!;\n  }\n\n  const serverCache = new AsyncLocalStorage<RenderStore>();\n\n  globalThis.__EXPO_RSC_CACHE__.set(platform!, serverCache);\n\n  return serverCache;\n}\n\nlet previousRenderStore: RenderStore | undefined;\nlet currentRenderStore: RenderStore | undefined;\n\n/**\n * This is an internal function and not for public use.\n */\nexport const runWithRenderStore = <T>(renderStore: RenderStore, fn: () => T): T => {\n  const renderStorage = getGlobalCacheForPlatform();\n  if (renderStorage) {\n    return renderStorage.run(renderStore, fn);\n  }\n  previousRenderStore = currentRenderStore;\n  currentRenderStore = renderStore;\n  try {\n    return fn();\n  } finally {\n    currentRenderStore = previousRenderStore;\n  }\n};\n\nexport async function rerender(input: string, params?: unknown) {\n  const renderStorage = getGlobalCacheForPlatform();\n  const renderStore = renderStorage.getStore() ?? currentRenderStore;\n  if (!renderStore) {\n    throw new Error('Render store is not available for rerender');\n  }\n  renderStore.rerender(input, params);\n}\n\nexport function getContext<\n  RscContext extends Record<string, unknown> = Record<string, unknown>,\n>(): RscContext {\n  const renderStorage = getGlobalCacheForPlatform();\n  const renderStore = renderStorage.getStore() ?? currentRenderStore;\n  if (!renderStore) {\n    throw new Error('Render store is not available for accessing context');\n  }\n  return renderStore.context as RscContext;\n}\n\n/** Get the request headers used to make the server component or action request. */\nexport async function unstable_headers(): Promise<Headers> {\n  const headers = (getContext()[REQUEST_HEADERS] || {}) as Record<string, string>;\n  return new ReadonlyHeaders(headers);\n}\n\nclass ReadonlyHeaders extends Headers {\n  set() {\n    throw new Error('Server component Headers are read-only');\n  }\n  append() {\n    throw new Error('Server component Headers are read-only');\n  }\n  delete() {\n    throw new Error('Server component Headers are read-only');\n  }\n}\n"]}