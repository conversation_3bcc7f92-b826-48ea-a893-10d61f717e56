{"version": 3, "file": "getStateFromPath-forks.js", "sourceRoot": "", "sources": ["../../src/fork/getStateFromPath-forks.ts"], "names": [], "mappings": ";;;;;AA2BA,wCAQC;AAED,4DAMC;AAED,oFA6BC;AAED,oCAiDC;AAED,sCAIC;AAED,oCAIC;AAED,sCAEC;AAED,kCAEC;AAED,sCAOC;AAwBD,0CAsBC;AAED,kEAKC;AAED,oCAUC;AAED,8CAgCC;AAED,0CAWC;AAQD,oDA4JC;AAED,4CAgCC;AAED,8BAUC;AAED,kDAeC;AA5eD,gFAA0C;AAI1C,0CAAyE;AAiBzE;;;;GAIG;AACH,SAAgB,cAAc,CAAC,MAAsB,EAAE,MAA4B;IACjF,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO;IAEnE,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAgB,wBAAwB,CAAC,GAAW;IAClD,IAAI,CAAC;QACH,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,GAAG,CAAC;IACb,CAAC;AACH,CAAC;AAED,SAAgB,oCAAoC,CAClD,IAAY,EACZ,UAA8B,OAAO,CAAC,GAAG,CAAC,aAAa;IAEvD,IAAI,MAAW,CAAC;IAChB,IAAI,CAAC;QACH,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;IAClD,CAAC;IAAC,MAAM,CAAC;QACP,gCAAgC;QAChC,OAAO;YACL,IAAI;YACJ,QAAQ,EAAE,EAAE;YACZ,mBAAmB,EAAE,EAAE;YACvB,GAAG,EAAE,IAAI,GAAG,CAAC,uBAAuB,CAAC;SACtC,CAAC;IACJ,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;IACjC,MAAM,cAAc,GAAG,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACvD,MAAM,iBAAiB,GAAG,IAAA,qCAA0B,EAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;IAElF,sCAAsC;IACtC,OAAO;QACL,gDAAgD;QAChD,IAAI;QACJ,mBAAmB,EAAE,cAAc,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG;QACnF,GAAG,EAAE,MAAM;QACX,iBAAiB;KAClB,CAAC;AACJ,CAAC;AAED,SAAgB,YAAY,CAC1B,MAAc,EACd,OAAe,EACf,UAAoB,EACpB,SAA8B,EAAE;IAEhC,MAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,MAAM,OAAO,GAAG,MAAM,KAAK,OAAO,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAChE,IAAI,eAAe,GAAG,CAAC,CAAC;IAExB,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;QACtC,IAAI,IAAI,EAAE,CAAC;YACT,oDAAoD;YACpD,MAAM,aAAa,GACjB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAE9E,SAAS,KAAK,aAAa,CAAC;YAE5B,IAAI,CAAC,IAAA,yBAAc,EAAC,IAAI,CAAC,EAAE,CAAC;gBAC1B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEjB,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,eAAe,EAAE,CAAC;gBACpB,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;IACnF,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC;IAEvE,IAAI,OAAO,EAAE,CAAC;QACZ,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpB,eAAe,EAAE,CAAC;IACpB,CAAC;IAED,OAAO;QACL,IAAI;QACJ,OAAO;QACP,WAAW;QACX,KAAK;QACL,eAAe;QACf,gBAAgB,EAAE,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QAC/E,sCAAsC;QACtC,kBAAkB,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACvD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC,CAAC;KACH,CAAC;AACJ,CAAC;AAED,SAAgB,aAAa,CAAC,OAAyB;IACrD,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;QACtB,MAAM,KAAK,CAAC,4EAA4E,CAAC,CAAC;IAC5F,CAAC;AACH,CAAC;AAED,SAAgB,YAAY,CAAC,MAAmB;IAC9C,OAAO,MAAM,CAAC,OAAO;QACnB,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACjF,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC;AAED,SAAgB,aAAa,CAAC,CAAS;IACrC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AAClE,CAAC;AAED,SAAgB,WAAW,CAAC,CAAS;IACnC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACnD,CAAC;AAED,SAAgB,aAAa,CAAC,CAAS,EAAE,KAAa;IACpD,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACtB,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;QACxD,OAAO,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;IACrE,CAAC;SAAM,CAAC;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,EAAU;IACpC,mCAAmC;IACnC,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAE5B,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,kCAAkC;QAClC,OAAO,cAAc,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;IACtD,CAAC;SAAM,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QAC9B,OAAO,WAAW,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,CAAC;IAED,gCAAgC;IAChC,IAAI,IAAA,yBAAc,EAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;QAC/B,+BAA+B;QAC/B,sEAAsE;QACtE,+EAA+E;QAC/E,OAAO,MAAM,IAAA,8BAAM,EAAC,EAAE,CAAC,OAAO,CAAC;IACjC,CAAC;IAED,OAAO,IAAA,8BAAM,EAAC,EAAE,CAAC,GAAG,KAAK,CAAC;AAC5B,CAAC;AAED,SAAgB,eAAe,CAAC,KAAkB,EAAE,MAAgC;IAClF,IAAI,MAAM,EAAE,CAAC;QACX,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAwB,CAAC;QACvF,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACnD,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;oBAC1C,OAAO,CAAC,IAAI,CACV,WAAW,KAAK,CAAC,IAAI,iBAAiB,IAAI,qEAAqE,CAChH,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1B,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;gBAC3B,SAAS;YACX,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3C,OAAO,KAAK,CAAC,MAAM,CAAC;QACtB,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAgB,2BAA2B,CAAC,KAAmB,EAAE,MAA4B;IAC3F,OAAO,KAAK,EAAE,CAAC;QACb,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC7B,KAAa,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAClE,CAAC;AACH,CAAC;AAED,SAAgB,YAAY,CAC1B,IAAY,EACZ,UAA8B,OAAO,CAAC,GAAG,CAAC,aAAa;IAEvD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,QAAQ,IAAA,8BAAM,EAAC,OAAO,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,iBAAiB,CAAC,OAAsB;IACtD,uFAAuF;IACvF,yEAAyE;IAEzE,2DAA2D;IAC3D,MAAM,SAAS,GAAG,OAAO;SACtB,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC;SACvC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACb,OAAO;YACL,GAAG,KAAK;YACR,wDAAwD;YACxD,yDAAyD;YACzD,IAAI,EAAE,IAAA,qCAA0B,EAAC,KAAK,CAAC,IAAI,CAAC;SAC7C,CAAC;IACJ,CAAC,CAAC,CAAC;IAEL,MAAM,KAAK,GACT,SAAS,CAAC,IAAI,CACZ,CAAC,MAAM,EAAE,EAAE;IACT,wGAAwG;IACxG,MAAM,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CACjE;QACD,SAAS,CAAC,IAAI,CACZ,CAAC,MAAM,EAAE,EAAE;QACT,6EAA6E;QAC7E,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,KAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CACxD;QACD,0EAA0E;QAC1E,8EAA8E;QAC9E,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,KAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAErF,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAgB,eAAe,CAAC,aAAmC;IACjE,MAAM,uBAAuB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAC1D,SAAS,CAAC,GAAG,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAC1D,CAAC;IAEF,OAAO,UAAU,MAAmB;QAClC,mDAAmD;QACnD,oFAAoF;QACpF,MAAM,CAAC,SAAS,GAAG,uBAAuB,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACjF,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,SAAS,GAAG,CAAC,GAAG,KAAe,EAAU,EAAE,CAC9C,EAAe;KACb,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;KACzC,MAAM,CAAC,OAAO,CAAC;KACf,IAAI,CAAC,GAAG,CAAC,CAAC;AAEf,SAAgB,oBAAoB,CAAC,mBAA6B,EAAE;IAClE,OAAO,SAAS,WAAW,CAAC,CAAc,EAAE,CAAc;QACxD,uBAAuB;QACvB,yDAAyD;QACzD,iDAAiD;QAEjD,gEAAgE;QAChE,4EAA4E;QAC5E,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;YAC5B,OAAO,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACtE,CAAC;QAED;;;;;;;;;;;;WAYG;QACH,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAClD,OAAO,CAAC,CAAC,CAAC;QACZ,CAAC;QAED,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAClD,OAAO,CAAC,CAAC;QACX,CAAC;QAED;;WAEG;QACH,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC/C,OAAO,CAAC,CAAC,CAAC;QACZ,CAAC;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtD,OAAO,CAAC,CAAC;QACX,CAAC;QAED;;WAEG;QACH,IAAI,CAAC,CAAC,eAAe,KAAK,CAAC,CAAC,eAAe,EAAE,CAAC;YAC5C,OAAO,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,eAAe,CAAC;QAC/C,CAAC;QAED;;WAEG;QACH,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAClE,OAAO,KAAK,KAAK,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAClE,OAAO,KAAK,KAAK,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,IACE,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;YAChE,kBAAkB,CAAC,MAAM,KAAK,kBAAkB,CAAC,MAAM,EACvD,CAAC;YACD,qEAAqE;YACrE,OAAO,kBAAkB,CAAC,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC;QAC/D,CAAC;QAED;;WAEG;QACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAClE,wCAAwC;YACxC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBACvB,OAAO,CAAC,CAAC;YACX,CAAC;YACD,wCAAwC;YACxC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBACvB,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC;YAED,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC7C,iDAAiD;YACjD,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC;gBAC3B,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBACrD,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBAErD,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC;oBAC3B,SAAS;gBACX,CAAC;qBAAM,IAAI,SAAS,EAAE,CAAC;oBACrB,OAAO,CAAC,CAAC;gBACX,CAAC;qBAAM,IAAI,SAAS,EAAE,CAAC;oBACrB,OAAO,CAAC,CAAC,CAAC;gBACZ,CAAC;gBACD,SAAS;YACX,CAAC;YACD,gDAAgD;YAChD,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,CAAC,CAAC;YACX,CAAC;YACD,gDAAgD;YAChD,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC;YAED,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACzC,iDAAiD;YACjD,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC;gBACnB,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBACrD,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;gBAErD,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC;oBAC3B,SAAS;gBACX,CAAC;qBAAM,IAAI,SAAS,EAAE,CAAC;oBACrB,OAAO,CAAC,CAAC;gBACX,CAAC;qBAAM,IAAI,SAAS,EAAE,CAAC;oBACrB,OAAO,CAAC,CAAC,CAAC;gBACZ,CAAC;gBAED,SAAS;YACX,CAAC;YACD,gDAAgD;YAChD,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,CAAC;YACX,CAAC;YACD,gDAAgD;YAChD,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC;QACH,CAAC;QAED;;;;;;;;;;;;;;WAcG;QACH,IAAI,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;YAChC,OAAO,CAAC,CAAC,CAAC;QACZ,CAAC;aAAM,IAAI,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;YACvC,OAAO,CAAC,CAAC;QACX,CAAC;QAED,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;IACzC,CAAC,CAAC;AACJ,CAAC;AAED,SAAgB,gBAAgB,CAC9B,IAAY,EACZ,KAAkB,EAClB,WAAoD,EACpD,IAAa;IAEb,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC,YAAY,CAAC;IACzE,MAAM,MAAM,GAAsC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAEtE,IAAI,IAAI,EAAE,CAAC;QACT,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IAED,KAAK,MAAM,IAAI,IAAI,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC;QACvC,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;gBAC1C,OAAO,CAAC,IAAI,CACV,WAAW,KAAK,CAAC,IAAI,iBAAiB,IAAI,qEAAqE,CAChH,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,MAAM,GAAG,WAAW,EAAE,cAAc,CAAC,IAAI,CAAC;gBAC9C,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;gBACpE,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAE9B,wCAAwC;YACxC,2FAA2F;YAC3F,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;AACzD,CAAC;AAED,SAAgB,SAAS,CAAC,IAAY;IACpC,IAAI,GAAG,IAAI;QACT,uBAAuB;QACvB,WAAW;SACV,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,+CAA+C;SACpE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,6BAA6B;SAChD,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,iDAAiD;IAE1E,sCAAsC;IACtC,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC;AAChD,CAAC;AAED,SAAgB,mBAAmB,CAAC,OAAe;IACjD,OAAO,IAAI,MAAM,CACf,KAAK,OAAO;SACT,KAAK,CAAC,GAAG,CAAC;SACV,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;QACV,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3C,OAAO,GAAG,EAAE,GAAG,CAAC;QAClB,CAAC;aAAM,IAAI,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,OAAO,cAAc,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;QACtD,CAAC;QAED,OAAO,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,8BAAM,EAAC,EAAE,CAAC,KAAK,CAAC;IAChD,CAAC,CAAC;SACD,IAAI,CAAC,EAAE,CAAC,GAAG,CACf,CAAC;AACJ,CAAC", "sourcesContent": ["import { InitialState } from '@react-navigation/native';\nimport escape from 'escape-string-regexp';\nimport * as queryString from 'query-string';\n\nimport type { InitialRouteConfig, Options, ParsedRoute, RouteConfig } from './getStateFromPath';\nimport { matchGroupName, stripGroupSegmentsFromPath } from '../matchers';\n\nexport type ExpoOptions = {\n  previousSegments?: string[];\n};\n\nexport type ExpoRouteConfig = {\n  type: 'static' | 'dynamic' | 'layout';\n  userReadableName: string;\n  isIndex: boolean;\n  isInitial?: boolean;\n  hasChildren: boolean;\n  expandedRouteNames: string[];\n  parts: string[];\n  staticPartCount: number;\n};\n\n/**\n * In Expo Router, the params are available at all levels of the routing config\n * @param routes\n * @returns\n */\nexport function populateParams(routes?: ParsedRoute[], params?: Record<string, any>) {\n  if (!routes || !params || Object.keys(params).length === 0) return;\n\n  for (const route of routes) {\n    Object.assign(route, { params });\n  }\n\n  return routes;\n}\n\nexport function safelyDecodeURIComponent(str: string) {\n  try {\n    return decodeURIComponent(str);\n  } catch {\n    return str;\n  }\n}\n\nexport function getUrlWithReactNavigationConcessions(\n  path: string,\n  baseUrl: string | undefined = process.env.EXPO_BASE_URL\n) {\n  let parsed: URL;\n  try {\n    parsed = new URL(path, 'https://phony.example');\n  } catch {\n    // Do nothing with invalid URLs.\n    return {\n      path,\n      cleanUrl: '',\n      nonstandardPathname: '',\n      url: new URL('https://phony.example'),\n    };\n  }\n\n  const pathname = parsed.pathname;\n  const withoutBaseUrl = stripBaseUrl(pathname, baseUrl);\n  const pathWithoutGroups = stripGroupSegmentsFromPath(stripBaseUrl(path, baseUrl));\n\n  // Make sure there is a trailing slash\n  return {\n    // The slashes are at the end, not the beginning\n    path,\n    nonstandardPathname: withoutBaseUrl.replace(/^\\/+/g, '').replace(/\\/+$/g, '') + '/',\n    url: parsed,\n    pathWithoutGroups,\n  };\n}\n\nexport function createConfig(\n  screen: string,\n  pattern: string,\n  routeNames: string[],\n  config: Record<string, any> = {}\n): Omit<ExpoRouteConfig, 'isInitial'> {\n  const parts: string[] = [];\n  let isDynamic = false;\n  const isIndex = screen === 'index' || screen.endsWith('/index');\n  let staticPartCount = 0;\n\n  for (const part of pattern.split('/')) {\n    if (part) {\n      // If any part is dynamic, then the route is dynamic\n      const isDynamicPart =\n        part.startsWith(':') || part.startsWith('*') || part.includes('*not-found');\n\n      isDynamic ||= isDynamicPart;\n\n      if (!matchGroupName(part)) {\n        parts.push(part);\n\n        if (!isDynamicPart) {\n          staticPartCount++;\n        }\n      }\n    }\n  }\n\n  const hasChildren = config.screens ? !!Object.keys(config.screens)?.length : false;\n  const type = hasChildren ? 'layout' : isDynamic ? 'dynamic' : 'static';\n\n  if (isIndex) {\n    parts.push('index');\n    staticPartCount++;\n  }\n\n  return {\n    type,\n    isIndex,\n    hasChildren,\n    parts,\n    staticPartCount,\n    userReadableName: [...routeNames.slice(0, -1), config.path || screen].join('/'),\n    // Don't include the __root route name\n    expandedRouteNames: routeNames.slice(1).flatMap((name) => {\n      return name.split('/');\n    }),\n  };\n}\n\nexport function assertScreens(options?: Options<object>): asserts options is Options<object> {\n  if (!options?.screens) {\n    throw Error(\"You must pass a 'screens' object to 'getStateFromPath' to generate a path.\");\n  }\n}\n\nexport function configRegExp(config: RouteConfig) {\n  return config.pattern\n    ? new RegExp(`^(${config.pattern.split('/').map(formatRegexPattern).join('')})$`)\n    : undefined;\n}\n\nexport function isDynamicPart(p: string) {\n  return p.length > 1 && (p.startsWith(':') || p.startsWith('*'));\n}\n\nexport function replacePart(p: string) {\n  return p.replace(/^[:*]/, '').replace(/\\?$/, '');\n}\n\nexport function getParamValue(p: string, value: string) {\n  if (p.startsWith('*')) {\n    const values = value.split('/').filter((v) => v !== '');\n    return values.length === 0 && p.endsWith('?') ? undefined : values;\n  } else {\n    return value;\n  }\n}\n\nfunction formatRegexPattern(it: string): string {\n  // Allow spaces in file path names.\n  it = it.replace(' ', '%20');\n\n  if (it.startsWith(':')) {\n    // TODO: Remove unused match group\n    return `(([^/]+\\\\/)${it.endsWith('?') ? '?' : ''})`;\n  } else if (it.startsWith('*')) {\n    return `((.*\\\\/)${it.endsWith('?') ? '?' : ''})`;\n  }\n\n  // Strip groups from the matcher\n  if (matchGroupName(it) != null) {\n    // Groups are optional segments\n    // this enables us to match `/bar` and `/(foo)/bar` for the same route\n    // NOTE(EvanBacon): Ignore this match in the regex to avoid capturing the group\n    return `(?:${escape(it)}\\\\/)?`;\n  }\n\n  return escape(it) + `\\\\/`;\n}\n\nexport function handleUrlParams(route: ParsedRoute, params?: queryString.ParsedQuery) {\n  if (params) {\n    route.params = Object.assign(Object.create(null), route.params) as Record<string, any>;\n    for (const [name, value] of Object.entries(params)) {\n      if (route.params?.[name]) {\n        if (process.env.NODE_ENV !== 'production') {\n          console.warn(\n            `Route '/${route.name}' with param '${name}' was specified both in the path and as a param, removing from path`\n          );\n        }\n      }\n\n      if (!route.params?.[name]) {\n        route.params[name] = value;\n        continue;\n      }\n    }\n\n    if (Object.keys(route.params).length === 0) {\n      delete route.params;\n    }\n  }\n}\n\nexport function spreadParamsAcrossAllStates(state: InitialState, params?: Record<string, any>) {\n  while (state) {\n    const route = state.routes[0];\n    (route as any).params = Object.assign({}, route.params, params);\n  }\n}\n\nexport function stripBaseUrl(\n  path: string,\n  baseUrl: string | undefined = process.env.EXPO_BASE_URL\n) {\n  if (process.env.NODE_ENV !== 'development') {\n    if (baseUrl) {\n      return path.replace(/^\\/+/g, '/').replace(new RegExp(`^\\\\/?${escape(baseUrl)}`, 'g'), '');\n    }\n  }\n  return path;\n}\n\nexport function matchForEmptyPath(configs: RouteConfig[]) {\n  // We need to add special handling of empty path so navigation to empty path also works\n  // When handling empty path, we should only look at the root level config\n\n  // NOTE(EvanBacon): We only care about matching leaf nodes.\n  const leafNodes = configs\n    .filter((config) => !config.hasChildren)\n    .map((value) => {\n      return {\n        ...value,\n        // Collapse all levels of group segments before testing.\n        // This enables `app/(one)/(two)/index.js` to be matched.\n        path: stripGroupSegmentsFromPath(value.path),\n      };\n    });\n\n  const match =\n    leafNodes.find(\n      (config) =>\n        // NOTE(EvanBacon): Test leaf node index routes that either don't have a regex or match an empty string.\n        config.path === '' && (!config.regex || config.regex.test(''))\n    ) ??\n    leafNodes.find(\n      (config) =>\n        // NOTE(EvanBacon): Test leaf node dynamic routes that match an empty string.\n        config.path.startsWith(':') && config.regex!.test('')\n    ) ??\n    // NOTE(EvanBacon): Test leaf node deep dynamic routes that match a slash.\n    // This should be done last to enable dynamic routes having a higher priority.\n    leafNodes.find((config) => config.path.startsWith('*') && config.regex!.test('/'));\n\n  return match;\n}\n\nexport function appendIsInitial(initialRoutes: InitialRouteConfig[]) {\n  const resolvedInitialPatterns = initialRoutes.map((route) =>\n    joinPaths(...route.parentScreens, route.initialRouteName)\n  );\n\n  return function (config: RouteConfig) {\n    // TODO(EvanBacon): Probably a safer way to do this\n    // Mark initial routes to give them potential priority over other routes that match.\n    config.isInitial = resolvedInitialPatterns.includes(config.routeNames.join('/'));\n    return config;\n  };\n}\n\nconst joinPaths = (...paths: string[]): string =>\n  ([] as string[])\n    .concat(...paths.map((p) => p.split('/')))\n    .filter(Boolean)\n    .join('/');\n\nexport function getRouteConfigSorter(previousSegments: string[] = []) {\n  return function sortConfigs(a: RouteConfig, b: RouteConfig) {\n    // Sort config so that:\n    // - the most exhaustive ones are always at the beginning\n    // - patterns with wildcard are always at the end\n\n    // If 2 patterns are same, move the one with less route names up\n    // This is an error state, so it's only useful for consistent error messages\n    if (a.pattern === b.pattern) {\n      return b.routeNames.join('>').localeCompare(a.routeNames.join('>'));\n    }\n\n    /*\n     * If one of the patterns starts with the other, it is earlier in the config sorting.\n     * However, configs are a mix of route configs and layout configs\n     * e.g There will be a config for `/(group)`, but maybe there isn't a `/(group)/index.tsx`\n     *\n     * This is because you can navigate to a directory and its navigator will determine the route\n     * These routes should be later in the config sorting, as their patterns are very open\n     * and will prevent routes from being matched\n     *\n     * Therefore before we compare segment parts, we force these layout configs later in the sorting\n     *\n     * NOTE(marklawlor): Is this a feature we want? I'm unsure if this is a gimmick or a feature.\n     */\n    if (a.pattern.startsWith(b.pattern) && !b.isIndex) {\n      return -1;\n    }\n\n    if (b.pattern.startsWith(a.pattern) && !a.isIndex) {\n      return 1;\n    }\n\n    /*\n     * Static routes should always be higher than dynamic and layout routes.\n     */\n    if (a.type === 'static' && b.type !== 'static') {\n      return -1;\n    } else if (a.type !== 'static' && b.type === 'static') {\n      return 1;\n    }\n\n    /*\n     * If the routes have any static segments, the one the most static segments should be higher\n     */\n    if (a.staticPartCount !== b.staticPartCount) {\n      return b.staticPartCount - a.staticPartCount;\n    }\n\n    /*\n     * If both are static/dynamic or a layout file, then we check group similarity\n     */\n    const similarToPreviousA = previousSegments.filter((value, index) => {\n      return value === a.expandedRouteNames[index] && value.startsWith('(') && value.endsWith(')');\n    });\n\n    const similarToPreviousB = previousSegments.filter((value, index) => {\n      return value === b.expandedRouteNames[index] && value.startsWith('(') && value.endsWith(')');\n    });\n\n    if (\n      (similarToPreviousA.length > 0 || similarToPreviousB.length > 0) &&\n      similarToPreviousA.length !== similarToPreviousB.length\n    ) {\n      // One matches more than the other, so pick the one that matches more\n      return similarToPreviousB.length - similarToPreviousA.length;\n    }\n\n    /*\n     * If there is not difference in similarity, then each non-group segment is compared against each other\n     */\n    for (let i = 0; i < Math.max(a.parts.length, b.parts.length); i++) {\n      // if b is longer, b get higher priority\n      if (a.parts[i] == null) {\n        return 1;\n      }\n      // if a is longer, a get higher priority\n      if (b.parts[i] == null) {\n        return -1;\n      }\n\n      const aWildCard = a.parts[i].startsWith('*');\n      const bWildCard = b.parts[i].startsWith('*');\n      // if both are wildcard we compare next component\n      if (aWildCard && bWildCard) {\n        const aNotFound = a.parts[i].match(/^[*]not-found$/);\n        const bNotFound = b.parts[i].match(/^[*]not-found$/);\n\n        if (aNotFound && bNotFound) {\n          continue;\n        } else if (aNotFound) {\n          return 1;\n        } else if (bNotFound) {\n          return -1;\n        }\n        continue;\n      }\n      // if only a is wild card, b get higher priority\n      if (aWildCard) {\n        return 1;\n      }\n      // if only b is wild card, a get higher priority\n      if (bWildCard) {\n        return -1;\n      }\n\n      const aSlug = a.parts[i].startsWith(':');\n      const bSlug = b.parts[i].startsWith(':');\n      // if both are wildcard we compare next component\n      if (aSlug && bSlug) {\n        const aNotFound = a.parts[i].match(/^[*]not-found$/);\n        const bNotFound = b.parts[i].match(/^[*]not-found$/);\n\n        if (aNotFound && bNotFound) {\n          continue;\n        } else if (aNotFound) {\n          return 1;\n        } else if (bNotFound) {\n          return -1;\n        }\n\n        continue;\n      }\n      // if only a is wild card, b get higher priority\n      if (aSlug) {\n        return 1;\n      }\n      // if only b is wild card, a get higher priority\n      if (bSlug) {\n        return -1;\n      }\n    }\n\n    /*\n     * Both configs are identical in specificity and segments count/type\n     * Try and sort by initial instead.\n     *\n     * TODO: We don't differentiate between the default initialRoute and group specific default routes\n     *\n     * const unstable_settings = {\n     *   \"group\": {\n     *     initialRouteName: \"article\"\n     *  }\n     * }\n     *\n     * \"article\" will be ranked higher because its an initialRoute for a group - even if not your not currently in\n     * that group. The current work around is to ways provide initialRouteName for all groups\n     */\n    if (a.isInitial && !b.isInitial) {\n      return -1;\n    } else if (!a.isInitial && b.isInitial) {\n      return 1;\n    }\n\n    return b.parts.length - a.parts.length;\n  };\n}\n\nexport function parseQueryParams(\n  path: string,\n  route: ParsedRoute,\n  parseConfig?: Record<string, (value: string) => any>,\n  hash?: string\n) {\n  const searchParams = new URL(path, 'https://phony.example').searchParams;\n  const params: Record<string, string | string[]> = Object.create(null);\n\n  if (hash) {\n    params['#'] = hash.slice(1);\n  }\n\n  for (const name of searchParams.keys()) {\n    if (route.params?.[name]) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\n          `Route '/${route.name}' with param '${name}' was specified both in the path and as a param, removing from path`\n        );\n      }\n    } else {\n      const values = parseConfig?.hasOwnProperty(name)\n        ? searchParams.getAll(name).map((value) => parseConfig[name](value))\n        : searchParams.getAll(name);\n\n      // searchParams.getAll returns an array.\n      // if we only have a single value, and its not an array param, we need to extract the value\n      params[name] = values.length === 1 ? values[0] : values;\n    }\n  }\n\n  return Object.keys(params).length ? params : undefined;\n}\n\nexport function cleanPath(path: string) {\n  path = path\n    // let remaining = path\n    // END FORK\n    .replace(/\\/+/g, '/') // Replace multiple slash (//) with single ones\n    .replace(/^\\//, '') // Remove extra leading slash\n    .replace(/\\?.*$/, ''); // Remove query params which we will handle later\n\n  // Make sure there is a trailing slash\n  return path.endsWith('/') ? path : `${path}/`;\n}\n\nexport function routePatternToRegex(pattern: string) {\n  return new RegExp(\n    `^(${pattern\n      .split('/')\n      .map((it) => {\n        if (it.startsWith('(') && it.endsWith(')')) {\n          return `${it}?`;\n        } else if (it.startsWith(':')) {\n          return `(([^/]+\\\\/)${it.endsWith('?') ? '?' : ''})`;\n        }\n\n        return `${it === '*' ? '.*' : escape(it)}\\\\/`;\n      })\n      .join('')})`\n  );\n}\n"]}