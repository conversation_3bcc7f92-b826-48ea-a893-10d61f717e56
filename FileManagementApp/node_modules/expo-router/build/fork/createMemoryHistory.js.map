{"version": 3, "file": "createMemoryHistory.js", "sourceRoot": "", "sources": ["../../src/fork/createMemoryHistory.ts"], "names": [], "mappings": ";;AAgBA,kDAqNC;AAhOD,kDAA2C;AAW3C,SAAgB,mBAAmB;IACjC,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,KAAK,GAAoB,EAAE,CAAC;IAEhC,wCAAwC;IACxC,6FAA6F;IAC7F,MAAM,OAAO,GAA4D,EAAE,CAAC;IAE5E,MAAM,SAAS,GAAG,GAAG,EAAE;QACrB,gFAAgF;QAChF,uFAAuF;QACvF,oEAAoE;QACpE,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YACrB,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;YACjB,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,OAAO,GAAG;QACd,IAAI,KAAK;YACP,kDAAkD;YAClD,oEAAoE;YACpE,MAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;YAEpC,IAAI,EAAE,EAAE,CAAC;gBACP,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;gBAExD,OAAO,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;YAED,OAAO,CAAC,CAAC;QACX,CAAC;QAED,GAAG,CAAC,KAAa;YACf,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;QAED,SAAS,CAAC,EAAE,IAAI,EAAoB;YAClC,8FAA8F;YAC9F,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACpC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEtB,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;oBACvB,OAAO,CAAC,CAAC;gBACX,CAAC;YACH,CAAC;YAED,OAAO,CAAC,CAAC,CAAC;QACZ,CAAC;QAED,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAA4C;YAC5D,SAAS,EAAE,CAAC;YAEZ,MAAM,EAAE,GAAG,IAAA,mBAAM,GAAE,CAAC;YAEpB,wFAAwF;YACxF,6EAA6E;YAC7E,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAElC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;YAChC,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;YAEzB,oFAAoF;YACpF,wDAAwD;YACxD,6EAA6E;YAC7E,yEAAyE;YACzE,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAA4C;YAC/D,SAAS,EAAE,CAAC;YAEZ,MAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,IAAI,IAAA,mBAAM,GAAE,CAAC;YAEhD,gFAAgF;YAChF,kDAAkD;YAClD,IAAI,YAAY,GAAG,IAAI,CAAC;YACxB,MAAM,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;YAE7D,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;gBACnE,8EAA8E;gBAC9E,uFAAuF;gBACvF,yFAAyF;gBACzF,iDAAiD;gBACjD,4DAA4D;gBAC5D,+DAA+D;gBAE/D,YAAY,GAAG,YAAY,GAAG,IAAI,CAAC;gBACnC,KAAK,GAAG,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC5C,KAAK,GAAG,CAAC,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACN,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;oBAC/B,YAAY,GAAG,YAAY,GAAG,IAAI,CAAC;gBACrC,CAAC;gBACD,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;YACrC,CAAC;YAED,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC;QACxD,CAAC;QAED,+EAA+E;QAC/E,oFAAoF;QACpF,8FAA8F;QAC9F,oFAAoF;QACpF,iGAAiG;QACjG,EAAE,CAAC,CAAS;YACV,SAAS,EAAE,CAAC;YAEZ,wIAAwI;YACxI,+IAA+I;YAC/I,MAAM,SAAS,GAAG,KAAK,GAAG,CAAC,CAAC;YAC5B,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;YACvC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC/B,+HAA+H;gBAC/H,CAAC,GAAG,CAAC,KAAK,CAAC;gBACX,KAAK,GAAG,CAAC,CAAC;YACZ,CAAC;iBAAM,IAAI,CAAC,GAAG,CAAC,IAAI,SAAS,GAAG,aAAa,EAAE,CAAC;gBAC9C,+GAA+G;gBAC/G,CAAC,GAAG,aAAa,GAAG,KAAK,CAAC;gBAC1B,KAAK,GAAG,aAAa,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,SAAS,CAAC;YACpB,CAAC;YAED,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACZ,OAAO;YACT,CAAC;YAED,qFAAqF;YACrF,gDAAgD;YAChD,6EAA6E;YAC7E,uFAAuF;YACvF,wFAAwF;YACxF,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC3C,MAAM,IAAI,GAAG,CAAC,WAAqB,EAAE,EAAE;oBACrC,YAAY,CAAC,KAAK,CAAC,CAAC;oBAEpB,IAAI,WAAW,EAAE,CAAC;wBAChB,MAAM,CAAC,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC,CAAC;wBAC5D,OAAO;oBACT,CAAC;oBAED,iEAAiE;oBACjE,0EAA0E;oBAC1E,mEAAmE;oBACnE,yCAAyC;oBACzC,sFAAsF;oBACtF,6DAA6D;oBAC7D,sFAAsF;oBACtF,yEAAyE;oBACzE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC;oBAElC,MAAM,CAAC,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC;oBAC3B,MAAM,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;oBAE9B,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC;gBAEF,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;gBAEtC,wEAAwE;gBACxE,qEAAqE;gBACrE,oEAAoE;gBACpE,6EAA6E;gBAC7E,kFAAkF;gBAClF,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC5B,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC;oBAEzD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;wBACpB,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;oBAC3B,CAAC;gBACH,CAAC,EAAE,GAAG,CAAC,CAAC;gBAER,MAAM,UAAU,GAAG,GAAG,EAAE;oBACtB,MAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;oBACpC,MAAM,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;oBAE/D,iDAAiD;oBACjD,2DAA2D;oBAC3D,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;oBAElC,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;oBAE3B,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;oBACnD,IAAI,EAAE,EAAE,EAAE,CAAC;gBACb,CAAC,CAAC;gBAEF,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;gBAChD,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;QACL,CAAC;QAED,gGAAgG;QAChG,iFAAiF;QACjF,4GAA4G;QAC5G,MAAM,CAAC,QAAoB;YACzB,MAAM,UAAU,GAAG,GAAG,EAAE;gBACtB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACnB,wEAAwE;oBACxE,OAAO;gBACT,CAAC;gBAED,QAAQ,EAAE,CAAC;YACb,CAAC,CAAC;YAEF,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAEhD,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAClE,CAAC;KACF,CAAC;IAEF,OAAO,OAAO,CAAC;AACjB,CAAC", "sourcesContent": ["// Forked from React Navigation in order to use a custom `useLinking` function.\n// https://github.com/react-navigation/react-navigation/blob/main/packages/native/src/createMemoryHistory.tsx\n// Look for 'START OF FORK' comments\n// Currently no forked behaviour.\nimport type { NavigationState } from '@react-navigation/core';\nimport { nanoid } from 'nanoid/non-secure';\n\ntype HistoryRecord = {\n  // Unique identifier for this record to match it with window.history.state\n  id: string;\n  // Navigation state object for the history entry\n  state: NavigationState;\n  // Path of the history entry\n  path: string;\n};\n\nexport function createMemoryHistory() {\n  let index = 0;\n  let items: HistoryRecord[] = [];\n\n  // Pending callbacks for `history.go(n)`\n  // We might modify the callback stored if it was interrupted, so we have a ref to identify it\n  const pending: { ref: unknown; cb: (interrupted?: boolean) => void }[] = [];\n\n  const interrupt = () => {\n    // If another history operation was performed we need to interrupt existing ones\n    // This makes sure that calls such as `history.replace` after `history.go` don't happen\n    // Since otherwise it won't be correct if something else has changed\n    pending.forEach((it) => {\n      const cb = it.cb;\n      it.cb = () => cb(true);\n    });\n  };\n\n  const history = {\n    get index(): number {\n      // We store an id in the state instead of an index\n      // Index could get out of sync with in-memory values if page reloads\n      const id = window.history.state?.id;\n\n      if (id) {\n        const index = items.findIndex((item) => item.id === id);\n\n        return index > -1 ? index : 0;\n      }\n\n      return 0;\n    },\n\n    get(index: number) {\n      return items[index];\n    },\n\n    backIndex({ path }: { path: string }) {\n      // We need to find the index from the element before current to get closest path to go back to\n      for (let i = index - 1; i >= 0; i--) {\n        const item = items[i];\n\n        if (item.path === path) {\n          return i;\n        }\n      }\n\n      return -1;\n    },\n\n    push({ path, state }: { path: string; state: NavigationState }) {\n      interrupt();\n\n      const id = nanoid();\n\n      // When a new entry is pushed, all the existing entries after index will be inaccessible\n      // So we remove any existing entries after the current index to clean them up\n      items = items.slice(0, index + 1);\n\n      items.push({ path, state, id });\n      index = items.length - 1;\n\n      // We pass empty string for title because it's ignored in all browsers except safari\n      // We don't store state object in history.state because:\n      // - browsers have limits on how big it can be, and we don't control the size\n      // - while not recommended, there could be non-serializable data in state\n      window.history.pushState({ id }, '', path);\n    },\n\n    replace({ path, state }: { path: string; state: NavigationState }) {\n      interrupt();\n\n      const id = window.history.state?.id ?? nanoid();\n\n      // Need to keep the hash part of the path if there was no previous history entry\n      // or the previous history entry had the same path\n      let pathWithHash = path;\n      const hash = pathWithHash.includes('#') ? '' : location.hash;\n\n      if (!items.length || items.findIndex((item) => item.id === id) < 0) {\n        // There are two scenarios for creating an array with only one history record:\n        // - When loaded id not found in the items array, this function by default will replace\n        //   the first item. We need to keep only the new updated object, otherwise it will break\n        //   the page when navigating forward in history.\n        // - This is the first time any state modifications are done\n        //   So we need to push the entry as there's nothing to replace\n\n        pathWithHash = pathWithHash + hash;\n        items = [{ path: pathWithHash, state, id }];\n        index = 0;\n      } else {\n        if (items[index].path === path) {\n          pathWithHash = pathWithHash + hash;\n        }\n        items[index] = { path, state, id };\n      }\n\n      window.history.replaceState({ id }, '', pathWithHash);\n    },\n\n    // `history.go(n)` is asynchronous, there are couple of things to keep in mind:\n    // - it won't do anything if we can't go `n` steps, the `popstate` event won't fire.\n    // - each `history.go(n)` call will trigger a separate `popstate` event with correct location.\n    // - the `popstate` event fires before the next frame after calling `history.go(n)`.\n    // This method differs from `history.go(n)` in the sense that it'll go back as many steps it can.\n    go(n: number) {\n      interrupt();\n\n      // To guard against unexpected navigation out of the app we will assume that browser history is only as deep as the length of our memory\n      // history. If we don't have an item to navigate to then update our index and navigate as far as we can without taking the user out of the app.\n      const nextIndex = index + n;\n      const lastItemIndex = items.length - 1;\n      if (n < 0 && !items[nextIndex]) {\n        // Attempted to navigate beyond the first index. Negating the current index will align the browser history with the first item.\n        n = -index;\n        index = 0;\n      } else if (n > 0 && nextIndex > lastItemIndex) {\n        // Attempted to navigate past the last index. Calculate how many indices away from the last index and go there.\n        n = lastItemIndex - index;\n        index = lastItemIndex;\n      } else {\n        index = nextIndex;\n      }\n\n      if (n === 0) {\n        return;\n      }\n\n      // When we call `history.go`, `popstate` will fire when there's history to go back to\n      // So we need to somehow handle following cases:\n      // - There's history to go back, `history.go` is called, and `popstate` fires\n      // - `history.go` is called multiple times, we need to resolve on respective `popstate`\n      // - No history to go back, but `history.go` was called, browser has no API to detect it\n      return new Promise<void>((resolve, reject) => {\n        const done = (interrupted?: boolean) => {\n          clearTimeout(timer);\n\n          if (interrupted) {\n            reject(new Error('History was changed during navigation.'));\n            return;\n          }\n\n          // There seems to be a bug in Chrome regarding updating the title\n          // If we set a title just before calling `history.go`, the title gets lost\n          // However the value of `document.title` is still what we set it to\n          // It's just not displayed in the tab bar\n          // To update the tab bar, we need to reset the title to something else first (e.g. '')\n          // And set the title to what it was before so it gets applied\n          // It won't work without setting it to empty string coz otherwise title isn't changing\n          // Which means that the browser won't do anything after setting the title\n          const { title } = window.document;\n\n          window.document.title = '';\n          window.document.title = title;\n\n          resolve();\n        };\n\n        pending.push({ ref: done, cb: done });\n\n        // If navigation didn't happen within 100ms, assume that it won't happen\n        // This may not be accurate, but hopefully it won't take so much time\n        // In Chrome, navigation seems to happen instantly in next microtask\n        // But on Firefox, it seems to take much longer, around 50ms from our testing\n        // We're using a hacky timeout since there doesn't seem to be way to know for sure\n        const timer = setTimeout(() => {\n          const index = pending.findIndex((it) => it.ref === done);\n\n          if (index > -1) {\n            pending[index].cb();\n            pending.splice(index, 1);\n          }\n        }, 100);\n\n        const onPopState = () => {\n          const id = window.history.state?.id;\n          const currentIndex = items.findIndex((item) => item.id === id);\n\n          // Fix createMemoryHistory.index variable's value\n          // as it may go out of sync when navigating in the browser.\n          index = Math.max(currentIndex, 0);\n\n          const last = pending.pop();\n\n          window.removeEventListener('popstate', onPopState);\n          last?.cb();\n        };\n\n        window.addEventListener('popstate', onPopState);\n        window.history.go(n);\n      });\n    },\n\n    // The `popstate` event is triggered when history changes, except `pushState` and `replaceState`\n    // If we call `history.go(n)` ourselves, we don't want it to trigger the listener\n    // Here we normalize it so that only external changes (e.g. user pressing back/forward) trigger the listener\n    listen(listener: () => void) {\n      const onPopState = () => {\n        if (pending.length) {\n          // This was triggered by `history.go(n)`, we shouldn't call the listener\n          return;\n        }\n\n        listener();\n      };\n\n      window.addEventListener('popstate', onPopState);\n\n      return () => window.removeEventListener('popstate', onPopState);\n    },\n  };\n\n  return history;\n}\n"]}