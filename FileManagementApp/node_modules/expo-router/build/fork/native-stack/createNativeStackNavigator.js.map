{"version": 3, "file": "createNativeStackNavigator.js", "sourceRoot": "", "sources": ["../../../src/fork/native-stack/createNativeStackNavigator.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFA,gEAiBC;AArGD,qDAakC;AAClC,iEAMwC;AACxC,6CAA+B;AAE/B,SAAS,oBAAoB,CAAC,EAC5B,EAAE,EACF,gBAAgB,EAChB,QAAQ,EACR,MAAM,EACN,eAAe,EACf,aAAa,EACb,YAAY,EACZ,GAAG,IAAI,EACmB;IAC1B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,EAAE,GAAG,IAAA,6BAAoB,EAM1F,oBAAW,EAAE;QACb,EAAE;QACF,gBAAgB;QAChB,QAAQ;QACR,MAAM;QACN,eAAe;QACf,aAAa;QACb,YAAY;KACb,CAAC,CAAC;IAEH,KAAK,CAAC,SAAS,CACb,GAAG,EAAE;IACH,+DAA+D;IAC/D,UAAU,EAAE,WAAW,EAAE,CAAC,UAAU,EAAE,CAAC,CAAM,EAAE,EAAE;QAC/C,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;QAEzC,gFAAgF;QAChF,gEAAgE;QAChE,qBAAqB,CAAC,GAAG,EAAE;YACzB,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,IAAI,SAAS,IAAI,CAAE,CAAgC,CAAC,gBAAgB,EAAE,CAAC;gBACxF,kEAAkE;gBAClE,gDAAgD;gBAChD,UAAU,CAAC,QAAQ,CAAC;oBAClB,GAAG,qBAAY,CAAC,QAAQ,EAAE;oBAC1B,MAAM,EAAE,KAAK,CAAC,GAAG;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,EACJ,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CACrC,CAAC;IAEF,OAAO,CACL,CAAC,iBAAiB,CAChB;MAAA,CAAC,8BAAe,CACd,IAAI,IAAI,CAAC,CACT,KAAK,CAAC,CAAC,KAAK,CAAC,CACb,UAAU,CAAC,CAAC,UAAU,CAAC,CACvB,WAAW,CAAC,CAAC,WAAW,CAAC,CACzB,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAEvB;IAAA,EAAE,iBAAiB,CAAC,CACrB,CAAC;AACJ,CAAC;AAED,SAAgB,0BAA0B,CAexC,MAAe;IACf,OAAO,IAAA,+BAAsB,EAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC;AAC9D,CAAC", "sourcesContent": ["import {\n  createNavi<PERSON>orFactory,\n  type EventArg,\n  type NavigatorTypeBagBase,\n  type ParamListBase,\n  type StackActionHelpers,\n  StackActions,\n  type StackN<PERSON><PERSON>State,\n  StackRouter,\n  type StackRouterOptions,\n  type StaticConfig,\n  type TypedNavigator,\n  useNavigationBuilder,\n} from '@react-navigation/native';\nimport {\n  NativeStackNavigatorProps,\n  NativeStackNavigationOptions,\n  NativeStackNavigationEventMap,\n  NativeStackView,\n  NativeStackNavigationProp,\n} from '@react-navigation/native-stack';\nimport * as React from 'react';\n\nfunction NativeStackNavigator({\n  id,\n  initialRouteName,\n  children,\n  layout,\n  screenListeners,\n  screenOptions,\n  screenLayout,\n  ...rest\n}: NativeStackNavigatorProps) {\n  const { state, describe, descriptors, navigation, NavigationContent } = useNavigationBuilder<\n    StackNavigationState<ParamListBase>,\n    StackRouterOptions,\n    StackActionHelpers<ParamListBase>,\n    NativeStackNavigationOptions,\n    NativeStackNavigationEventMap\n  >(StackRouter, {\n    id,\n    initialRouteName,\n    children,\n    layout,\n    screenListeners,\n    screenOptions,\n    screenLayout,\n  });\n\n  React.useEffect(\n    () =>\n      // @ts-expect-error: there may not be a tab navigator in parent\n      navigation?.addListener?.('tabPress', (e: any) => {\n        const isFocused = navigation.isFocused();\n\n        // Run the operation in the next frame so we're sure all listeners have been run\n        // This is necessary to know if preventDefault() has been called\n        requestAnimationFrame(() => {\n          if (state.index > 0 && isFocused && !(e as EventArg<'tabPress', true>).defaultPrevented) {\n            // When user taps on already focused tab and we're inside the tab,\n            // reset the stack to replicate native behaviour\n            navigation.dispatch({\n              ...StackActions.popToTop(),\n              target: state.key,\n            });\n          }\n        });\n      }),\n    [navigation, state.index, state.key]\n  );\n\n  return (\n    <NavigationContent>\n      <NativeStackView\n        {...rest}\n        state={state}\n        navigation={navigation}\n        descriptors={descriptors}\n        describe={describe}\n      />\n    </NavigationContent>\n  );\n}\n\nexport function createNativeStackNavigator<\n  const ParamList extends ParamListBase,\n  const NavigatorID extends string | undefined = undefined,\n  const TypeBag extends NavigatorTypeBagBase = {\n    ParamList: ParamList;\n    NavigatorID: NavigatorID;\n    State: StackNavigationState<ParamList>;\n    ScreenOptions: NativeStackNavigationOptions;\n    EventMap: NativeStackNavigationEventMap;\n    NavigationList: {\n      [RouteName in keyof ParamList]: NativeStackNavigationProp<ParamList, RouteName, NavigatorID>;\n    };\n    Navigator: typeof NativeStackNavigator;\n  },\n  const Config extends StaticConfig<TypeBag> = StaticConfig<TypeBag>,\n>(config?: Config): TypedNavigator<TypeBag, Config> {\n  return createNavigatorFactory(NativeStackNavigator)(config);\n}\n"]}