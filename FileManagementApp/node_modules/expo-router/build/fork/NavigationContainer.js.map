{"version": 3, "file": "NavigationContainer.js", "sourceRoot": "", "sources": ["../../src/fork/NavigationContainer.tsx"], "names": [], "mappings": ";;;;;;AAAA,qDAkBkC;AAClC,kDAA0B;AAC1B,+CAA2C;AAC3C,8EAAoD;AAEpD,mDAAgD;AAChD,yDAAsD;AACtD,6CAA0C;AAC1C,+CAA4C;AAU5C,UAAU,CAAC,yBAAyB,GAAG,IAAI,OAAO,EAAE,CAAC;AASrD;;;;;;;;;;;;;;;GAeG;AACH,SAAS,wBAAwB,CAC/B,EACE,SAAS,GAAG,0BAAW,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAC5D,KAAK,GAAG,qBAAY,EACpB,OAAO,EACP,QAAQ,GAAG,IAAI,EACf,aAAa,EACb,OAAO,EACP,aAAa,EACb,GAAG,IAAI,EACc,EACvB,GAA6D;IAE7D,MAAM,gBAAgB,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;IAErE,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;QACpB,IAAA,2BAAkB,EAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED,MAAM,YAAY,GAAG,eAAK,CAAC,MAAM,CAA+C,IAAI,CAAC,CAAC;IAEtF,IAAA,6BAAa,EAAC,YAAY,CAAC,CAAC;IAC5B,IAAA,mCAAgB,EAAC,YAAY,EAAE,aAAa,CAAC,CAAC;IAE9C,MAAM,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,GAAG,eAAK,CAAC,QAAQ,EAAsB,CAAC;IAEvF,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,uBAAU,EACpC,YAAY,EACZ;QACE,OAAO,EAAE,gBAAgB;QACzB,QAAQ,EAAE,EAAE;QACZ,GAAG,OAAO;KACX,EACD,oBAAoB,CACrB,CAAC;IAEF,MAAM,cAAc,GAAG,eAAK,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAE9E,MAAM,uBAAuB,GAAG,eAAK,CAAC,OAAO,CAC3C,GAAG,EAAE,CAAC,CAAC,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,CAAC,EACnD,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,CAC1C,CAAC;IAEF,MAAM,yBAAyB,GAAG,IAAA,6BAAiB,EAAC,GAAG,EAAE;QACvD,mEAAmE;QACnE,MAAM,IAAI,GAAG,YAAY,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,IAAI,CAAC;QAC3D,oBAAoB,CAAC,CAAC,yBAAyB,EAAE,EAAE;YACjD,IAAI,yBAAyB,KAAK,IAAI,EAAE,CAAC;gBACvC,OAAO,SAAS,CAAC;YACnB,CAAC;YACD,OAAO,yBAAyB,CAAC;QACnC,CAAC,CAAC,CAAC;QACH,OAAO,EAAE,EAAE,CAAC;IACd,CAAC,CAAC,CAAC;IAEH,MAAM,+BAA+B,GAAG,IAAA,6BAAiB,EACvD,CAAC,KAA4C,EAAE,EAAE;QAC/C,mEAAmE;QACnE,MAAM,IAAI,GAAG,YAAY,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,IAAI,CAAC;QAC3D,oBAAoB,CAAC,CAAC,yBAAyB,EAAE,EAAE;YACjD,IAAI,yBAAyB,KAAK,IAAI,EAAE,CAAC;gBACvC,OAAO,SAAS,CAAC;YACnB,CAAC;YACD,OAAO,yBAAyB,CAAC;QACnC,CAAC,CAAC,CAAC;QACH,aAAa,EAAE,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC,CACF,CAAC;IACF,iDAAiD;IACjD,oCAAoC;IACpC,eAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;YACzB,yBAAyB,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE;gBAClD,IAAI,OAAO;oBACT,OAAO;wBACL,GAAG,OAAO;wBACV,OAAO,EAAE,gBAAgB;wBACzB,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE;wBACjC,gBAAgB,EAAE,OAAO,EAAE,gBAAgB,IAAI,yBAAgB;wBAC/D,gBAAgB,EAAE,OAAO,EAAE,gBAAgB,IAAI,yBAAgB;wBAC/D,kBAAkB,EAAE,OAAO,EAAE,kBAAkB,IAAI,2BAAkB;qBACtE,CAAC;gBACJ,CAAC;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,GAAG,IAAA,yBAAW,EAAC,eAAe,CAAC,CAAC;IAEhE,eAAK,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,OAAQ,CAAC,CAAC;IAE5D,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,IAAI,CAAC,gBAAgB,IAAI,UAAU,CAAC;IAEpF,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,6DAA6D;QAC7D,qEAAqE;QACrE,OAAO,CAAC,sBAAa,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,sBAAa,CAAC,CAAC;IACjE,CAAC;IAED,OAAO,CACL,CAAC,yBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAC1C;MAAA,CAAC,yCAAuB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,uBAAuB,CAAC,CAC/D;QAAA,CAAC,uBAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,CAC7C;UAAA,CAAC,gCAAuB,CACtB,IAAI,IAAI,CAAC,CACT,KAAK,CAAC,CAAC,KAAK,CAAC,CACb,OAAO,CAAC,CAAC,yBAAyB,CAAC,CACnC,aAAa,CAAC,CAAC,+BAA+B,CAAC,CAC/C,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAC3E,GAAG,CAAC,CAAC,YAAY,CAAC,EAEtB;QAAA,EAAE,uBAAc,CAAC,QAAQ,CAC3B;MAAA,EAAE,yCAAuB,CAAC,QAAQ,CACpC;IAAA,EAAE,yBAAgB,CAAC,QAAQ,CAAC,CAC7B,CAAC;AACJ,CAAC;AAEY,QAAA,mBAAmB,GAAG,eAAK,CAAC,UAAU,CAAC,wBAAwB,CAMrD,CAAC", "sourcesContent": ["import {\n  BaseNavigationContainer,\n  DefaultTheme,\n  DocumentTitleOptions,\n  LinkingContext,\n  LinkingOptions,\n  LocaleDirContext,\n  LocaleDirection,\n  NavigationContainerProps,\n  NavigationContainerRef,\n  NavigationState,\n  ParamListBase,\n  ThemeProvider,\n  UNSTABLE_UnhandledLinkingContext as UnhandledLinkingContext,\n  getActionFromState,\n  getPathFromState,\n  getStateFromPath,\n  validatePathConfig,\n} from '@react-navigation/native';\nimport React from 'react';\nimport { I18nManager } from 'react-native';\nimport useLatestCallback from 'use-latest-callback';\n\nimport { useBackButton } from './useBackButton';\nimport { useDocumentTitle } from './useDocumentTitle';\nimport { useLinking } from './useLinking';\nimport { useThenable } from './useThenable';\n\ndeclare global {\n  // eslint-disable-next-line no-var\n  var REACT_NAVIGATION_DEVTOOLS: WeakMap<\n    NavigationContainerRef<any>,\n    { readonly linking: LinkingOptions<any> }\n  >;\n}\n\nglobalThis.REACT_NAVIGATION_DEVTOOLS = new WeakMap();\n\ntype Props<ParamList extends object> = NavigationContainerProps & {\n  direction?: LocaleDirection;\n  linking?: LinkingOptions<ParamList>;\n  fallback?: React.ReactNode;\n  documentTitle?: DocumentTitleOptions;\n};\n\n/**\n * Container component which holds the navigation state designed for React Native apps.\n * This should be rendered at the root wrapping the whole app.\n *\n * @param props.initialState Initial state object for the navigation tree. When deep link handling is enabled, this will override deep links when specified. Make sure that you don't specify an `initialState` when there's a deep link (`Linking.getInitialURL()`).\n * @param props.onReady Callback which is called after the navigation tree mounts.\n * @param props.onStateChange Callback which is called with the latest navigation state when it changes.\n * @param props.onUnhandledAction Callback which is called when an action is not handled.\n * @param props.direction Text direction of the components. Defaults to `'ltr'`.\n * @param props.theme Theme object for the UI elements.\n * @param props.linking Options for deep linking. Deep link handling is enabled when this prop is provided, unless `linking.enabled` is `false`.\n * @param props.fallback Fallback component to render until we have finished getting initial state when linking is enabled. Defaults to `null`.\n * @param props.documentTitle Options to configure the document title on Web. Updating document title is handled by default unless `documentTitle.enabled` is `false`.\n * @param props.children Child elements to render the content.\n * @param props.ref Ref object which refers to the navigation object containing helper methods.\n */\nfunction NavigationContainerInner(\n  {\n    direction = I18nManager.getConstants().isRTL ? 'rtl' : 'ltr',\n    theme = DefaultTheme,\n    linking,\n    fallback = null,\n    documentTitle,\n    onReady,\n    onStateChange,\n    ...rest\n  }: Props<ParamListBase>,\n  ref?: React.Ref<NavigationContainerRef<ParamListBase> | null>\n) {\n  const isLinkingEnabled = linking ? linking.enabled !== false : false;\n\n  if (linking?.config) {\n    validatePathConfig(linking.config);\n  }\n\n  const refContainer = React.useRef<NavigationContainerRef<ParamListBase> | null>(null);\n\n  useBackButton(refContainer);\n  useDocumentTitle(refContainer, documentTitle);\n\n  const [lastUnhandledLink, setLastUnhandledLink] = React.useState<string | undefined>();\n\n  const { getInitialState } = useLinking(\n    refContainer,\n    {\n      enabled: isLinkingEnabled,\n      prefixes: [],\n      ...linking,\n    },\n    setLastUnhandledLink\n  );\n\n  const linkingContext = React.useMemo(() => ({ options: linking }), [linking]);\n\n  const unhandledLinkingContext = React.useMemo(\n    () => ({ lastUnhandledLink, setLastUnhandledLink }),\n    [lastUnhandledLink, setLastUnhandledLink]\n  );\n\n  const onReadyForLinkingHandling = useLatestCallback(() => {\n    // If the screen path matches lastUnhandledLink, we do not track it\n    const path = refContainer.current?.getCurrentRoute()?.path;\n    setLastUnhandledLink((previousLastUnhandledLink) => {\n      if (previousLastUnhandledLink === path) {\n        return undefined;\n      }\n      return previousLastUnhandledLink;\n    });\n    onReady?.();\n  });\n\n  const onStateChangeForLinkingHandling = useLatestCallback(\n    (state: Readonly<NavigationState> | undefined) => {\n      // If the screen path matches lastUnhandledLink, we do not track it\n      const path = refContainer.current?.getCurrentRoute()?.path;\n      setLastUnhandledLink((previousLastUnhandledLink) => {\n        if (previousLastUnhandledLink === path) {\n          return undefined;\n        }\n        return previousLastUnhandledLink;\n      });\n      onStateChange?.(state);\n    }\n  );\n  // Add additional linking related info to the ref\n  // This will be used by the devtools\n  React.useEffect(() => {\n    if (refContainer.current) {\n      REACT_NAVIGATION_DEVTOOLS.set(refContainer.current, {\n        get linking() {\n          return {\n            ...linking,\n            enabled: isLinkingEnabled,\n            prefixes: linking?.prefixes ?? [],\n            getStateFromPath: linking?.getStateFromPath ?? getStateFromPath,\n            getPathFromState: linking?.getPathFromState ?? getPathFromState,\n            getActionFromState: linking?.getActionFromState ?? getActionFromState,\n          };\n        },\n      });\n    }\n  });\n\n  const [isResolved, initialState] = useThenable(getInitialState);\n\n  React.useImperativeHandle(ref, () => refContainer.current!);\n\n  const isLinkingReady = rest.initialState != null || !isLinkingEnabled || isResolved;\n\n  if (!isLinkingReady) {\n    // This is temporary until we have Suspense for data-fetching\n    // Then the fallback will be handled by a parent `Suspense` component\n    return <ThemeProvider value={theme}>{fallback}</ThemeProvider>;\n  }\n\n  return (\n    <LocaleDirContext.Provider value={direction}>\n      <UnhandledLinkingContext.Provider value={unhandledLinkingContext}>\n        <LinkingContext.Provider value={linkingContext}>\n          <BaseNavigationContainer\n            {...rest}\n            theme={theme}\n            onReady={onReadyForLinkingHandling}\n            onStateChange={onStateChangeForLinkingHandling}\n            initialState={rest.initialState == null ? initialState : rest.initialState}\n            ref={refContainer}\n          />\n        </LinkingContext.Provider>\n      </UnhandledLinkingContext.Provider>\n    </LocaleDirContext.Provider>\n  );\n}\n\nexport const NavigationContainer = React.forwardRef(NavigationContainerInner) as <\n  RootParamList extends object = ReactNavigation.RootParamList,\n>(\n  props: Props<RootParamList> & {\n    ref?: React.Ref<NavigationContainerRef<RootParamList>>;\n  }\n) => React.ReactElement;\n"]}