{"version": 3, "file": "useDocumentTitle.js", "sourceRoot": "", "sources": ["../../src/fork/useDocumentTitle.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,4CA0BC;AAjCD,6CAA+B;AAE/B,uDAAuD;AAEvD;;GAEG;AACH,SAAgB,gBAAgB,CAC9B,GAAkE,EAClE,EACE,OAAO,GAAG,IAAI,EACd,SAAS,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,OAAO,EAAE,KAAK,IAAI,KAAK,EAAE,IAAI,MACrC,EAAE;IAE5B,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC;QAE/B,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC,iBAAiB,EAAE,EAAE,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC;YAEtF,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;QACzB,CAAC;QAED,OAAO,UAAU,EAAE,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE;YAC9C,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC,CAAC;YAEvE,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["/*\n * This file is unchanged, except for fixing imports and removing eslint comments\n */\nimport type {\n  DocumentTitleOptions,\n  NavigationContainerRef,\n  ParamListBase,\n} from '@react-navigation/native';\nimport * as React from 'react';\n\n// import type { DocumentTitleOptions } from './types';\n\n/**\n * Set the document title for the active screen\n */\nexport function useDocumentTitle(\n  ref: React.RefObject<NavigationContainerRef<ParamListBase> | null>,\n  {\n    enabled = true,\n    formatter = (options, route) => options?.title ?? route?.name,\n  }: DocumentTitleOptions = {}\n) {\n  React.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n\n    const navigation = ref.current;\n\n    if (navigation) {\n      const title = formatter(navigation.getCurrentOptions(), navigation.getCurrentRoute());\n\n      document.title = title;\n    }\n\n    return navigation?.addListener('options', (e) => {\n      const title = formatter(e.data.options, navigation?.getCurrentRoute());\n\n      document.title = title;\n    });\n  });\n}\n"]}