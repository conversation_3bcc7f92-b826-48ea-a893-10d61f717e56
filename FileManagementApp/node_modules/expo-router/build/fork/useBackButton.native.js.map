{"version": 3, "file": "useBackButton.native.js", "sourceRoot": "", "sources": ["../../src/fork/useBackButton.native.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,sCAoBC;AAvBD,6CAA+B;AAC/B,+CAA2C;AAE3C,SAAgB,aAAa,CAAC,GAA2D;IACvF,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,MAAM,YAAY,GAAG,0BAAW,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,GAAG,EAAE;YAC1E,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC;YAE/B,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;gBACvB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,UAAU,CAAC,SAAS,EAAE,EAAE,CAAC;gBAC3B,UAAU,CAAC,MAAM,EAAE,CAAC;gBAEpB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;IACrC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["/**\n * This file is unchanged, except for removing eslint comments\n */\nimport type { NavigationContainerRef, ParamListBase } from '@react-navigation/native';\nimport * as React from 'react';\nimport { BackHandler } from 'react-native';\n\nexport function useBackButton(ref: React.RefObject<NavigationContainerRef<ParamListBase>>) {\n  React.useEffect(() => {\n    const subscription = BackHandler.addEventListener('hardwareBackPress', () => {\n      const navigation = ref.current;\n\n      if (navigation == null) {\n        return false;\n      }\n\n      if (navigation.canGoBack()) {\n        navigation.goBack();\n\n        return true;\n      }\n\n      return false;\n    });\n\n    return () => subscription.remove();\n  }, [ref]);\n}\n"]}