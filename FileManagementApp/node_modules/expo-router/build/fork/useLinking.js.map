{"version": 3, "file": "useLinking.js", "sourceRoot": "", "sources": ["../../src/fork/useLinking.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA,gCAmZC;AAED,4DAEC;AApeD,qDAUkC;AAClC,sEAAsC;AACtC,6CAA+B;AAE/B,+DAA4D;AAC5D,yDAAmD;AACnD,4CAAkD;AAClD,iFAAsE;AACtE,+DAAkE;AAIlE;;;GAGG;AACH,MAAM,iBAAiB,GAAG,CACxB,CAAgB,EAChB,CAAgB,EACgB,EAAE;IAClC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC;QAC1D,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAChC,CAAC;IAED,uFAAuF;IACvF,MAAM,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;IACtE,MAAM,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;IAEtE,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACjC,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAEjC,MAAM,WAAW,GAAG,MAAM,CAAC,KAAsB,CAAC;IAClD,MAAM,WAAW,GAAG,MAAM,CAAC,KAAsB,CAAC;IAElD,sDAAsD;IACtD,gCAAgC;IAChC,iCAAiC;IACjC,yCAAyC;IACzC,mCAAmC;IACnC,IACE,cAAc,KAAK,cAAc;QACjC,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG;QACzB,WAAW,KAAK,SAAS;QACzB,WAAW,KAAK,SAAS;QACzB,WAAW,CAAC,GAAG,KAAK,WAAW,CAAC,GAAG,EACnC,CAAC;QACD,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC;IAED,OAAO,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AACrD,CAAC,CAAC;AAEF;;GAEG;AACI,MAAM,MAAM,GAAG,CAAC,EAAuB,EAAE,EAAE;IAChD,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;IAC9B,MAAM,QAAQ,GAAG,GAAG,EAAE;QACpB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACzB,CAAC,CAAC;IACF,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AANW,QAAA,MAAM,UAMjB;AAEF,MAAM,eAAe,GAAa,EAAE,CAAC;AAIrC,SAAgB,UAAU,CACxB,GAAkE,EAClE,EACE,OAAO,GAAG,IAAI,EACd,MAAM,EACN,gBAAgB,GAAG,yBAAuB,EAC1C,gBAAgB,GAAG,yBAAuB,EAC1C,kBAAkB,GAAG,2BAAyB,GACtC,EACV,kBAAqE;IAErE,MAAM,WAAW,GAAG,IAAA,qCAA4B,GAAE,CAAC;IAEnD,MAAM,KAAK,GAAG,IAAA,iCAAkB,GAAE,CAAC;IAEnC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,OAAO,KAAK,KAAK,IAAI,eAAe,CAAC,MAAM,EAAE,CAAC;YAChD,OAAO,CAAC,KAAK,CACX;gBACE,6KAA6K;gBAC7K,uFAAuF;gBACvF,4DAA4D;aAC7D;iBACE,IAAI,CAAC,IAAI,CAAC;iBACV,IAAI,EAAE,CACV,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC;QAEzB,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;YACtB,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,GAAG,EAAE;YACV,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAE/C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACf,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACnC,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;IAE3B,MAAM,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,yCAAmB,CAAC,CAAC;IAEtD,kGAAkG;IAClG,oFAAoF;IACpF,yGAAyG;IACzG,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACzC,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACvC,MAAM,mBAAmB,GAAG,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAC3D,MAAM,mBAAmB,GAAG,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAC3D,MAAM,qBAAqB,GAAG,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;IAE/D,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7B,SAAS,CAAC,OAAO,GAAG,MAAM,CAAC;QAC3B,mBAAmB,CAAC,OAAO,GAAG,gBAAgB,CAAC;QAC/C,mBAAmB,CAAC,OAAO,GAAG,gBAAgB,CAAC;QAC/C,qBAAqB,CAAC,OAAO,GAAG,kBAAkB,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,MAAM,iCAAiC,GAAG,KAAK,CAAC,WAAW,CACzD,CAAC,KAAkB,EAAE,EAAE;QACrB,aAAa;QACb,0HAA0H;QAC1H,kCAAkC;QAClC,gDAAgD;QAChD,MAAM,UAAU,GAAG,CAAC,8BAAkB,CAAC,CAAC;QACxC,WAAW;QAEX,qEAAqE;QACrE,0DAA0D;QAC1D,aAAa;QACb,8EAA8E;QAC9E,OAAO,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/D,WAAW;IACb,CAAC,EACD,CAAC,GAAG,CAAC,CACN,CAAC;IAEF,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,qCAAa,CAAC,CAAC;IAExC,MAAM,eAAe,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE;QAC7C,IAAI,KAA8B,CAAC;QAEnC,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,MAAM,QAAQ,GACZ,MAAM,EAAE,QAAQ,IAAI,CAAC,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAEpF,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;YAExE,IAAI,IAAI,EAAE,CAAC;gBACT,KAAK,GAAG,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;YAC/D,CAAC;YAED,mEAAmE;YACnE,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;QAED,MAAM,QAAQ,GAAG;YACf,IAAI,CAAC,WAAsD;gBACzD,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACnE,CAAC;YACD,KAAK;gBACH,OAAO,QAAQ,CAAC;YAClB,CAAC;SACF,CAAC;QAEF,OAAO,QAAgD,CAAC;QACxD,uDAAuD;IACzD,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,gBAAgB,GAAG,KAAK,CAAC,MAAM,CAAqB,SAAS,CAAC,CAAC;IACrE,MAAM,gBAAgB,GAAG,KAAK,CAAC,MAAM,CAA8B,SAAS,CAAC,CAAC;IAC9E,MAAM,sBAAsB,GAAG,KAAK,CAAC,MAAM,CAAqB,SAAS,CAAC,CAAC;IAE3E,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,gBAAgB,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;QAEzC,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE;YACzB,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC;YAE/B,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,OAAO;YACT,CAAC;YAED,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;YAE5B,MAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC;YACjE,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;YAE5B,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,IAAI,CAAC,CAAC;YAEpD,gBAAgB,CAAC,OAAO,GAAG,KAAK,CAAC;YACjC,sBAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;YAEtC,qGAAqG;YACrG,8CAA8C;YAC9C,sDAAsD;YACtD,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAElC,IAAI,MAAM,EAAE,IAAI,KAAK,IAAI,IAAI,MAAM,EAAE,KAAK,EAAE,CAAC;gBAC3C,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACnC,OAAO;YACT,CAAC;YAED,MAAM,KAAK,GAAG,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;YAEnE,uDAAuD;YACvD,oFAAoF;YACpF,IAAI,KAAK,EAAE,CAAC;gBACV,mEAAmE;gBACnE,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBACzB,qEAAqE;gBACrE,0DAA0D;gBAC1D,IAAI,iCAAiC,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7C,OAAO;gBACT,CAAC;gBAED,IACE,KAAK,GAAG,aAAa;oBACrB;;;;;;;;;;;;;;;;;;uBAkBG;oBACH,CAAC,KAAK,KAAK,aAAa,IAAI,CAAC,CAAC,MAAM,IAAI,GAAG,MAAM,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC;gBACpF,WAAW;kBACX,CAAC;oBACD,MAAM,MAAM,GAAG,qBAAqB,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;oBAEvE,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;wBACzB,IAAI,CAAC;4BACH,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;wBAC9B,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACX,uCAAuC;4BACvC,6FAA6F;4BAC7F,OAAO,CAAC,IAAI,CACV,qDAAqD,IAAI,MACvD,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACrE,EAAE,CACH,CAAC;wBACJ,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBAC9B,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,6EAA6E;gBAC7E,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,kBAAkB,EAAE,GAAG,EAAE,iCAAiC,CAAC,CAAC,CAAC;IAEnF,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;QACT,CAAC;QAED,MAAM,eAAe,GAAG,CACtB,KAA0C,EAC1C,KAAsB,EACd,EAAE;YACV,IAAI,IAAI,CAAC;YAET,0GAA0G;YAC1G,wEAAwE;YACxE,IAAI,KAAK,EAAE,IAAI,EAAE,CAAC;gBAChB,MAAM,YAAY,GAAG,mBAAmB,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;gBAEhF,IAAI,YAAY,EAAE,CAAC;oBACjB,MAAM,YAAY,GAAG,IAAA,yBAAgB,EAAC,YAAY,CAAC,CAAC;oBAEpD,IACE,YAAY;wBACZ,YAAY,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;wBAChC,IAAA,yBAAO,EAAC,EAAE,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EACxD,CAAC;wBACD,+EAA+E;wBAC/E,qBAAqB;wBACrB,IAAI,GAAG,IAAA,gCAAa,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACjC,WAAW;oBACb,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,IAAI,GAAG,mBAAmB,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;YAC/D,CAAC;YAED,sFAAsF;YACtF,iDAAiD;YACjD,iDAAiD;YACjD,iBAAiB;YAEjB,+CAA+C;YAC/C,OAAO;YACP,qBAAqB;YACrB,aAAa;YACb,8BAA8B;YAC9B,sBAAsB;YACtB,oCAAoC;YACpC,MAAM;YACN,iCAAiC;YACjC,IAAI;YACJ,WAAW;YAEX,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QAEF,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,gFAAgF;YAChF,+DAA+D;YAE/D,aAAa;YACb,wFAAwF;YACxF,4CAA4C;YAC5C,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAC7C,MAAM,KAAK,GAAG,KAAK,CAAC,KAAwB,CAAC;YAE7C,WAAW;YAEX,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,KAAK,GAAG,IAAA,yBAAgB,EAAC,KAAK,CAAC,CAAC;gBACtC,MAAM,IAAI,GAAG,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAE3C,IAAI,gBAAgB,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;oBAC3C,aAAa;oBACb,oCAAoC;oBACpC,gBAAgB,CAAC,OAAO,GAAG,SAAS,CAAC;oBACrC,WAAW;gBACb,CAAC;gBAED,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QAED,MAAM,aAAa,GAAG,KAAK,IAAI,EAAE;YAC/B,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC;YAE/B,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,OAAO;YACT,CAAC;YAED,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC;YAC/C,aAAa;YACb,wFAAwF;YACxF,2CAA2C;YAC3C,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,EAAE,CAAC;YAC5C,MAAM,KAAK,GAAG,KAAK,CAAC,KAAwB,CAAC;YAE7C,WAAW;YAEX,6FAA6F;YAC7F,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO;YACT,CAAC;YAED,MAAM,WAAW,GAAG,sBAAsB,CAAC,OAAO,CAAC;YACnD,MAAM,KAAK,GAAG,IAAA,yBAAgB,EAAC,KAAK,CAAC,CAAC;YACtC,MAAM,IAAI,GAAG,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAE3C,aAAa;YACb,oCAAoC;YACpC,gBAAgB,CAAC,OAAO,GAAG,SAAS,CAAC;YACrC,WAAW;YACX,sBAAsB,CAAC,OAAO,GAAG,SAAS,CAAC;YAE3C,kDAAkD;YAClD,2EAA2E;YAC3E,yGAAyG;YACzG,gEAAgE;YAChE,MAAM,CAAC,oBAAoB,EAAE,YAAY,CAAC,GAAG,iBAAiB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAErF,IACE,oBAAoB;gBACpB,YAAY;gBACZ,kFAAkF;gBAClF,yDAAyD;gBACzD,IAAI,KAAK,WAAW,EACpB,CAAC;gBACD,MAAM,YAAY,GAChB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;oBACjF,CAAC,oBAAoB,CAAC,OAAO;wBAC3B,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM;wBACrC,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAE1C,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;oBACrB,sDAAsD;oBACtD,2FAA2F;oBAC3F,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;gBAChC,CAAC;qBAAM,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;oBAC5B,gFAAgF;oBAEhF,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC9C,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;oBAEnC,IAAI,CAAC;wBACH,IACE,SAAS,KAAK,CAAC,CAAC;4BAChB,SAAS,GAAG,YAAY;4BACxB,8EAA8E;4BAC9E,OAAO,CAAC,GAAG,CAAC,SAAS,GAAG,YAAY,CAAC,EACrC,CAAC;4BACD,2FAA2F;4BAC3F,MAAM,OAAO,CAAC,EAAE,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC;wBAC7C,CAAC;6BAAM,CAAC;4BACN,kFAAkF;4BAClF,wEAAwE;4BACxE,gEAAgE;4BAChE,MAAM,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;wBACjC,CAAC;wBAED,+DAA+D;wBAC/D,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;oBACnC,CAAC;oBAAC,MAAM,CAAC;wBACP,iCAAiC;oBACnC,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,0DAA0D;oBAC1D,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,iEAAiE;gBACjE,6EAA6E;gBAC7E,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YACnC,CAAC;QACH,CAAC,CAAC;QAEF,+FAA+F;QAC/F,0DAA0D;QAC1D,qGAAqG;QACrG,OAAO,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,EAAE,IAAA,cAAM,EAAC,aAAa,CAAC,CAAC,CAAC;IAClE,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAE5B,OAAO;QACL,eAAe;KAChB,CAAC;AACJ,CAAC;AAED,SAAgB,wBAAwB;IACtC,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;AACnE,CAAC", "sourcesContent": ["import {\n  LinkingOptions,\n  findFocusedRoute,\n  getActionFromState as getActionFromStateDefault,\n  getPathFromState as getPathFromStateDefault,\n  getStateFromPath as getStateFromPathDefault,\n  type NavigationContainerRef,\n  type NavigationState,\n  type ParamListBase,\n  useNavigationIndependentTree,\n} from '@react-navigation/native';\nimport isEqual from 'fast-deep-equal';\nimport * as React from 'react';\n\nimport { createMemoryHistory } from './createMemoryHistory';\nimport { appendBaseUrl } from './getPathFromState';\nimport { INTERNAL_SLOT_NAME } from '../constants';\nimport { ServerContext } from '../global-state/serverLocationContext';\nimport { useExpoRouterStore } from '../global-state/storeContext';\n\ntype ResultState = ReturnType<typeof getStateFromPathDefault>;\n\n/**\n * Find the matching navigation state that changed between 2 navigation states\n * e.g.: a -> b -> c -> d and a -> b -> c -> e -> f, if history in b changed, b is the matching state\n */\nconst findMatchingState = <T extends NavigationState>(\n  a: T | undefined,\n  b: T | undefined\n): [T | undefined, T | undefined] => {\n  if (a === undefined || b === undefined || a.key !== b.key) {\n    return [undefined, undefined];\n  }\n\n  // Tab and drawer will have `history` property, but stack will have history in `routes`\n  const aHistoryLength = a.history ? a.history.length : a.routes.length;\n  const bHistoryLength = b.history ? b.history.length : b.routes.length;\n\n  const aRoute = a.routes[a.index];\n  const bRoute = b.routes[b.index];\n\n  const aChildState = aRoute.state as T | undefined;\n  const bChildState = bRoute.state as T | undefined;\n\n  // Stop here if this is the state object that changed:\n  // - history length is different\n  // - focused routes are different\n  // - one of them doesn't have child state\n  // - child state keys are different\n  if (\n    aHistoryLength !== bHistoryLength ||\n    aRoute.key !== bRoute.key ||\n    aChildState === undefined ||\n    bChildState === undefined ||\n    aChildState.key !== bChildState.key\n  ) {\n    return [a, b];\n  }\n\n  return findMatchingState(aChildState, bChildState);\n};\n\n/**\n * Run async function in series as it's called.\n */\nexport const series = (cb: () => Promise<void>) => {\n  let queue = Promise.resolve();\n  const callback = () => {\n    queue = queue.then(cb);\n  };\n  return callback;\n};\n\nconst linkingHandlers: symbol[] = [];\n\ntype Options = LinkingOptions<ParamListBase>;\n\nexport function useLinking(\n  ref: React.RefObject<NavigationContainerRef<ParamListBase> | null>,\n  {\n    enabled = true,\n    config,\n    getStateFromPath = getStateFromPathDefault,\n    getPathFromState = getPathFromStateDefault,\n    getActionFromState = getActionFromStateDefault,\n  }: Options,\n  onUnhandledLinking: (lastUnhandledLining: string | undefined) => void\n) {\n  const independent = useNavigationIndependentTree();\n\n  const store = useExpoRouterStore();\n\n  React.useEffect(() => {\n    if (process.env.NODE_ENV === 'production') {\n      return undefined;\n    }\n\n    if (independent) {\n      return undefined;\n    }\n\n    if (enabled !== false && linkingHandlers.length) {\n      console.error(\n        [\n          'Looks like you have configured linking in multiple places. This is likely an error since deep links should only be handled in one place to avoid conflicts. Make sure that:',\n          \"- You don't have multiple NavigationContainers in the app each with 'linking' enabled\",\n          '- Only a single instance of the root component is rendered',\n        ]\n          .join('\\n')\n          .trim()\n      );\n    }\n\n    const handler = Symbol();\n\n    if (enabled !== false) {\n      linkingHandlers.push(handler);\n    }\n\n    return () => {\n      const index = linkingHandlers.indexOf(handler);\n\n      if (index > -1) {\n        linkingHandlers.splice(index, 1);\n      }\n    };\n  }, [enabled, independent]);\n\n  const [history] = React.useState(createMemoryHistory);\n\n  // We store these options in ref to avoid re-creating getInitialState and re-subscribing listeners\n  // This lets user avoid wrapping the items in `React.useCallback` or `React.useMemo`\n  // Not re-creating `getInitialState` is important coz it makes it easier for the user to use in an effect\n  const enabledRef = React.useRef(enabled);\n  const configRef = React.useRef(config);\n  const getStateFromPathRef = React.useRef(getStateFromPath);\n  const getPathFromStateRef = React.useRef(getPathFromState);\n  const getActionFromStateRef = React.useRef(getActionFromState);\n\n  React.useEffect(() => {\n    enabledRef.current = enabled;\n    configRef.current = config;\n    getStateFromPathRef.current = getStateFromPath;\n    getPathFromStateRef.current = getPathFromState;\n    getActionFromStateRef.current = getActionFromState;\n  });\n\n  const validateRoutesNotExistInRootState = React.useCallback(\n    (state: ResultState) => {\n      // START FORK\n      // Instead of using the rootState, we use INTERNAL_SLOT_NAME, which is the only route in the root navigator in Expo Router\n      // const navigation = ref.current;\n      // const rootState = navigation?.getRootState();\n      const routeNames = [INTERNAL_SLOT_NAME];\n      // END FORK\n\n      // Make sure that the routes in the state exist in the root navigator\n      // Otherwise there's an error in the linking configuration\n      // START FORK\n      // return state?.routes.some((r) => !rootState?.routeNames?.includes(r.name));\n      return state?.routes.some((r) => !routeNames.includes(r.name));\n      // END FORK\n    },\n    [ref]\n  );\n\n  const server = React.use(ServerContext);\n\n  const getInitialState = React.useCallback(() => {\n    let value: ResultState | undefined;\n\n    if (enabledRef.current) {\n      const location =\n        server?.location ?? (typeof window !== 'undefined' ? window.location : undefined);\n\n      const path = location ? location.pathname + location.search : undefined;\n\n      if (path) {\n        value = getStateFromPathRef.current(path, configRef.current);\n      }\n\n      // If the link were handled, it gets cleared in NavigationContainer\n      onUnhandledLinking(path);\n    }\n\n    const thenable = {\n      then(onfulfilled?: (state: ResultState | undefined) => void) {\n        return Promise.resolve(onfulfilled ? onfulfilled(value) : value);\n      },\n      catch() {\n        return thenable;\n      },\n    };\n\n    return thenable as PromiseLike<ResultState | undefined>;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const previousIndexRef = React.useRef<number | undefined>(undefined);\n  const previousStateRef = React.useRef<NavigationState | undefined>(undefined);\n  const pendingPopStatePathRef = React.useRef<string | undefined>(undefined);\n\n  React.useEffect(() => {\n    previousIndexRef.current = history.index;\n\n    return history.listen(() => {\n      const navigation = ref.current;\n\n      if (!navigation || !enabled) {\n        return;\n      }\n\n      const { location } = window;\n\n      const path = location.pathname + location.search + location.hash;\n      const index = history.index;\n\n      const previousIndex = previousIndexRef.current ?? 0;\n\n      previousIndexRef.current = index;\n      pendingPopStatePathRef.current = path;\n\n      // When browser back/forward is clicked, we first need to check if state object for this index exists\n      // If it does we'll reset to that state object\n      // Otherwise, we'll handle it like a regular deep link\n      const record = history.get(index);\n\n      if (record?.path === path && record?.state) {\n        navigation.resetRoot(record.state);\n        return;\n      }\n\n      const state = getStateFromPathRef.current(path, configRef.current);\n\n      // We should only dispatch an action when going forward\n      // Otherwise the action will likely add items to history, which would mess things up\n      if (state) {\n        // If the link were handled, it gets cleared in NavigationContainer\n        onUnhandledLinking(path);\n        // Make sure that the routes in the state exist in the root navigator\n        // Otherwise there's an error in the linking configuration\n        if (validateRoutesNotExistInRootState(state)) {\n          return;\n        }\n\n        if (\n          index > previousIndex ||\n          /* START FORK\n           *\n           * This is a workaround for React Navigation's handling of hashes (it doesn't handle them)\n           * When you click on <a href=\"#hash\">, the browser will first fire a popstate event\n           * and this callback will be called.\n           *\n           * From React Navigation's perspective, it's treating the new hash change like a back/forward\n           * button press, so it thinks it should reset the state. When we should\n           * be to be pushing the new state\n           *\n           * Our fix is to check if the index is the same as the previous index\n           * and if the incoming path is the same as the old path but with the hash added,\n           * then treat it as a push instead of a reset\n           *\n           * This also works for subsequent hash changes, as internally RN\n           * doesn't store the hash in the history state.\n           *\n           * @see https://developer.mozilla.org/en-US/docs/Web/API/Window/popstate_event#when_popstate_is_sent\n           */\n          (index === previousIndex && (!record || `${record?.path}${location.hash}` === path))\n          // END FORK\n        ) {\n          const action = getActionFromStateRef.current(state, configRef.current);\n\n          if (action !== undefined) {\n            try {\n              navigation.dispatch(action);\n            } catch (e) {\n              // Ignore any errors from deep linking.\n              // This could happen in case of malformed links, navigation object not being initialized etc.\n              console.warn(\n                `An error occurred when trying to handle the link '${path}': ${\n                  typeof e === 'object' && e != null && 'message' in e ? e.message : e\n                }`\n              );\n            }\n          } else {\n            navigation.resetRoot(state);\n          }\n        } else {\n          navigation.resetRoot(state);\n        }\n      } else {\n        // if current path didn't return any state, we should revert to initial state\n        navigation.resetRoot(state);\n      }\n    });\n  }, [enabled, history, onUnhandledLinking, ref, validateRoutesNotExistInRootState]);\n\n  React.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n\n    const getPathForRoute = (\n      route: ReturnType<typeof findFocusedRoute>,\n      state: NavigationState\n    ): string => {\n      let path;\n\n      // If the `route` object contains a `path`, use that path as long as `route.name` and `params` still match\n      // This makes sure that we preserve the original URL for wildcard routes\n      if (route?.path) {\n        const stateForPath = getStateFromPathRef.current(route.path, configRef.current);\n\n        if (stateForPath) {\n          const focusedRoute = findFocusedRoute(stateForPath);\n\n          if (\n            focusedRoute &&\n            focusedRoute.name === route.name &&\n            isEqual({ ...focusedRoute.params }, { ...route.params })\n          ) {\n            // START FORK - Ensure paths coming from events (e.g refresh) have the base URL\n            // path = route.path;\n            path = appendBaseUrl(route.path);\n            // END FORK\n          }\n        }\n      }\n\n      if (path == null) {\n        path = getPathFromStateRef.current(state, configRef.current);\n      }\n\n      // START FORK - ExpoRouter manually handles hashes. This code is intentionally removed\n      // const previousRoute = previousStateRef.current\n      //   ? findFocusedRoute(previousStateRef.current)\n      //   : undefined;\n\n      // Preserve the hash if the route didn't change\n      // if (\n      //   previousRoute &&\n      //   route &&\n      //   'key' in previousRoute &&\n      //   'key' in route &&\n      //   previousRoute.key === route.key\n      // ) {\n      //   path = path + location.hash;\n      // }\n      // END FORK\n\n      return path;\n    };\n\n    if (ref.current) {\n      // We need to record the current metadata on the first render if they aren't set\n      // This will allow the initial state to be in the history entry\n\n      // START FORK\n      // Instead of using the rootState (which might be stale) we should use the focused state\n      // const state = ref.current.getRootState();\n      const rootState = ref.current.getRootState();\n      const state = store.state as NavigationState;\n\n      // END FORK\n\n      if (state) {\n        const route = findFocusedRoute(state);\n        const path = getPathForRoute(route, state);\n\n        if (previousStateRef.current === undefined) {\n          // START FORK\n          // previousStateRef.current = state;\n          previousStateRef.current = rootState;\n          // END FORK\n        }\n\n        history.replace({ path, state });\n      }\n    }\n\n    const onStateChange = async () => {\n      const navigation = ref.current;\n\n      if (!navigation || !enabled) {\n        return;\n      }\n\n      const previousState = previousStateRef.current;\n      // START FORK\n      // Instead of using the rootState (which might be stale) we should use the focused state\n      // const state = navigation.getRootState();\n      const rootState = navigation.getRootState();\n      const state = store.state as NavigationState;\n\n      // END FORK\n\n      // root state may not available, for example when root navigators switch inside the container\n      if (!state) {\n        return;\n      }\n\n      const pendingPath = pendingPopStatePathRef.current;\n      const route = findFocusedRoute(state);\n      const path = getPathForRoute(route, state);\n\n      // START FORK\n      // previousStateRef.current = state;\n      previousStateRef.current = rootState;\n      // END FORK\n      pendingPopStatePathRef.current = undefined;\n\n      // To detect the kind of state change, we need to:\n      // - Find the common focused navigation state in previous and current state\n      // - If only the route keys changed, compare history/routes.length to check if we go back/forward/replace\n      // - If no common focused navigation state found, it's a replace\n      const [previousFocusedState, focusedState] = findMatchingState(previousState, state);\n\n      if (\n        previousFocusedState &&\n        focusedState &&\n        // We should only handle push/pop if path changed from what was in last `popstate`\n        // Otherwise it's likely a change triggered by `popstate`\n        path !== pendingPath\n      ) {\n        const historyDelta =\n          (focusedState.history ? focusedState.history.length : focusedState.routes.length) -\n          (previousFocusedState.history\n            ? previousFocusedState.history.length\n            : previousFocusedState.routes.length);\n\n        if (historyDelta > 0) {\n          // If history length is increased, we should pushState\n          // Note that path might not actually change here, for example, drawer open should pushState\n          history.push({ path, state });\n        } else if (historyDelta < 0) {\n          // If history length is decreased, i.e. entries were removed, we want to go back\n\n          const nextIndex = history.backIndex({ path });\n          const currentIndex = history.index;\n\n          try {\n            if (\n              nextIndex !== -1 &&\n              nextIndex < currentIndex &&\n              // We should only go back if the entry exists and it's less than current index\n              history.get(nextIndex - currentIndex)\n            ) {\n              // An existing entry for this path exists and it's less than current index, go back to that\n              await history.go(nextIndex - currentIndex);\n            } else {\n              // We couldn't find an existing entry to go back to, so we'll go back by the delta\n              // This won't be correct if multiple routes were pushed in one go before\n              // Usually this shouldn't happen and this is a fallback for that\n              await history.go(historyDelta);\n            }\n\n            // Store the updated state as well as fix the path if incorrect\n            history.replace({ path, state });\n          } catch {\n            // The navigation was interrupted\n          }\n        } else {\n          // If history length is unchanged, we want to replaceState\n          history.replace({ path, state });\n        }\n      } else {\n        // If no common navigation state was found, assume it's a replace\n        // This would happen if the user did a reset/conditionally changed navigators\n        history.replace({ path, state });\n      }\n    };\n\n    // We debounce onStateChange coz we don't want multiple state changes to be handled at one time\n    // This could happen since `history.go(n)` is asynchronous\n    // If `pushState` or `replaceState` were called before `history.go(n)` completes, it'll mess stuff up\n    return ref.current?.addListener('state', series(onStateChange));\n  }, [enabled, history, ref]);\n\n  return {\n    getInitialState,\n  };\n}\n\nexport function getInitialURLWithTimeout(): string | null | Promise<string | null> {\n  return typeof window === 'undefined' ? '' : window.location.href;\n}\n"]}