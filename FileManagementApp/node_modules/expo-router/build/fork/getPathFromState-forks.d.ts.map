{"version": 3, "file": "getPathFromState-forks.d.ts", "sourceRoot": "", "sources": ["../../src/fork/getPathFromState-forks.ts"], "names": [], "mappings": "AAAA,OAAO,EAA8C,KAAK,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAGlG,OAAO,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAG1E,MAAM,MAAM,WAAW,GAAG;IACxB,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,sBAAsB,CAAC,EAAE,OAAO,CAAC;CAClC,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG;IAE3B,gBAAgB,CAAC,EAAE,MAAM,CAAC;CAC3B,CAAC;AAEF,wBAAgB,kBAAkB,CAAC,SAAS,SAAS,MAAM,EAAE,EAC3D,qBAAqB,EACrB,cAAc,EACd,sBAAsB,EACtB,GAAG,OAAO,EACX,EAAE,OAAO,CAAC,SAAS,CAAC,QAEpB;AAED,wBAAgB,gBAAgB,CAC9B,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAC9B,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG;IACrB,KAAK,CAAC,EAAE,KAAK,CAAC;CACf,EACD,SAAS,CAAC,EAAE,eAAe;;EA0B5B;AAED,wBAAgB,kBAAkB,CAChC,IAAI,EAAE,MAAM,EACZ,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,aAAa,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,UAarD;AAED,wBAAgB,aAAa,CAC3B,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE,MAAM,GAAG,SAAqC,UASxD;AAED,wBAAgB,+BAA+B,CAAC,EAC9C,OAAO,EACP,KAAK,EACL,MAAM,EACN,cAAc,EACd,qBAAqB,EACrB,sBAA6B,EAC7B,gBAAgB,GACjB,EAAE,WAAW,GAAG;IACf,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;IAClB,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC5B,gBAAgB,CAAC,EAAE,MAAM,CAAC;CAC3B,UAkEA;AAED,eAAO,MAAM,YAAY,GAAI,SAAS,MAAM,WAAoD,CAAC;AAEjG,wBAAgB,aAAa,CAAC,CAAC,EAAE,MAAM,WAEtC"}