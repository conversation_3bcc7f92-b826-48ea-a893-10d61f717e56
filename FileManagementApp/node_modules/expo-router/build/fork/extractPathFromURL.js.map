{"version": 3, "file": "extractPathFromURL.js", "sourceRoot": "", "sources": ["../../src/fork/extractPathFromURL.ts"], "names": [], "mappings": ";;AAAA,4EAaC;AAED,0DAIC;AAsGD,wDAMC;AA/HD,SAAgB,gCAAgC,CAAC,GAAW;IAI1D,+EAA+E;IAC/E,oFAAoF;IAEpF,MAAM,IAAI,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC;IAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC7C,OAAO;QACL,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;QAC5B,WAAW,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;KAChC,CAAC;AACJ,CAAC;AAED,SAAgB,uBAAuB,CAAC,GAAW;IACjD,+EAA+E;IAC/E,oFAAoF;IACpF,OAAO,GAAG,CAAC,KAAK,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AACzD,CAAC;AAED,8BAA8B;AAC9B,SAAS,uBAAuB,CAAC,GAAW;IAC1C;IACE,2EAA2E;IAC3E,4CAA4C;IAC5C,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,EACzB,CAAC;QACD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAEhD,IAAI,QAAQ,KAAK,UAAU,IAAI,QAAQ,KAAK,YAAY,EAAE,CAAC;YACzD,wEAAwE;YACxE,kGAAkG;YAClG,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IAClC,CAAC;IAED,MAAM,QAAQ,GAAG,OAAO,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC;IAEjF,oEAAoE;IACpE,IACE,QAAQ;QACR,wEAAwE;QACxE,+CAA+C;QAC/C,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,EAC1B,CAAC;QACD,MAAM,QAAQ,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,YAAY,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC;QACzC,CAAC;QACD,sCAAsC;QACtC,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzD,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,YAAY,CAAC,OAAO,GAAG,WAAW,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,gCAAgC;IAEhC,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC;AAC3B,CAAC;AAED,0EAA0E;AAC1E,SAAS,uBAAuB,CAAC,GAAQ;IACvC,OAAO,GAAG,CAAC,QAAQ,KAAK,yBAAyB,CAAC;AACpD,CAAC;AAED,SAAS,YAAY,CAAC,GAAW;IAC/B,IAAI,GAAe,CAAC;IACpB,IAAI,CAAC;QACH,yEAAyE;QACzE,+BAA+B;QAC/B,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IAAC,MAAM,CAAC;QACP;;;;WAIG;QAEH;;;WAGG;QACH,OAAO,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;IACxC,CAAC;IAED,IAAI,uBAAuB,CAAC,GAAG,CAAC,EAAE,CAAC;QACjC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,MAAM,WAAW,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC;QACjD,OAAO,uBAAuB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,OAAO,GAAG,EAAE,CAAC;IAEjB,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACb,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC;IACtB,CAAC;IAED,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;QACjB,OAAO,IAAI,GAAG,CAAC,QAAQ,CAAC;IAC1B,CAAC;IAED,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM;QACpB,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,wEAAwE;YACxE,CAAC,GAAG,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE/F,IAAI,EAAE,EAAE,CAAC;QACP,OAAO,IAAI,GAAG,GAAG,EAAE,CAAC;IACtB,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAgB,sBAAsB,CAAC,SAAmB,EAAE,MAAc,EAAE;IAC1E,OAAO,CACL,uBAAuB,CAAC,GAAG,CAAC;QAC1B,sEAAsE;SACrE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CACtB,CAAC;AACJ,CAAC", "sourcesContent": ["export function parsePathAndParamsFromExpoGoLink(url: string): {\n  pathname: string;\n  queryString: string;\n} {\n  // If the URL is defined (default in Expo Go dev apps) and the URL has no path:\n  // `exp://*************:19000/` then use the default `exp://*************:19000/--/`\n\n  const href = parsePathFromExpoGoLink(url);\n  const results = href.match(/([^?]*)(\\?.*)?/);\n  return {\n    pathname: results?.[1] ?? '',\n    queryString: results?.[2] ?? '',\n  };\n}\n\nexport function parsePathFromExpoGoLink(url: string): string {\n  // If the URL is defined (default in Expo Go dev apps) and the URL has no path:\n  // `exp://*************:19000/` then use the default `exp://*************:19000/--/`\n  return url.match(/exps?:\\/\\/.*?\\/--\\/(.*)/)?.[1] ?? '';\n}\n\n// This is only run on native.\nfunction extractExactPathFromURL(url: string): string {\n  if (\n    // If a universal link / app link / web URL is used, we should use the path\n    // from the URL, while stripping the origin.\n    url.match(/^https?:\\/\\//)\n  ) {\n    const { origin, href, hostname } = new URL(url);\n\n    if (hostname === 'exp.host' || hostname === 'u.expo.dev') {\n      // These are QR code generate deep-link that always like to the '/' path\n      // TODO: In the future, QR code may link to a specific path and this logic will need to be udpated\n      return '';\n    }\n\n    return href.replace(origin, '');\n  }\n\n  const isExpoGo = typeof expo !== 'undefined' && globalThis.expo?.modules?.ExpoGo;\n\n  // Handle special URLs used in Expo Go: `/--/pathname` -> `pathname`\n  if (\n    isExpoGo &&\n    // while not exhaustive, `exp` and `exps` are the only two schemes which\n    // are passed through to other apps in Expo Go.\n    url.match(/^exp(s)?:\\/\\//)\n  ) {\n    const pathname = parsePathFromExpoGoLink(url);\n    if (pathname) {\n      return fromDeepLink('a://' + pathname);\n    }\n    // Match the `?.*` segment of the URL.\n    const queryParams = url.match(/exps?:\\/\\/.*\\?(.*)/)?.[1];\n    if (queryParams) {\n      return fromDeepLink('a://?' + queryParams);\n    }\n\n    return '';\n  }\n\n  // TODO: Support dev client URLs\n\n  return fromDeepLink(url);\n}\n\n/** Major hack to support the makeshift expo-development-client system. */\nfunction isExpoDevelopmentClient(url: URL): boolean {\n  return url.hostname === 'expo-development-client';\n}\n\nfunction fromDeepLink(url: string): string {\n  let res: URL | null;\n  try {\n    // This is for all standard deep links, e.g. `foobar://` where everything\n    // after the `://` is the path.\n    res = new URL(url);\n  } catch {\n    /**\n     * We failed to parse the URL. This can occur for a variety of reasons, including:\n     * - Its a partial URL (e.g. `/route?query=param`).\n     * - It has a valid App scheme, but the scheme isn't a valid URL scheme (e.g. `my_app://`)\n     */\n\n    /**\n     * App schemes are not valid URL schemes, so they will fail to parse.\n     * We need to strip the scheme from these URLs\n     */\n    return url.replace(/^[^:]+:\\/\\//, '');\n  }\n\n  if (isExpoDevelopmentClient(res)) {\n    if (!res.searchParams.get('url')) {\n      return '';\n    }\n    const incomingUrl = res.searchParams.get('url')!;\n    return extractExactPathFromURL(decodeURI(incomingUrl));\n  }\n\n  let results = '';\n\n  if (res.host) {\n    results += res.host;\n  }\n\n  if (res.pathname) {\n    results += res.pathname;\n  }\n\n  const qs = !res.search\n    ? ''\n    : // @ts-ignore: `entries` is not on `URLSearchParams` in some typechecks.\n      [...res.searchParams.entries()].map(([k, v]) => `${k}=${decodeURIComponent(v)}`).join('&');\n\n  if (qs) {\n    results += '?' + qs;\n  }\n\n  return results;\n}\n\nexport function extractExpoPathFromURL(_prefixes: string[], url: string = '') {\n  return (\n    extractExactPathFromURL(url)\n      // TODO: We should get rid of this, dropping specificities is not good\n      .replace(/^\\//, '')\n  );\n}\n"]}