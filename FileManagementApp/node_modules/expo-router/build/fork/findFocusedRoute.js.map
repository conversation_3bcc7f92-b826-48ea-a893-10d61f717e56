{"version": 3, "file": "findFocusedRoute.js", "sourceRoot": "", "sources": ["../../src/fork/findFocusedRoute.tsx"], "names": [], "mappings": ";AAAA,2FAA2F;;AAI3F,4CAUC;AAVD,SAAgB,gBAAgB,CAAC,KAAmB;IAClD,IAAI,OAAO,GAA6B,KAAK,CAAC;IAE9C,OAAO,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;QACzD,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IACrD,CAAC;IAED,MAAM,KAAK,GAAG,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;IAEnD,OAAO,KAAK,CAAC;AACf,CAAC", "sourcesContent": ["// Forked so we can access without importing any React Native code in Node.js environments.\n\nimport type { InitialState } from '@react-navigation/routers';\n\nexport function findFocusedRoute(state: InitialState) {\n  let current: InitialState | undefined = state;\n\n  while (current?.routes[current.index ?? 0].state != null) {\n    current = current.routes[current.index ?? 0].state;\n  }\n\n  const route = current?.routes[current?.index ?? 0];\n\n  return route;\n}\n"]}