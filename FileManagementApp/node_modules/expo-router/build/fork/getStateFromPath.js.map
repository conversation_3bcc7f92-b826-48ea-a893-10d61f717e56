{"version": 3, "file": "getStateFromPath.js", "sourceRoot": "", "sources": ["../../src/fork/getStateFromPath.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEA,4CA8GC;AAlLD,qDAA6E;AAE7E,gFAA0C;AAE1C,yDAAsD;AAEtD,+DAAiD;AACjD,4CAAkD;AAwClD;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,SAAgB,gBAAgB,CAC9B,IAAY,EACZ,OAA4B;AAC5B,aAAa;AACb,WAAqB,EAAE;AACvB,WAAW;;IAEX,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,GAAG,kBAAkB,CACtE,OAAO;IACP,aAAa;IACb,QAAQ;IACR,WAAW;KACZ,CAAC;IAEF,MAAM,OAAO,GAAG,OAAO,EAAE,OAAO,CAAC;IAEjC,aAAa;IACb,MAAM,QAAQ,GAAG,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,CAAC;IACjE,WAAW;IAEX,aAAa;IACb,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;IAC7D,uBAAuB;IACvB,0EAA0E;IAC1E,sDAAsD;IACtD,6EAA6E;IAE7E,yCAAyC;IACzC,qEAAqE;IACrE,WAAW;IAEX,MAAM,MAAM,GAAG,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,6BAA6B;IAE/E,IAAI,MAAM,EAAE,CAAC;QACX,sCAAsC;QACtC,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC;QAEtE,8DAA8D;QAC9D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC5C,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,kCAAkC;QAClC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;IACtD,CAAC;IAED,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QAC1B,oEAAoE;QACpE,MAAM,MAAM,GAAG,SAAS;aACrB,KAAK,CAAC,GAAG,CAAC;aACV,MAAM,CAAC,OAAO,CAAC;aACf,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACf,MAAM,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACzC,OAAO,EAAE,IAAI,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;QAEL,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,OAAO,uBAAuB,CAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzF,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,SAAS,KAAK,GAAG,EAAE,CAAC;QACtB,uFAAuF;QACvF,yEAAyE;QACzE,aAAa;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;QACxD,8BAA8B;QAC9B,gBAAgB;QAChB,4BAA4B;QAC5B,+BAA+B;QAC/B,mFAAmF;QACnF,gEAAgE;QAChE,QAAQ;QACR,KAAK;QACL,WAAW;QAEX,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,uBAAuB,CAC5B,QAAQ,EACR,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAC1C,aAAa,EACb,OAAO,EACP,QAAQ,CAAC,GAAG,CAAC,IAAI,CAClB,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,MAAiD,CAAC;IACtD,IAAI,OAAkD,CAAC;IAEvD,gEAAgE;IAChE,2FAA2F;IAC3F,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,mBAAmB,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;IAEpF,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACzB,iDAAiD;QACjD,OAAO,GAAG,uBAAuB,CAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC/F,SAAS,GAAG,aAAa,CAAC;QAC1B,MAAM,GAAG,OAAO,CAAC;IACnB,CAAC;IAED,IAAI,OAAO,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACtC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,IAAI,qBAAqB,GAAmD;IAC1E,SAAS;IACT,sBAAsB,EAAE;CACzB,CAAC;AAEF,SAAS,kBAAkB,CACzB,OAAuC;AACvC,aAAa;AACb,gBAA2B;AAC3B,WAAW;;IAEX,sGAAsG;IACtG,8CAA8C;IAC9C,qBAAqB,GAAG,CAAC,OAAO,EAAE,sBAAsB,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAC;IACrF,IAAI;IACJ,gBAAgB;IAEhB,OAAO,qBAAqB,CAAC,CAAC,CAAC,CAAC;AAClC,CAAC;AAED,SAAS,sBAAsB,CAAC,OAAyB,EAAE,gBAA2B;IACpF,IAAI,OAAO,EAAE,CAAC;QACZ,IAAA,2BAAkB,EAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAEhD,MAAM,OAAO,GAAG,oBAAoB,CAAC,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;IAExF,yBAAyB,CAAC,OAAO,CAAC,CAAC;IAEnC,MAAM,iBAAiB,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAEzD,OAAO;QACL,aAAa;QACb,OAAO;QACP,iBAAiB;KAClB,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAyB;IACjD,MAAM,aAAa,GAAyB,EAAE,CAAC;IAE/C,IAAI,OAAO,EAAE,gBAAgB,EAAE,CAAC;QAC9B,aAAa,CAAC,IAAI,CAAC;YACjB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,aAAa,EAAE,EAAE;SAClB,CAAC,CAAC;IACL,CAAC;IAED,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,SAAS,oBAAoB,CAC3B,aAAmC,EACnC,UAAiC,EAAE;AACnC,aAAa;AACb,gBAA2B;AAC3B,WAAW;;IAEX,gEAAgE;IAChE,OAAQ,EAAoB;SACzB,MAAM,CACL,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAClC,uBAAuB,CAAC,GAAG,EAAE,OAAgC,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,CAAC,CACtF,CACF;SACA,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;SACxC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACrD,oBAAoB;IACpB,4BAA4B;IAC5B,8DAA8D;IAC9D,sDAAsD;IAEtD,qEAAqE;IACrE,iFAAiF;IACjF,mCAAmC;IACnC,2EAA2E;IAC3E,MAAM;IAEN,0EAA0E;IAC1E,qBAAqB;IACrB,2CAA2C;IAC3C,iBAAiB;IACjB,MAAM;IAEN,2CAA2C;IAC3C,gBAAgB;IAChB,MAAM;IAEN,yCAAyC;IACzC,yCAAyC;IAEzC,uEAAuE;IACvE,+CAA+C;IAC/C,+BAA+B;IAC/B,kBAAkB;IAClB,QAAQ;IACR,+CAA+C;IAC/C,+BAA+B;IAC/B,mBAAmB;IACnB,QAAQ;IACR,wEAAwE;IACxE,wEAAwE;IACxE,wDAAwD;IACxD,oCAAoC;IACpC,kBAAkB;IAClB,QAAQ;IACR,uDAAuD;IACvD,uBAAuB;IACvB,kBAAkB;IAClB,QAAQ;IACR,uDAAuD;IACvD,uBAAuB;IACvB,mBAAmB;IACnB,QAAQ;IACR,MAAM;IACN,0CAA0C;IAC1C,MAAM;AACR,CAAC;AAED,SAAS,yBAAyB,CAAC,OAAsB;IACvD,6CAA6C;IAC7C,OAAO,CAAC,MAAM,CAA8B,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;QAC1D,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC;YACzC,MAAM,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC;YAE5B,yEAAyE;YACzE,oEAAoE;YACpE,MAAM,UAAU,GACd,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;YAE1F,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,IAAI,KAAK,CACb,iEACE,MAAM,CAAC,OACT,uBAAuB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAClD,KAAK,CACN,wEAAwE,CAC1E,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE;YACxB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,MAAM;SACzB,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,SAAS,qBAAqB,CAAC,OAAsB;IACnD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACzB,GAAG,CAAC;QACJ,2FAA2F;QAC3F,aAAa;QACb,iEAAiE;QACjE,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAC3B,WAAW;KACZ,CAAC,CAAC,CAAC;AACN,CAAC;AAED,MAAM,SAAS,GAAG,CAAC,GAAG,KAAe,EAAU,EAAE,CAC9C,EAAe;KACb,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;KACzC,MAAM,CAAC,OAAO,CAAC;KACf,IAAI,CAAC,GAAG,CAAC,CAAC;AAEf,MAAM,mBAAmB,GAAG,CAAC,SAAiB,EAAE,OAAsB,EAAE,EAAE;IACxE,IAAI,MAAiC,CAAC;IACtC,IAAI,aAAa,GAAG,SAAS,CAAC;IAE9B,aAAa;IACb,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACtC,WAAW;IAEX,6EAA6E;IAC7E,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAClB,SAAS;QACX,CAAC;QAED,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAEhD,gEAAgE;QAChE,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAInD,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE;gBAChB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC3B,OAAO,GAAG,CAAC;gBACb,CAAC;gBAED,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;gBAEb,aAAa;gBACb,MAAM,mBAAmB,GAAG,IAAI,CAAC,wBAAwB;gBACvD,kDAAkD;gBAClD,wFAAwF;gBACxF,KAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACvB,wBAAwB;qBACvB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CACtB,CAAC;gBACF,WAAW;gBAEX,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE;oBAC/B,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE;wBAC7C,CAAC,KAAK,CAAC,EAAE,mBAAmB;qBAC7B,CAAC;iBACH,CAAC,CAAC;gBAEH,OAAO,GAAG,CAAC;YACb,CAAC,EACD,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,aAAa,EAAE,EAAE,EAAE,CAC/B,CAAC;YAEF,MAAM,aAAa,GAAG,WAAW,CAAC,aAAa,IAAI,EAAE,CAAC;YAEtD,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACtC,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;oBACrC,4FAA4F;oBAC5F,OAAO,CAAC,CAAC,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;gBACnE,CAAC,CAAC,CAAC;gBAEH,oFAAoF;gBACpF,MAAM,cAAc,GAAG,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAE9E,oDAAoD;gBACpD,MAAM,kBAAkB,GAAG,WAAW,EAAE,OAAO;oBAC7C,yIAAyI;qBACxI,OAAO,CAAC,IAAI,MAAM,CAAC,GAAG,IAAA,8BAAM,EAAC,cAAe,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;oBACvD,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;gBAEtB,MAAM,MAAM,GAAG,cAAc;oBAC3B,EAAE,KAAK,CAAC,GAAG,CAAC;qBACX,MAAM,CAA0B,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE;oBACjD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC3B,OAAO,GAAG,CAAC;oBACb,CAAC;oBAED,+DAA+D;oBAC/D,iEAAiE;oBACjE,MAAM,MAAM,GAAG,kBAAkB,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/D,aAAa;oBACb,oDAAoD;oBACpD,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC;oBACxE,WAAW;oBAEX,IAAI,KAAK,EAAE,CAAC;wBACV,aAAa;wBACb,sDAAsD;wBACtD,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;wBAChC,WAAW;wBACX,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;oBACtF,CAAC;oBAED,OAAO,GAAG,CAAC;gBACb,CAAC,EAAE,EAAE,CAAC,CAAC;gBAET,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;oBACzC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;oBACjC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;gBAC1B,CAAC;gBAED,OAAO,EAAE,IAAI,EAAE,CAAC;YAClB,CAAC,CAAC,CAAC;YAEH,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAEpD,MAAM;QACR,CAAC;IACH,CAAC;IAED,aAAa;IACb,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACvC,WAAW;IAEX,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;AACnC,CAAC,CAAC;AAEF,MAAM,uBAAuB,GAAG,CAC9B,MAAc,EACd,WAAkC,EAClC,aAAuB,EAAE,EACzB,QAA8B,EAC9B,aAAuB,EACvB,aAAsB,EACP,EAAE;IACjB,MAAM,OAAO,GAAkB,EAAE,CAAC;IAElC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAExB,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAE3B,6IAA6I;IAC7I,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IAEnC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,6FAA6F;QAC7F,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAE1E,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;IACtE,CAAC;SAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QACtC,IAAI,OAA2B,CAAC;QAEhC,8DAA8D;QAC9D,kCAAkC;QAClC,wDAAwD;QACxD,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACpC,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC9C,MAAM,IAAI,KAAK,CACb,sJAAsJ,CACvJ,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,MAAM,CAAC,KAAK,KAAK,IAAI;oBACnB,CAAC,CAAC,SAAS,CAAC,aAAa,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;oBACnD,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;YAExB,IAAI,MAAM,KAAK,8BAAkB,EAAE,CAAC;gBAClC,OAAO,CAAC,IAAI,CACV,gBAAgB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAQ,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAClF,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,+DAA+D;YAC/D,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAC5B,QAAQ,CAAC,IAAI,CAAC;oBACZ,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;oBACzC,aAAa;iBACd,CAAC,CAAC;YACL,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;gBACnD,MAAM,MAAM,GAAG,uBAAuB,CACpC,YAAY,EACZ,MAAM,CAAC,OAAgC,EACvC,UAAU,EACV,QAAQ,EACR,CAAC,GAAG,aAAa,CAAC,EAClB,OAAO,IAAI,aAAa,CACzB,CAAC;gBAEF,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,UAAU,CAAC,GAAG,EAAE,CAAC;IAEjB,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,CACvB,MAAc,EACd,UAAoB,EACpB,OAAe,EACf,IAAY,EACZ,QAAiC,SAAS,EAC1C,SAA8B,EAAE,EACnB,EAAE;IACf,oFAAoF;IACpF,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEvD,aAAa;IACb,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACtE,wBAAwB;IACxB,kBAAkB;IAClB,qBAAqB;IACrB,sBAAsB;IACtB,yBAAyB;IACzB,sCAAsC;IACtC,mEAAmE;IACnE,cAAc;IAEd,2DAA2D;IAC3D,aAAa;IACb,uBAAuB;IACvB,QAAQ;IACR,iBAAiB;IACjB,WAAW;IAEX,OAAO;QACL,MAAM;QACN,KAAK;QACL,OAAO;QACP,IAAI;QACJ,wEAAwE;QACxE,UAAU,EAAE,CAAC,GAAG,UAAU,CAAC;QAC3B,KAAK;QACL,aAAa;QACb,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC;QACzD,WAAW;KACZ,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,uBAAuB,GAAG,CAC9B,SAAiB,EACjB,UAAyB,EACA,EAAE;IAC3B,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE,CAAC;QAChC,IAAI,SAAS,KAAK,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;YAClE,OAAO,MAAM,CAAC,KAAK,CAAC;QACtB,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,6DAA6D;AAC7D,MAAM,gBAAgB,GAAG,CACvB,SAAiB,EACjB,aAAuB,EACvB,aAAmC,EACf,EAAE;IACtB,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;QACnC,IAAI,aAAa,CAAC,MAAM,KAAK,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACzD,IAAI,WAAW,GAAG,IAAI,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;oBAClE,WAAW,GAAG,KAAK,CAAC;oBACpB,MAAM;gBACR,CAAC;YACH,CAAC;YACD,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,SAAS,KAAK,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC;YACrF,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,wDAAwD;AACxD,qEAAqE;AACrE,MAAM,iBAAiB,GAAG,CACxB,YAAgC,EAChC,KAAkB,EAClB,OAAgB,EACF,EAAE;IAChB,IAAI,OAAO,EAAE,CAAC;QACZ,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO;gBACL,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,MAAM,EAAE,CAAC,KAAK,CAAC;aAChB,CAAC;QACJ,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO;gBACL,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE;oBACN,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE;oBAC5C,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE;iBACpC;aACF,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,MAAM,EAAE,CAAC,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC;aAC9C,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,uBAAuB,GAAG,CAC9B,EAAE,IAAI,EAAE,GAAG,OAAO,EAAgE,EAClF,MAAqB,EACrB,aAAmC,EACnC,UAA0B,EAC1B,IAAa,EACb,EAAE;IACF,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,EAAiB,CAAC;IAC1C,MAAM,aAAa,GAAa,EAAE,CAAC;IAEnC,IAAI,YAAY,GAAG,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;IAE9E,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAE/B,MAAM,KAAK,GAAiB,iBAAiB,CAAC,YAAY,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;IAExF,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtB,IAAI,WAAW,GAAG,KAAK,CAAC;QAExB,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,EAAiB,CAAC,EAAE,CAAC;YAC/C,YAAY,GAAG,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;YAE1E,MAAM,gBAAgB,GAAG,WAAW,CAAC,KAAK,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YAE5E,WAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,KAAK,GAAG,iBAAiB,CAC5D,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,MAAM,KAAK,CAAC,CACpB,CAAC;YAEF,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,KAAqB,CAAC;YAC3E,CAAC;YAED,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAED,KAAK,GAAG,IAAA,mCAAgB,EAAC,KAAK,CAAgB,CAAC;IAC/C,aAAa;IACb,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC;IACvC,qBAAqB;IACrB,WAAW;IAEX,aAAa;IACb,mCAAmC;IACnC,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAClC,IAAI,EACJ,KAAK,EACL,UAAU,CAAC,CAAC,CAAC,uBAAuB,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,EACxE,IAAI,CACL,CAAC;IACF,WAAW;IAEX,aAAa;IACb,6CAA6C;IAC7C,IAAI,MAAM,EAAE,CAAC;QACX,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;IAChD,CAAC;IACD,WAAW;IAEX,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,aAAa;AACb,qGAAqG;AACrG,sCAAsC;AACtC,6CAA6C;AAE7C,uBAAuB;AACvB,8CAA8C;AAC9C,iGAAiG;AACjG,oEAAoE;AACpE,UAAU;AACV,UAAU;AACV,MAAM;AAEN,4DAA4D;AAC5D,KAAK;AACL,WAAW", "sourcesContent": ["import { PathConfigMap, validatePathConfig } from '@react-navigation/native';\nimport type { InitialState, NavigationState, PartialState } from '@react-navigation/routers';\nimport escape from 'escape-string-regexp';\n\nimport { findFocusedRoute } from './findFocusedRoute';\nimport type { ExpoOptions, ExpoRouteConfig } from './getStateFromPath-forks';\nimport * as expo from './getStateFromPath-forks';\nimport { INTERNAL_SLOT_NAME } from '../constants';\n\nexport type Options<ParamList extends object> = ExpoOptions & {\n  path?: string;\n  initialRouteName?: string;\n  screens: PathConfigMap<ParamList>;\n};\n\ntype ParseConfig = Record<string, (value: string) => any>;\n\nexport type RouteConfig = ExpoRouteConfig & {\n  screen: string;\n  regex?: RegExp;\n  path: string;\n  pattern: string;\n  routeNames: string[];\n  parse?: ParseConfig;\n};\n\nexport type InitialRouteConfig = {\n  initialRouteName: string;\n  parentScreens: string[];\n};\n\nexport type ResultState = PartialState<NavigationState> & {\n  state?: ResultState;\n};\n\nexport type ParsedRoute = {\n  name: string;\n  path?: string;\n  params?: Record<string, any> | undefined;\n};\n\ntype ConfigResources = {\n  initialRoutes: InitialRouteConfig[];\n  configs: RouteConfig[];\n  configWithRegexes: RouteConfig[];\n};\n\n/**\n * Utility to parse a path string to initial state object accepted by the container.\n * This is useful for deep linking when we need to handle the incoming URL.\n *\n * @example\n * ```js\n * getStateFromPath(\n *   '/chat/jane/42',\n *   {\n *     screens: {\n *       Chat: {\n *         path: 'chat/:author/:id',\n *         parse: { id: Number }\n *       }\n *     }\n *   }\n * )\n * ```\n * @param path Path string to parse and convert, e.g. /foo/bar?count=42.\n * @param options Extra options to fine-tune how to parse the path.\n */\nexport function getStateFromPath<ParamList extends object>(\n  path: string,\n  options?: Options<ParamList>,\n  // START FORK\n  segments: string[] = []\n  // END FORK\n): ResultState | undefined {\n  const { initialRoutes, configs, configWithRegexes } = getConfigResources(\n    options,\n    // START FORK\n    segments\n    // END FORK\n  );\n\n  const screens = options?.screens;\n\n  // START FORK\n  const expoPath = expo.getUrlWithReactNavigationConcessions(path);\n  // END FORK\n\n  // START FORK\n  let remaining = expo.cleanPath(expoPath.nonstandardPathname);\n  // let remaining = path\n  //   .replace(/\\/+/g, '/') // Replace multiple slash (//) with single ones\n  //   .replace(/^\\//, '') // Remove extra leading slash\n  //   .replace(/\\?.*$/, ''); // Remove query params which we will handle later\n\n  // // Make sure there is a trailing slash\n  // remaining = remaining.endsWith('/') ? remaining : `${remaining}/`;\n  // END FORK\n\n  const prefix = options?.path?.replace(/^\\//, ''); // Remove extra leading slash\n\n  if (prefix) {\n    // Make sure there is a trailing slash\n    const normalizedPrefix = prefix.endsWith('/') ? prefix : `${prefix}/`;\n\n    // If the path doesn't start with the prefix, it's not a match\n    if (!remaining.startsWith(normalizedPrefix)) {\n      return undefined;\n    }\n\n    // Remove the prefix from the path\n    remaining = remaining.replace(normalizedPrefix, '');\n  }\n\n  if (screens === undefined) {\n    // When no config is specified, use the path segments as route names\n    const routes = remaining\n      .split('/')\n      .filter(Boolean)\n      .map((segment) => {\n        const name = decodeURIComponent(segment);\n        return { name };\n      });\n\n    if (routes.length) {\n      return createNestedStateObject(expoPath, routes, initialRoutes, [], expoPath.url.hash);\n    }\n\n    return undefined;\n  }\n\n  if (remaining === '/') {\n    // We need to add special handling of empty path so navigation to empty path also works\n    // When handling empty path, we should only look at the root level config\n    // START FORK\n    const match = expo.matchForEmptyPath(configWithRegexes);\n    // const match = configs.find(\n    //   (config) =>\n    //     config.path === '' &&\n    //     config.routeNames.every(\n    //       // Make sure that none of the parent configs have a non-empty path defined\n    //       (name) => !configs.find((c) => c.screen === name)?.path\n    //     )\n    // );\n    // END FORK\n\n    if (match) {\n      return createNestedStateObject(\n        expoPath,\n        match.routeNames.map((name) => ({ name })),\n        initialRoutes,\n        configs,\n        expoPath.url.hash\n      );\n    }\n\n    return undefined;\n  }\n\n  let result: PartialState<NavigationState> | undefined;\n  let current: PartialState<NavigationState> | undefined;\n\n  // We match the whole path against the regex instead of segments\n  // This makes sure matches such as wildcard will catch any unmatched routes, even if nested\n  const { routes, remainingPath } = matchAgainstConfigs(remaining, configWithRegexes);\n\n  if (routes !== undefined) {\n    // This will always be empty if full path matched\n    current = createNestedStateObject(expoPath, routes, initialRoutes, configs, expoPath.url.hash);\n    remaining = remainingPath;\n    result = current;\n  }\n\n  if (current == null || result == null) {\n    return undefined;\n  }\n\n  return result;\n}\n\n/**\n * Reference to the last used config resources. This is used to avoid recomputing the config resources when the options are the same.\n */\nlet cachedConfigResources: [Options<object> | undefined, ConfigResources] = [\n  undefined,\n  prepareConfigResources(),\n];\n\nfunction getConfigResources<ParamList extends object>(\n  options: Options<ParamList> | undefined,\n  // START FORK\n  previousSegments?: string[]\n  // END FORK\n) {\n  // START FORK - We need to disable this caching as our configs can change based upon the current state\n  // if (cachedConfigResources[0] !== options) {\n  cachedConfigResources = [options, prepareConfigResources(options, previousSegments)];\n  // }\n  // END FORK FORK\n\n  return cachedConfigResources[1];\n}\n\nfunction prepareConfigResources(options?: Options<object>, previousSegments?: string[]) {\n  if (options) {\n    validatePathConfig(options);\n  }\n\n  const initialRoutes = getInitialRoutes(options);\n\n  const configs = getNormalizedConfigs(initialRoutes, options?.screens, previousSegments);\n\n  checkForDuplicatedConfigs(configs);\n\n  const configWithRegexes = getConfigsWithRegexes(configs);\n\n  return {\n    initialRoutes,\n    configs,\n    configWithRegexes,\n  };\n}\n\nfunction getInitialRoutes(options?: Options<object>) {\n  const initialRoutes: InitialRouteConfig[] = [];\n\n  if (options?.initialRouteName) {\n    initialRoutes.push({\n      initialRouteName: options.initialRouteName,\n      parentScreens: [],\n    });\n  }\n\n  return initialRoutes;\n}\n\nfunction getNormalizedConfigs(\n  initialRoutes: InitialRouteConfig[],\n  screens: PathConfigMap<object> = {},\n  // START FORK\n  previousSegments?: string[]\n  // END FORK\n) {\n  // Create a normalized configs array which will be easier to use\n  return ([] as RouteConfig[])\n    .concat(\n      ...Object.keys(screens).map((key) =>\n        createNormalizedConfigs(key, screens as PathConfigMap<object>, [], initialRoutes, [])\n      )\n    )\n    .map(expo.appendIsInitial(initialRoutes))\n    .sort(expo.getRouteConfigSorter(previousSegments));\n  // .sort((a, b) => {\n  //   // Sort config so that:\n  //   // - the most exhaustive ones are always at the beginning\n  //   // - patterns with wildcard are always at the end\n\n  //   // If 2 patterns are same, move the one with less route names up\n  //   // This is an error state, so it's only useful for consistent error messages\n  //   if (a.pattern === b.pattern) {\n  //     return b.routeNames.join('>').localeCompare(a.routeNames.join('>'));\n  //   }\n\n  //   // If one of the patterns starts with the other, it's more exhaustive\n  //   // So move it up\n  //   if (a.pattern.startsWith(b.pattern)) {\n  //     return -1;\n  //   }\n\n  //   if (b.pattern.startsWith(a.pattern)) {\n  //     return 1;\n  //   }\n\n  //   const aParts = a.pattern.split('/');\n  //   const bParts = b.pattern.split('/');\n\n  //   for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {\n  //     // if b is longer, b get higher priority\n  //     if (aParts[i] == null) {\n  //       return 1;\n  //     }\n  //     // if a is longer, a get higher priority\n  //     if (bParts[i] == null) {\n  //       return -1;\n  //     }\n  //     const aWildCard = aParts[i] === '*' || aParts[i].startsWith(':');\n  //     const bWildCard = bParts[i] === '*' || bParts[i].startsWith(':');\n  //     // if both are wildcard we compare next component\n  //     if (aWildCard && bWildCard) {\n  //       continue;\n  //     }\n  //     // if only a is wild card, b get higher priority\n  //     if (aWildCard) {\n  //       return 1;\n  //     }\n  //     // if only b is wild card, a get higher priority\n  //     if (bWildCard) {\n  //       return -1;\n  //     }\n  //   }\n  //   return bParts.length - aParts.length;\n  // });\n}\n\nfunction checkForDuplicatedConfigs(configs: RouteConfig[]) {\n  // Check for duplicate patterns in the config\n  configs.reduce<Record<string, RouteConfig>>((acc, config) => {\n    if (acc[config.pattern]) {\n      const a = acc[config.pattern].routeNames;\n      const b = config.routeNames;\n\n      // It's not a problem if the path string omitted from a inner most screen\n      // For example, it's ok if a path resolves to `A > B > C` or `A > B`\n      const intersects =\n        a.length > b.length ? b.every((it, i) => a[i] === it) : a.every((it, i) => b[i] === it);\n\n      if (!intersects) {\n        throw new Error(\n          `Found conflicting screens with the same pattern. The pattern '${\n            config.pattern\n          }' resolves to both '${a.join(' > ')}' and '${b.join(\n            ' > '\n          )}'. Patterns must be unique and cannot resolve to more than one screen.`\n        );\n      }\n    }\n\n    return Object.assign(acc, {\n      [config.pattern]: config,\n    });\n  }, {});\n}\n\nfunction getConfigsWithRegexes(configs: RouteConfig[]) {\n  return configs.map((c) => ({\n    ...c,\n    // Add `$` to the regex to make sure it matches till end of the path and not just beginning\n    // START FORK\n    // regex: c.regex ? new RegExp(c.regex.source + '$') : undefined,\n    regex: expo.configRegExp(c),\n    // END FORK\n  }));\n}\n\nconst joinPaths = (...paths: string[]): string =>\n  ([] as string[])\n    .concat(...paths.map((p) => p.split('/')))\n    .filter(Boolean)\n    .join('/');\n\nconst matchAgainstConfigs = (remaining: string, configs: RouteConfig[]) => {\n  let routes: ParsedRoute[] | undefined;\n  let remainingPath = remaining;\n\n  // START FORK\n  const allParams = Object.create(null);\n  // END FORK\n\n  // Go through all configs, and see if the next path segment matches our regex\n  for (const config of configs) {\n    if (!config.regex) {\n      continue;\n    }\n\n    const match = remainingPath.match(config.regex);\n\n    // If our regex matches, we need to extract params from the path\n    if (match) {\n      const matchResult = config.pattern?.split('/').reduce<{\n        pos: number; // Position of the current path param segment in the path (e.g in pattern `a/:b/:c`, `:a` is 0 and `:b` is 1)\n        matchedParams: Record<string, Record<string, string>>; // The extracted params\n      }>(\n        (acc, p, index) => {\n          if (!expo.isDynamicPart(p)) {\n            return acc;\n          }\n\n          acc.pos += 1;\n\n          // START FORK\n          const decodedParamSegment = expo.safelyDecodeURIComponent(\n            // const decodedParamSegment = decodeURIComponent(\n            // The param segments appear every second item starting from 2 in the regex match result\n            match![(acc.pos + 1) * 2]\n              // Remove trailing slash\n              .replace(/\\/$/, '')\n          );\n          // END FORK\n\n          Object.assign(acc.matchedParams, {\n            [p]: Object.assign(acc.matchedParams[p] || {}, {\n              [index]: decodedParamSegment,\n            }),\n          });\n\n          return acc;\n        },\n        { pos: -1, matchedParams: {} }\n      );\n\n      const matchedParams = matchResult.matchedParams || {};\n\n      routes = config.routeNames.map((name) => {\n        const routeConfig = configs.find((c) => {\n          // Check matching name AND pattern in case same screen is used at different levels in config\n          return c.screen === name && config.pattern.startsWith(c.pattern);\n        });\n\n        // Normalize pattern to remove any leading, trailing slashes, duplicate slashes etc.\n        const normalizedPath = routeConfig?.path.split('/').filter(Boolean).join('/');\n\n        // Get the number of segments in the initial pattern\n        const numInitialSegments = routeConfig?.pattern\n          // Extract the prefix from the pattern by removing the ending path pattern (e.g pattern=`a/b/c/d` and normalizedPath=`c/d` becomes `a/b`)\n          .replace(new RegExp(`${escape(normalizedPath!)}$`), '')\n          ?.split('/').length;\n\n        const params = normalizedPath\n          ?.split('/')\n          .reduce<Record<string, unknown>>((acc, p, index) => {\n            if (!expo.isDynamicPart(p)) {\n              return acc;\n            }\n\n            // Get the real index of the path parameter in the matched path\n            // by offsetting by the number of segments in the initial pattern\n            const offset = numInitialSegments ? numInitialSegments - 1 : 0;\n            // START FORK\n            // const value = matchedParams[p]?.[index + offset];\n            const value = expo.getParamValue(p, matchedParams[p]?.[index + offset]);\n            // END FORK\n\n            if (value) {\n              // START FORK\n              // const key = p.replace(/^:/, '').replace(/\\?$/, '');\n              const key = expo.replacePart(p);\n              // END FORK\n              acc[key] = routeConfig?.parse?.[key] ? routeConfig.parse[key](value as any) : value;\n            }\n\n            return acc;\n          }, {});\n\n        if (params && Object.keys(params).length) {\n          Object.assign(allParams, params);\n          return { name, params };\n        }\n\n        return { name };\n      });\n\n      remainingPath = remainingPath.replace(match[1], '');\n\n      break;\n    }\n  }\n\n  // START FORK\n  expo.populateParams(routes, allParams);\n  // END FORK\n\n  return { routes, remainingPath };\n};\n\nconst createNormalizedConfigs = (\n  screen: string,\n  routeConfig: PathConfigMap<object>,\n  routeNames: string[] = [],\n  initials: InitialRouteConfig[],\n  parentScreens: string[],\n  parentPattern?: string\n): RouteConfig[] => {\n  const configs: RouteConfig[] = [];\n\n  routeNames.push(screen);\n\n  parentScreens.push(screen);\n\n  // @ts-expect-error: TODO(@kitten): This is entirely untyped. The index access just flags this, but we're not typing the config properly here\n  const config = routeConfig[screen];\n\n  if (typeof config === 'string') {\n    // If a string is specified as the value of the key(e.g. Foo: '/path'), use it as the pattern\n    const pattern = parentPattern ? joinPaths(parentPattern, config) : config;\n\n    configs.push(createConfigItem(screen, routeNames, pattern, config));\n  } else if (typeof config === 'object') {\n    let pattern: string | undefined;\n\n    // if an object is specified as the value (e.g. Foo: { ... }),\n    // it can have `path` property and\n    // it could have `screens` prop which has nested configs\n    if (typeof config.path === 'string') {\n      if (config.exact && config.path === undefined) {\n        throw new Error(\n          \"A 'path' needs to be specified when specifying 'exact: true'. If you don't want this screen in the URL, specify it as empty string, e.g. `path: ''`.\"\n        );\n      }\n\n      pattern =\n        config.exact !== true\n          ? joinPaths(parentPattern || '', config.path || '')\n          : config.path || '';\n\n      if (screen !== INTERNAL_SLOT_NAME) {\n        configs.push(\n          createConfigItem(screen, routeNames, pattern!, config.path, config.parse, config)\n        );\n      }\n    }\n\n    if (config.screens) {\n      // property `initialRouteName` without `screens` has no purpose\n      if (config.initialRouteName) {\n        initials.push({\n          initialRouteName: config.initialRouteName,\n          parentScreens,\n        });\n      }\n\n      Object.keys(config.screens).forEach((nestedConfig) => {\n        const result = createNormalizedConfigs(\n          nestedConfig,\n          config.screens as PathConfigMap<object>,\n          routeNames,\n          initials,\n          [...parentScreens],\n          pattern ?? parentPattern\n        );\n\n        configs.push(...result);\n      });\n    }\n  }\n\n  routeNames.pop();\n\n  return configs;\n};\n\nconst createConfigItem = (\n  screen: string,\n  routeNames: string[],\n  pattern: string,\n  path: string,\n  parse: ParseConfig | undefined = undefined,\n  config: Record<string, any> = {}\n): RouteConfig => {\n  // Normalize pattern to remove any leading, trailing slashes, duplicate slashes etc.\n  pattern = pattern.split('/').filter(Boolean).join('/');\n\n  // START FORK\n  const regex = pattern ? expo.routePatternToRegex(pattern) : undefined;\n  // const regex = pattern\n  //   ? new RegExp(\n  //       `^(${pattern\n  //         .split('/')\n  //         .map((it) => {\n  //           if (it.startsWith(':')) {\n  //             return `(([^/]+\\\\/)${it.endsWith('?') ? '?' : ''})`;\n  //           }\n\n  //           return `${it === '*' ? '.*' : escape(it)}\\\\/`;\n  //         })\n  //         .join('')})`\n  //     )\n  //   : undefined;\n  // END FORK\n\n  return {\n    screen,\n    regex,\n    pattern,\n    path,\n    // The routeNames array is mutated, so copy it to keep the current state\n    routeNames: [...routeNames],\n    parse,\n    // START FORK\n    ...expo.createConfig(screen, pattern, routeNames, config),\n    // END FORK\n  };\n};\n\nconst findParseConfigForRoute = (\n  routeName: string,\n  flatConfig: RouteConfig[]\n): ParseConfig | undefined => {\n  for (const config of flatConfig) {\n    if (routeName === config.routeNames[config.routeNames.length - 1]) {\n      return config.parse;\n    }\n  }\n\n  return undefined;\n};\n\n// Try to find an initial route connected with the one passed\nconst findInitialRoute = (\n  routeName: string,\n  parentScreens: string[],\n  initialRoutes: InitialRouteConfig[]\n): string | undefined => {\n  for (const config of initialRoutes) {\n    if (parentScreens.length === config.parentScreens.length) {\n      let sameParents = true;\n      for (let i = 0; i < parentScreens.length; i++) {\n        if (parentScreens[i].localeCompare(config.parentScreens[i]) !== 0) {\n          sameParents = false;\n          break;\n        }\n      }\n      if (sameParents) {\n        return routeName !== config.initialRouteName ? config.initialRouteName : undefined;\n      }\n    }\n  }\n  return undefined;\n};\n\n// returns state object with values depending on whether\n// it is the end of state and if there is initialRoute for this level\nconst createStateObject = (\n  initialRoute: string | undefined,\n  route: ParsedRoute,\n  isEmpty: boolean\n): InitialState => {\n  if (isEmpty) {\n    if (initialRoute) {\n      return {\n        index: 1,\n        routes: [{ name: initialRoute, params: route.params }, route],\n      };\n    } else {\n      return {\n        routes: [route],\n      };\n    }\n  } else {\n    if (initialRoute) {\n      return {\n        index: 1,\n        routes: [\n          { name: initialRoute, params: route.params },\n          { ...route, state: { routes: [] } },\n        ],\n      };\n    } else {\n      return {\n        routes: [{ ...route, state: { routes: [] } }],\n      };\n    }\n  }\n};\n\nconst createNestedStateObject = (\n  { path, ...expoURL }: ReturnType<typeof expo.getUrlWithReactNavigationConcessions>,\n  routes: ParsedRoute[],\n  initialRoutes: InitialRouteConfig[],\n  flatConfig?: RouteConfig[],\n  hash?: string\n) => {\n  let route = routes.shift() as ParsedRoute;\n  const parentScreens: string[] = [];\n\n  let initialRoute = findInitialRoute(route.name, parentScreens, initialRoutes);\n\n  parentScreens.push(route.name);\n\n  const state: InitialState = createStateObject(initialRoute, route, routes.length === 0);\n\n  if (routes.length > 0) {\n    let nestedState = state;\n\n    while ((route = routes.shift() as ParsedRoute)) {\n      initialRoute = findInitialRoute(route.name, parentScreens, initialRoutes);\n\n      const nestedStateIndex = nestedState.index || nestedState.routes.length - 1;\n\n      nestedState.routes[nestedStateIndex].state = createStateObject(\n        initialRoute,\n        route,\n        routes.length === 0\n      );\n\n      if (routes.length > 0) {\n        nestedState = nestedState.routes[nestedStateIndex].state as InitialState;\n      }\n\n      parentScreens.push(route.name);\n    }\n  }\n\n  route = findFocusedRoute(state) as ParsedRoute;\n  // START FORK\n  route.path = expoURL.pathWithoutGroups;\n  // route.path = path;\n  // END FORK\n\n  // START FORK\n  // const params = parseQueryParams(\n  const params = expo.parseQueryParams(\n    path,\n    route,\n    flatConfig ? findParseConfigForRoute(route.name, flatConfig) : undefined,\n    hash\n  );\n  // END FORK\n\n  // START FORK\n  // expo.handleUrlParams(route, params, hash);\n  if (params) {\n    route.params = { ...route.params, ...params };\n  }\n  // END FORK\n\n  return state;\n};\n\n// START FORK\n// const parseQueryParams = (path: string, parseConfig?: Record<string, (value: string) => any>) => {\n//   const query = path.split('?')[1];\n//   const params = queryString.parse(query);\n\n//   if (parseConfig) {\n//     Object.keys(params).forEach((name) => {\n//       if (Object.hasOwnProperty.call(parseConfig, name) && typeof params[name] === 'string') {\n//         params[name] = parseConfig[name](params[name] as string);\n//       }\n//     });\n//   }\n\n//   return Object.keys(params).length ? params : undefined;\n// };\n// END FORK\n"]}