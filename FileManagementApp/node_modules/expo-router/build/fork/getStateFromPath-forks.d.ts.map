{"version": 3, "file": "getStateFromPath-forks.d.ts", "sourceRoot": "", "sources": ["../../src/fork/getStateFromPath-forks.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAExD,OAAO,KAAK,WAAW,MAAM,cAAc,CAAC;AAE5C,OAAO,KAAK,EAAE,kBAAkB,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAGhG,MAAM,MAAM,WAAW,GAAG;IACxB,gBAAgB,CAAC,EAAE,MAAM,EAAE,CAAC;CAC7B,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG;IAC5B,IAAI,EAAE,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;IACtC,gBAAgB,EAAE,MAAM,CAAC;IACzB,OAAO,EAAE,OAAO,CAAC;IACjB,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,WAAW,EAAE,OAAO,CAAC;IACrB,kBAAkB,EAAE,MAAM,EAAE,CAAC;IAC7B,KAAK,EAAE,MAAM,EAAE,CAAC;IAChB,eAAe,EAAE,MAAM,CAAC;CACzB,CAAC;AAEF;;;;GAIG;AACH,wBAAgB,cAAc,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,6BAQlF;AAED,wBAAgB,wBAAwB,CAAC,GAAG,EAAE,MAAM,UAMnD;AAED,wBAAgB,oCAAoC,CAClD,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE,MAAM,GAAG,SAAqC;;;;;;;;;;;;EA2BxD;AAED,wBAAgB,YAAY,CAC1B,MAAM,EAAE,MAAM,EACd,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAAE,EACpB,MAAM,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,GAC/B,IAAI,CAAC,eAAe,EAAE,WAAW,CAAC,CA4CpC;AAED,wBAAgB,aAAa,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,CAI3F;AAED,wBAAgB,YAAY,CAAC,MAAM,EAAE,WAAW,sBAI/C;AAED,wBAAgB,aAAa,CAAC,CAAC,EAAE,MAAM,WAEtC;AAED,wBAAgB,WAAW,CAAC,CAAC,EAAE,MAAM,UAEpC;AAED,wBAAgB,aAAa,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,iCAOrD;AAwBD,wBAAgB,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,MAAM,CAAC,EAAE,WAAW,CAAC,WAAW,QAsBnF;AAED,wBAAgB,2BAA2B,CAAC,KAAK,EAAE,YAAY,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,QAK5F;AAED,wBAAgB,YAAY,CAC1B,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE,MAAM,GAAG,SAAqC,UAQxD;AAED,wBAAgB,iBAAiB,CAAC,OAAO,EAAE,WAAW,EAAE;;UAjNhD,QAAQ,GAAG,SAAS,GAAG,QAAQ;sBACnB,MAAM;aACf,OAAO;gBACJ,OAAO;iBACN,OAAO;wBACA,MAAM,EAAE;WACrB,MAAM,EAAE;qBACE,MAAM;;;;;;;;cA0OxB;AAED,wBAAgB,eAAe,CAAC,aAAa,EAAE,kBAAkB,EAAE,IAKhD,QAAQ,WAAW,iBAMrC;AAQD,wBAAgB,oBAAoB,CAAC,gBAAgB,GAAE,MAAM,EAAO,IACtC,GAAG,WAAW,EAAE,GAAG,WAAW,YA2J3D;AAED,wBAAgB,gBAAgB,CAC9B,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,WAAW,EAClB,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,GAAG,CAAC,EACpD,IAAI,CAAC,EAAE,MAAM,iDA4Bd;AAED,wBAAgB,SAAS,CAAC,IAAI,EAAE,MAAM,UAUrC;AAED,wBAAgB,mBAAmB,CAAC,OAAO,EAAE,MAAM,UAelD"}