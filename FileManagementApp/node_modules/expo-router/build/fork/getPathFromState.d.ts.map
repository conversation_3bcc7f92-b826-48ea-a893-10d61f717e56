{"version": 3, "file": "getPathFromState.d.ts", "sourceRoot": "", "sources": ["../../src/fork/getPathFromState.ts"], "names": [], "mappings": "AAAA,OAAO,EAAc,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACrE,OAAO,KAAK,EAAE,eAAe,EAAE,YAAY,EAAS,MAAM,2BAA2B,CAAC;AAItF,OAAO,KAAK,EAAkB,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAG5E,MAAM,MAAM,OAAO,CAAC,SAAS,SAAS,MAAM,IAAI,WAAW,GAAG;IAC5D,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,OAAO,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;CACnC,CAAC;AAGF,MAAM,MAAM,KAAK,GAAG,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC,CAAC;AAEnF,MAAM,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,MAAM,CAAC,CAAC;AA4BrE;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,wBAAgB,gBAAgB,CAAC,SAAS,SAAS,MAAM,EACvD,KAAK,EAAE,KAAK,EACZ,OAAO,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,GAC3B,MAAM,CAER;AAED,wBAAgB,oBAAoB,CAAC,SAAS,SAAS,MAAM,EAC3D,KAAK,EAAE,KAAK,EACZ,OAAO,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC;;;EAyP7B;AAsDD,wBAAgB,aAAa,CAC3B,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE,MAAM,GAAG,SAAqC,UAQxD"}