{"version": 3, "file": "getPathFromState.js", "sourceRoot": "", "sources": ["../../src/fork/getPathFromState.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA,4CAKC;AAED,oDA2PC;AAsDD,sCAUC;AA1YD,0DAA4C;AAE5C,+DAAiD;AAqBjD,WAAW;AAEX,MAAM,cAAc,GAAG,CAAC,KAAY,EAAqC,EAAE;IACzE,MAAM,KAAK,GACT,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ;QAC7B,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;QAC3B,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAE5C,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,OAAO,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,IAAI,uBAAuB,GAAoE;IAC7F,SAAS;IACT,EAAE;CACH,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,SAAgB,gBAAgB,CAC9B,KAAY,EACZ,OAA4B;IAE5B,OAAO,oBAAoB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC;AACnD,CAAC;AAED,SAAgB,oBAAoB,CAClC,KAAY,EACZ,OAA4B;IAE5B,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,MAAM,KAAK,CAAC,+EAA+E,CAAC,CAAC;IAC/F,CAAC;IAED,IAAI,OAAO,EAAE,CAAC;QACZ,aAAa;QACb,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACjC,+BAA+B;QAC/B,WAAW;IACb,CAAC;IAED,iEAAiE;IACjE,IAAI,uBAAuB,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE,OAAO,EAAE,CAAC;QACpD,uBAAuB,GAAG;YACxB,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,uBAAuB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;SACjE,CAAC;IACJ,CAAC;IACD,MAAM,OAAO,GAA+B,uBAAuB,CAAC,CAAC,CAAC,CAAC;IAEvE,IAAI,IAAI,GAAG,GAAG,CAAC;IACf,IAAI,OAAO,GAAsB,KAAK,CAAC;IAEvC,MAAM,SAAS,GAAwB,EAAE,CAAC;IAE1C,OAAO,OAAO,EAAE,CAAC;QACf,IAAI,KAAK,GAAG,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAE/B,CAAC;QAEF,IAAI,OAA2B,CAAC;QAEhC,IAAI,aAA8C,CAAC;QACnD,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;QAC3C,IAAI,cAAc,GAAG,OAAO,CAAC;QAE7B,oHAAoH;QACpH,MAAM,gBAAgB,GAAa,EAAE,CAAC;QAEtC,IAAI,OAAO,GAAG,IAAI,CAAC;QAEnB,OAAO,KAAK,CAAC,IAAI,IAAI,cAAc,IAAI,OAAO,EAAE,CAAC;YAC/C,OAAO,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;YAE7C,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAElC,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,SAAS,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC;gBAExD,aAAa;gBACb,yBAAyB;gBACzB,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;gBAEzE,4CAA4C;gBAC5C,yDAAyD;gBACzD,WAAW;gBACX,gEAAgE;gBAChE,OAAO;gBACP,KAAK;gBAEL,iBAAiB;gBACjB,6CAA6C;gBAC7C,IAAI;gBACJ,WAAW;gBAEX,IAAI,YAAY,KAAK,KAAK,EAAE,CAAC;oBAC3B,8DAA8D;oBAC9D,sDAAsD;oBACtD,aAAa,GAAG,EAAE,GAAG,aAAa,EAAE,CAAC;oBAErC,OAAO;wBACL,EAAE,KAAK,CAAC,GAAG,CAAC;yBACX,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;wBACrC,wCAAwC;yBACvC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;wBACb,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;wBAElC,0FAA0F;wBAC1F,IAAI,aAAa,EAAE,CAAC;4BAClB,gEAAgE;4BAChE,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;wBAC7B,CAAC;oBACH,CAAC,CAAC,CAAC;gBACP,CAAC;YACH,CAAC;YAED,0EAA0E;YAC1E,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBACrE,aAAa;gBACb,kFAAkF;gBAClF,0EAA0E;gBAC1E,0GAA0G;gBAE1G,uFAAuF;gBACvF,2GAA2G;gBAC3G,WAAW;gBAEX,WAAW;gBACX,kBAAkB;gBAClB,wEAAwE;gBACxE,wEAAwE;gBACxE,wEAAwE;gBACxE,EAAE;gBACF,6EAA6E;gBAC7E,gFAAgF;gBAChF,MAAM,OAAO,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAEnD,kHAAkH;gBAClH,kFAAkF;gBAClF,MAAM,YAAY;gBAChB,qEAAqE;gBACrE,KAAK,CAAC,MAAM,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,QAAQ;oBACjF,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;oBACrB,CAAC,CAAC,OAAO,CAAC;gBAEd,iFAAiF;gBACjF,2FAA2F;gBAC3F,2CAA2C;gBAC3C,MAAM,MAAM,GAAG,OAAO;oBACpB,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;wBACrB,CAAC,CAAC,YAAY;wBACd,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBAC3B,CAAC,CAAC,SAAS,CAAC;gBAEd,IAAI,MAAM,IAAI,OAAO,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;oBACtE,KAAK,GAAG,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;oBAC1D,cAAc,GAAG,OAAO,CAAC;gBAC3B,CAAC;qBAAM,CAAC;oBACN,OAAO,GAAG,KAAK,CAAC;gBAClB,CAAC;gBACD,mBAAmB;gBACnB,WAAW;YACb,CAAC;iBAAM,CAAC;gBACN,KAAK;oBACH,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;gBAE5F,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5C,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;gBAExD,uDAAuD;gBACvD,IAAI,YAAY,IAAI,SAAS,CAAC,IAAI,IAAI,YAAY,EAAE,CAAC;oBACnD,KAAK,GAAG,SAA8C,CAAC;oBACvD,cAAc,GAAG,YAAY,CAAC;gBAChC,CAAC;qBAAM,CAAC;oBACN,sDAAsD;oBACtD,OAAO,GAAG,KAAK,CAAC;gBAClB,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,OAAO,GAAG,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;YAC7C,aAAa;YACb,IAAI,IAAI,IAAI,CAAC,+BAA+B,CAAC;gBAC3C,GAAG,OAAO;gBACV,OAAO;gBACP,KAAK;gBACL,MAAM,EAAE,SAAS;gBACjB,gBAAgB,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,gBAAgB;aACxD,CAAC,CAAC;YACH,kBAAkB;YAClB,gBAAgB;YAChB,kBAAkB;YAClB,oCAAoC;YAEpC,0DAA0D;YAC1D,yFAAyF;YACzF,+CAA+C;YAC/C,uBAAuB;YACvB,2BAA2B;YAC3B,QAAQ;YAER,0EAA0E;YAC1E,+BAA+B;YAC/B,uCAAuC;YAEvC,sDAAsD;YACtD,sFAAsF;YACtF,qBAAqB;YACrB,UAAU;YAEV,yCAAyC;YACzC,4FAA4F;YAC5F,kFAAkF;YAClF,mCAAmC;YACnC,WAAW;YACX,QAAQ;YAER,oCAAoC;YACpC,OAAO;YACP,gBAAgB;YAChB,WAAW;QACb,CAAC;aAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACvC,IAAI,IAAI,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;QACD,WAAW;QAEX,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC;QACtC,CAAC;QAED,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,IAAI,GAAG,CAAC;QACd,CAAC;aAAM,IAAI,aAAa,EAAE,CAAC;YACzB,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;gBAClC,IAAI,aAAa,CAAC,KAAK,CAAC,KAAK,WAAW,EAAE,CAAC;oBACzC,gEAAgE;oBAChE,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;YAED,aAAa;YACb,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC;YAC1B,WAAW;YAEX,MAAM,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YACpE,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,IAAI,IAAI,KAAK,EAAE,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC;IACxB,CAAC;IAED,8CAA8C;IAC9C,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACjC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAExD,qCAAqC;IACrC,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;QAClB,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC;IAED,aAAa;IACb,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAChC,IAAI,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;QACnB,IAAI,IAAI,IAAI,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;IAC/B,CAAC;IACD,WAAW;IAEX,aAAa;IACb,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;IACnC,WAAW;AACb,CAAC;AAED,0FAA0F;AAE1F,MAAM,SAAS,GAAG,CAAC,GAAG,KAAe,EAAU,EAAE,CAC9C,EAAe;KACb,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;KACzC,MAAM,CAAC,OAAO,CAAC;KACf,IAAI,CAAC,GAAG,CAAC,CAAC;AAEf,MAAM,gBAAgB,GAAG,CACvB,MAAmC,EACnC,aAAsB,EACV,EAAE;IACd,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,6FAA6F;QAC7F,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAE1E,OAAO,EAAE,OAAO,EAAE,CAAC;IACrB,CAAC;IAED,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC9C,MAAM,IAAI,KAAK,CACb,sJAAsJ,CACvJ,CAAC;IACJ,CAAC;IAED,8DAA8D;IAC9D,0EAA0E;IAC1E,MAAM,OAAO,GACX,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;IAEhG,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,uBAAuB,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAE9F,OAAO;QACL,oFAAoF;QACpF,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QACtD,SAAS,EAAE,MAAM,CAAC,SAAS;QAC3B,OAAO;KACR,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,uBAAuB,GAAG,CAC9B,OAA8B,EAC9B,OAAgB,EACY,EAAE,CAC9B,MAAM,CAAC,WAAW,CAChB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE;IACxC,MAAM,MAAM,GAAG,gBAAgB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAE5C,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACxB,CAAC,CAAC,CACH,CAAC;AAEJ,SAAgB,aAAa,CAC3B,IAAY,EACZ,UAA8B,OAAO,CAAC,GAAG,CAAC,aAAa;IAEvD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC;QACrE,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import { PathConfig, PathConfigMap } from '@react-navigation/native';\nimport type { NavigationState, PartialState, Route } from '@react-navigation/routers';\nimport * as queryString from 'query-string';\n\nimport * as expo from './getPathFromState-forks';\nimport type { ExpoConfigItem, ExpoOptions } from './getPathFromState-forks';\n\n// START FORK\nexport type Options<ParamList extends object> = ExpoOptions & {\n  path?: string;\n  initialRouteName?: string;\n  screens: PathConfigMap<ParamList>;\n};\n// END FORK\n\nexport type State = NavigationState | Omit<PartialState<NavigationState>, 'stale'>;\n\nexport type StringifyConfig = Record<string, (value: any) => string>;\n\n// START FORK\ntype ConfigItem = ExpoConfigItem & {\n  pattern?: string;\n  stringify?: StringifyConfig;\n  screens?: Record<string, ConfigItem>;\n};\n// END FORK\n\nconst getActiveRoute = (state: State): { name: string; params?: object } => {\n  const route =\n    typeof state.index === 'number'\n      ? state.routes[state.index]\n      : state.routes[state.routes.length - 1];\n\n  if (route.state) {\n    return getActiveRoute(route.state);\n  }\n\n  return route;\n};\n\nlet cachedNormalizedConfigs: [PathConfigMap<object> | undefined, Record<string, ConfigItem>] = [\n  undefined,\n  {},\n];\n\n/**\n * Utility to serialize a navigation state object to a path string.\n *\n * @example\n * ```js\n * getPathFromState(\n *   {\n *     routes: [\n *       {\n *         name: 'Chat',\n *         params: { author: 'Jane', id: 42 },\n *       },\n *     ],\n *   },\n *   {\n *     screens: {\n *       Chat: {\n *         path: 'chat/:author/:id',\n *         stringify: { author: author => author.toLowerCase() }\n *       }\n *     }\n *   }\n * )\n * ```\n *\n * @param state Navigation state to serialize.\n * @param options Extra options to fine-tune how to serialize the path.\n * @returns Path representing the state, e.g. /foo/bar?count=42.\n */\nexport function getPathFromState<ParamList extends object>(\n  state: State,\n  options?: Options<ParamList>\n): string {\n  return getPathDataFromState(state, options).path;\n}\n\nexport function getPathDataFromState<ParamList extends object>(\n  state: State,\n  options?: Options<ParamList>\n) {\n  if (state == null) {\n    throw Error(\"Got 'undefined' for the navigation state. You must pass a valid state object.\");\n  }\n\n  if (options) {\n    // START FORK\n    expo.validatePathConfig(options);\n    // validatePathConfig(options);\n    // END FORK\n  }\n\n  // Create a normalized configs object which will be easier to use\n  if (cachedNormalizedConfigs[0] !== options?.screens) {\n    cachedNormalizedConfigs = [\n      options?.screens,\n      options?.screens ? createNormalizedConfigs(options.screens) : {},\n    ];\n  }\n  const configs: Record<string, ConfigItem> = cachedNormalizedConfigs[1];\n\n  let path = '/';\n  let current: State | undefined = state;\n\n  const allParams: Record<string, any> = {};\n\n  while (current) {\n    let index = typeof current.index === 'number' ? current.index : 0;\n    let route = current.routes[index] as Route<string> & {\n      state?: State;\n    };\n\n    let pattern: string | undefined;\n\n    let focusedParams: Record<string, any> | undefined;\n    const focusedRoute = getActiveRoute(state);\n    let currentOptions = configs;\n\n    // Keep all the route names that appeared during going deeper in config in case the pattern is resolved to undefined\n    const nestedRouteNames: string[] = [];\n\n    let hasNext = true;\n\n    while (route.name in currentOptions && hasNext) {\n      pattern = currentOptions[route.name].pattern;\n\n      nestedRouteNames.push(route.name);\n\n      if (route.params) {\n        const stringify = currentOptions[route.name]?.stringify;\n\n        // START FORK\n        // This mutates allParams\n        const currentParams = expo.fixCurrentParams(allParams, route, stringify);\n\n        // const currentParams = Object.fromEntries(\n        //   Object.entries(route.params).map(([key, value]) => [\n        //     key,\n        //     stringify?.[key] ? stringify[key](value) : String(value),\n        //   ])\n        // );\n\n        // if (pattern) {\n        //   Object.assign(allParams, currentParams);\n        // }\n        // END FORK\n\n        if (focusedRoute === route) {\n          // If this is the focused route, keep the params for later use\n          // We save it here since it's been stringified already\n          focusedParams = { ...currentParams };\n\n          pattern\n            ?.split('/')\n            .filter((p) => expo.isDynamicPart(p))\n            // eslint-disable-next-line no-loop-func\n            .forEach((p) => {\n              const name = expo.getParamName(p);\n\n              // Remove the params present in the pattern since we'll only use the rest for query string\n              if (focusedParams) {\n                // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n                delete focusedParams[name];\n              }\n            });\n        }\n      }\n\n      // If there is no `screens` property or no nested state, we return pattern\n      if (!currentOptions[route.name].screens || route.state === undefined) {\n        // START FORK\n        // Expo Router allows you to navigate to a (group) and not specify a target screen\n        // This is different from React Navigation, which requires a target screen\n        // We need to handle this case here, by selecting either the index screen or the first screen of the group\n\n        // IMPORTANT: This does not affect groups that use _layout files with initialRouteNames\n        // Layout files create a new route config. This only affects groups without layouts that have their screens\n        // hoisted.\n\n        // Example:\n        // - /home/<USER>\n        // - /home/<USER>/index          --> Hoisted to /home/<USER>\n        // - /home/<USER>/other          --> Hoisted to /home/<USER>\n        // - /home/<USER>/me           --> Hoisted to /home/<USER>\n        //\n        // route.push('/home/<USER>')        --> This should navigate to /home/<USER>/index\n        // route.push('/home/<USER>')  --> This should navigate to /home/<USER>/me\n        const screens = currentOptions[route.name].screens;\n\n        // Determine what screen the user wants to navigate to. If no screen is specified, assume there is an index screen\n        // In the examples above, this ensures that /home/<USER>/home/<USER>/index\n        const targetScreen =\n          // This is typed as unknown, so we need to add these extra assertions\n          route.params && 'screen' in route.params && typeof route.params.screen === 'string'\n            ? route.params.screen\n            : 'index';\n\n        // If the target screen is not in the screens object, default to the first screen\n        // In the examples above, this ensures that /home/<USER>/home/<USER>/me\n        // As there is no index screen in the group\n        const screen = screens\n          ? screens[targetScreen]\n            ? targetScreen\n            : Object.keys(screens)[0]\n          : undefined;\n\n        if (screen && screens && currentOptions[route.name].screens?.[screen]) {\n          route = { ...screens[screen], name: screen, key: screen };\n          currentOptions = screens;\n        } else {\n          hasNext = false;\n        }\n        // hasNext = false;\n        // END FORK\n      } else {\n        index =\n          typeof route.state.index === 'number' ? route.state.index : route.state.routes.length - 1;\n\n        const nextRoute = route.state.routes[index];\n        const nestedConfig = currentOptions[route.name].screens;\n\n        // if there is config for next route name, we go deeper\n        if (nestedConfig && nextRoute.name in nestedConfig) {\n          route = nextRoute as Route<string> & { state?: State };\n          currentOptions = nestedConfig;\n        } else {\n          // If not, there is no sense in going deeper in config\n          hasNext = false;\n        }\n      }\n    }\n\n    if (pattern === undefined) {\n      pattern = nestedRouteNames.join('/');\n    }\n\n    if (currentOptions[route.name] !== undefined) {\n      // START FORK\n      path += expo.getPathWithConventionsCollapsed({\n        ...options,\n        pattern,\n        route,\n        params: allParams,\n        initialRouteName: configs[route.name]?.initialRouteName,\n      });\n      // path += pattern\n      //   .split('/')\n      //   .map((p) => {\n      //     const name = getParamName(p);\n\n      //     // We don't know what to show for wildcard patterns\n      //     // Showing the route name seems ok, though whatever we show here will be incorrect\n      //     // Since the page doesn't actually exist\n      //     if (p === '*') {\n      //       return route.name;\n      //     }\n\n      //     // If the path has a pattern for a param, put the param in the path\n      //     if (p.startsWith(':')) {\n      //       const value = allParams[name];\n\n      //       if (value === undefined && p.endsWith('?')) {\n      //         // Optional params without value assigned in route.params should be ignored\n      //         return '';\n      //       }\n\n      //       // Valid characters according to\n      //       // https://datatracker.ietf.org/doc/html/rfc3986#section-3.3 (see pchar definition)\n      //       return String(value).replace(/[^A-Za-z0-9\\-._~!$&'()*+,;=:@]/g, (char) =>\n      //         encodeURIComponent(char)\n      //       );\n      //     }\n\n      //     return encodeURIComponent(p);\n      //   })\n      //   .join('/');\n      // } else {\n    } else if (!route.name.startsWith('+')) {\n      path += encodeURIComponent(route.name);\n    }\n    // END FORK\n\n    if (!focusedParams) {\n      focusedParams = focusedRoute.params;\n    }\n\n    if (route.state) {\n      path += '/';\n    } else if (focusedParams) {\n      for (const param in focusedParams) {\n        if (focusedParams[param] === 'undefined') {\n          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n          delete focusedParams[param];\n        }\n      }\n\n      // START FORK\n      delete focusedParams['#'];\n      // END FORK\n\n      const query = queryString.stringify(focusedParams, { sort: false });\n      if (query) {\n        path += `?${query}`;\n      }\n    }\n\n    current = route.state;\n  }\n\n  // Remove multiple as well as trailing slashes\n  path = path.replace(/\\/+/g, '/');\n  path = path.length > 1 ? path.replace(/\\/$/, '') : path;\n\n  // Include the root path if specified\n  if (options?.path) {\n    path = joinPaths(options.path, path);\n  }\n\n  // START FORK\n  path = expo.appendBaseUrl(path);\n  if (allParams['#']) {\n    path += `#${allParams['#']}`;\n  }\n  // END FORK\n\n  // START FORK\n  return { path, params: allParams };\n  // END FORK\n}\n\n// const getParamName = (pattern: string) => pattern.replace(/^:/, '').replace(/\\?$/, '');\n\nconst joinPaths = (...paths: string[]): string =>\n  ([] as string[])\n    .concat(...paths.map((p) => p.split('/')))\n    .filter(Boolean)\n    .join('/');\n\nconst createConfigItem = (\n  config: PathConfig<object> | string,\n  parentPattern?: string\n): ConfigItem => {\n  if (typeof config === 'string') {\n    // If a string is specified as the value of the key(e.g. Foo: '/path'), use it as the pattern\n    const pattern = parentPattern ? joinPaths(parentPattern, config) : config;\n\n    return { pattern };\n  }\n\n  if (config.exact && config.path === undefined) {\n    throw new Error(\n      \"A 'path' needs to be specified when specifying 'exact: true'. If you don't want this screen in the URL, specify it as empty string, e.g. `path: ''`.\"\n    );\n  }\n\n  // If an object is specified as the value (e.g. Foo: { ... }),\n  // It can have `path` property and `screens` prop which has nested configs\n  const pattern =\n    config.exact !== true ? joinPaths(parentPattern || '', config.path || '') : config.path || '';\n\n  const screens = config.screens ? createNormalizedConfigs(config.screens, pattern) : undefined;\n\n  return {\n    // Normalize pattern to remove any leading, trailing slashes, duplicate slashes etc.\n    pattern: pattern?.split('/').filter(Boolean).join('/'),\n    stringify: config.stringify,\n    screens,\n  };\n};\n\nconst createNormalizedConfigs = (\n  options: PathConfigMap<object>,\n  pattern?: string\n): Record<string, ConfigItem> =>\n  Object.fromEntries(\n    Object.entries(options).map(([name, c]) => {\n      const result = createConfigItem(c, pattern);\n\n      return [name, result];\n    })\n  );\n\nexport function appendBaseUrl(\n  path: string,\n  baseUrl: string | undefined = process.env.EXPO_BASE_URL\n) {\n  if (process.env.NODE_ENV !== 'development') {\n    if (baseUrl) {\n      return `/${baseUrl.replace(/^\\/+/, '').replace(/\\/$/, '')}${path}`;\n    }\n  }\n  return path;\n}\n"]}