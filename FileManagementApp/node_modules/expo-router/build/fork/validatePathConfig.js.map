{"version": 3, "file": "validatePathConfig.js", "sourceRoot": "", "sources": ["../../src/fork/validatePathConfig.ts"], "names": [], "mappings": ";;AAEA,qCA0BC;AA5BD,MAAM,YAAY,GAAG,CAAC,KAAe,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAEpF,SAAwB,kBAAkB,CAAC,MAAW,EAAE,IAAI,GAAG,IAAI;IACjE,MAAM,SAAS,GAAG,CAAC,kBAAkB,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IAE5D,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;IAED,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAElF,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;QACvB,MAAM,IAAI,KAAK,CACb,mDAAmD,YAAY,CAC7D,WAAW,CACZ,qHAAqH,YAAY,CAChI,SAAS,CACV,wHAAwH,CAC1H,CAAC;IACJ,CAAC;IAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE;YACpD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,kBAAkB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACnC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;AACH,CAAC", "sourcesContent": ["const formatToList = (items: string[]) => items.map((key) => `- ${key}`).join('\\n');\n\nexport default function validatePathConfig(config: any, root = true) {\n  const validKeys = ['initialRouteName', 'screens', '_route'];\n\n  if (!root) {\n    validKeys.push('path', 'exact', 'stringify', 'parse');\n  }\n\n  const invalidKeys = Object.keys(config).filter((key) => !validKeys.includes(key));\n\n  if (invalidKeys.length) {\n    throw new Error(\n      `Found invalid properties in the configuration:\\n${formatToList(\n        invalidKeys\n      )}\\n\\nDid you forget to specify them under a 'screens' property?\\n\\nYou can only specify the following properties:\\n${formatToList(\n        validKeys\n      )}\\n\\nSee https://reactnavigation.org/docs/configuring-links for more details on how to specify a linking configuration.`\n    );\n  }\n\n  if (config.screens) {\n    Object.entries(config.screens).forEach(([_, value]) => {\n      if (typeof value !== 'string') {\n        validatePathConfig(value, false);\n      }\n    });\n  }\n}\n"]}