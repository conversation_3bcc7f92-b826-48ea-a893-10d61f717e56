{"version": 3, "file": "useBackButton.js", "sourceRoot": "", "sources": ["../../src/fork/useBackButton.ts"], "names": [], "mappings": ";;AAKA,sCAGC;AAHD,SAAgB,aAAa,CAAC,CAAgE;IAC5F,QAAQ;IACR,sCAAsC;AACxC,CAAC", "sourcesContent": ["/*\n * This file is unchanged, except for removing eslint comments\n */\nimport type { NavigationContainerRef, ParamListBase } from '@react-navigation/native';\n\nexport function useBackButton(_: React.RefObject<NavigationContainerRef<ParamListBase> | null>) {\n  // No-op\n  // BackHandler is not available on web\n}\n"]}