{"version": 3, "file": "StackClient.js", "sourceRoot": "", "sources": ["../../src/layouts/StackClient.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;AACb,qDAWkC;AAClC,iEAIwC;AACxC,kDAA2C;AAG3C,2DAAwD;AACxD,8CAA+D;AAC/D,kDAA+C;AAI/C,MAAM,oBAAoB,GAAG,IAAA,yCAA0B,GAAE,CAAC,SAAS,CAAC;AAEpE,MAAM,OAAO,GAAG,IAAA,qCAAiB,EAK/B,oBAAoB,CAAC,CAAC;AAExB,SAAS,aAAa,CACpB,MAAwB;IAExB,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,MAAM;QACtB,MAAM,CAAC,IAAI,KAAK,UAAU;QAC1B,MAAM,CAAC,IAAI,KAAK,KAAK;QACrB,MAAM,CAAC,IAAI,KAAK,YAAY;QAC5B,MAAM,CAAC,IAAI,KAAK,SAAS,CAC1B,CAAC;AACJ,CAAC;AAED;;;;;;;;GAQG;AACI,MAAM,mBAAmB,GAAmE,CACjG,QAAQ,EACR,EAAE;IACF,OAAO;QACL,iBAAiB,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;YAC5C,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,GAAG,EAAE,CAAC;gBACjD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC3B,OAAO,QAAQ,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YAC5D,CAAC;YAED,oFAAoF;YACpF,MAAM,qBAAqB,GACzB,MAAM,CAAC,OAAO,IAAI,UAAU,IAAI,MAAM,CAAC,OAAO;gBAC5C,CAAC,CAAE,MAAM,CAAC,OAAO,CAAC,QAA4B;gBAC9C,CAAC,CAAC,SAAS,CAAC;YAEhB,0CAA0C;YAC1C,SAAS,aAAa;gBACpB,+EAA+E;gBAC/E,IACE,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC;oBACtB,CAAC,MAAM,CAAC,OAAO;oBACf,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC;oBAC3B,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,EACvC,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;gBAEvC,OAAO;gBACL,6FAA6F;gBAC7F,qBAAqB,CAAC,qBAAqB,EAAE,UAAU,CAAC;oBACxD,qFAAqF;oBACrF,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,CACnC,CAAC;YACJ,CAAC;YAED,MAAM,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;YAEnC,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,MAAM,CAAC;gBACZ,KAAK,UAAU,CAAC,CAAC,CAAC;oBAChB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;wBACpD,OAAO,IAAI,CAAC;oBACd,CAAC;oBAED,aAAa;oBACb,MAAM,KAAK,GAAG,aAAa,EAAE,CAAC;oBAC9B,6DAA6D;oBAC7D,WAAW;oBACX,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;oBAEtD,IAAI,KAAgC,CAAC;oBAErC,IAAI,EAAE,KAAK,SAAS,EAAE,CAAC;wBACrB,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAC3B,CAAC,KAAK,EAAE,EAAE,CACR,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CACjF,CAAC;oBACJ,CAAC;yBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;wBACtC,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;wBAE/C,4DAA4D;wBAC5D,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC;4BAC9C,KAAK,GAAG,YAAY,CAAC;wBACvB,CAAC;6BAAM,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;4BAC9B,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;wBAC/E,CAAC;oBACH,CAAC;oBAED,IAAI,CAAC,KAAK,EAAE,CAAC;wBACX,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC,IAAI,CAChC,CAAC,KAAK,EAAE,EAAE,CACR,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CACjF,CAAC;oBACJ,CAAC;oBAED,IAAI,MAAM,CAAC;oBAEX,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC;wBAChE,MAAM;4BACJ,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS;gCACnC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS;gCAC/C,CAAC,CAAC;oCACE,GAAG,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;oCACtC,GAAG,KAAK,CAAC,MAAM;oCACf,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM;iCACzB;gCACH,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;oBACrB,CAAC;yBAAM,CAAC;wBACN,MAAM;4BACJ,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS;gCAC/C,CAAC,CAAC;oCACE,GAAG,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;oCACtC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM;iCACzB;gCACH,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;oBAC9B,CAAC;oBAED,IAAI,MAAuB,CAAC;oBAE5B,IAAI,KAAK,EAAE,CAAC;wBACV,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;4BACrD,MAAM,GAAG,EAAE,CAAC;4BAEZ,wCAAwC;4BACxC,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gCAC7B,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE,CAAC;oCACxB,MAAM,CAAC,IAAI,CAAC;wCACV,GAAG,KAAK;wCACR,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI;wCAC1E,MAAM;qCACP,CAAC,CAAC;oCACH,MAAM;gCACR,CAAC;gCAED,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;4BACjB,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,aAAa;4BACb,0EAA0E;4BAC1E,0EAA0E;4BAC1E,IAAI,EAAE,KAAK,SAAS,EAAE,CAAC;gCACrB,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC;4BAC3D,CAAC;iCAAM,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCACjE,oGAAoG;gCACpG,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gCACxD,IACE,IAAA,0BAAa,EAAC,SAAS,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC,MAAM,EAAE,CAAC;oCAC3D,IAAA,0BAAa,EAAC,KAAK,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,EACrC,CAAC;oCACD,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gCACrC,CAAC;qCAAM,CAAC;oCACN,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;gCAC7B,CAAC;4BACH,CAAC;iCAAM,CAAC;gCACN,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;4BAC7B,CAAC;4BAED,sGAAsG;4BACtG,gDAAgD;4BAChD,MAAM,GAAG,GACP,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,MAAM;gCACnC,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,IAAA,mBAAM,GAAE,EAAE;gCACtC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;4BAEhB,MAAM,CAAC,IAAI,CAAC;gCACV,GAAG,KAAK;gCACR,GAAG;gCACH,IAAI,EACF,MAAM,CAAC,IAAI,KAAK,UAAU,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;oCAC7D,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;oCACrB,CAAC,CAAC,KAAK,CAAC,IAAI;gCAChB,MAAM;6BACP,CAAC,CAAC;4BAEH,4DAA4D;4BAC5D,gBAAgB;4BAChB,cAAc;4BACd,UAAU;4BACV,sEAAsE;4BACtE,8BAA8B;4BAC9B,sBAAsB;4BACtB,YAAY;4BACZ,MAAM;4BACN,WAAW;wBACb,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,MAAM,GAAG;4BACP,GAAG,KAAK,CAAC,MAAM;4BACf;gCACE,GAAG,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,IAAA,mBAAM,GAAE,EAAE;gCACzC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI;gCACzB,IAAI,EAAE,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;gCAClE,MAAM;6BACP;yBACF,CAAC;oBACJ,CAAC;oBAED,aAAa;oBACb,yBAAyB;oBACzB,MAAM,MAAM,GAAG;wBACb,GAAG,KAAK;wBACR,KAAK,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC;wBACxB,eAAe,EAAE,KAAK,CAAC,eAAe,CAAC,MAAM,CAC3C,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,CACvD;wBACD,MAAM;qBACP,CAAC;oBAEF,IAAI,qBAAqB,EAAE,CAAC;wBAC1B,OAAO,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;oBACvC,CAAC;oBAED,OAAO,MAAM,CAAC;oBACd,WAAW;oBACX,cAAc;oBACd,8BAA8B;oBAC9B,mDAAmD;oBACnD,6DAA6D;oBAC7D,OAAO;oBACP,YAAY;oBACZ,KAAK;oBACL,WAAW;gBACb,CAAC;gBACD,OAAO,CAAC,CAAC,CAAC;oBACR,OAAO,QAAQ,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAvNW,QAAA,mBAAmB,uBAuN9B;AAEF,SAAS,qBAAqB,CAC5B,WAAwC,EACxC,IAAY;IAEZ,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE,CAAC;QACtC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;IAC9D,CAAC;SAAM,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;QAChC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,IAAA,0BAAa,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;GAGG;AACH,SAAS,cAAc,CAKrB,KAAQ,EAAE,KAAa;IACvB,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAClB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5D,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC3C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAE1B,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAE/C,IAAI,CAAC,EAAE,EAAE,CAAC;QACR,OAAO,KAAK,CAAC;IACf,CAAC;IAED,iEAAiE;IACjE,IAAI,MAAM,GAAG,KAAK,CAAC,MAA2D,CAAC;IAC/E,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QACtC,8CAA8C;QAC9C,IAAI,KAAK,KAAK,YAAY,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,qDAAqD;QACrD,OAAO,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;IAEH,OAAO;QACL,GAAG,KAAK;QACR,KAAK,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC;QACxB,MAAM;KACP,CAAC;AACJ,CAAC;AAED,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACzB,CAAC,KAAqC,EAAE,EAAE;IACxC,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC,2BAAmB,CAAC,EAAG,CAAC;AACtE,CAAC,EACD;IACE,MAAM,EAAE,OAAO,CAAC,MAEP;IACT,SAAS,EAAT,qBAAS;CACV,CACF,CAAC;AAEF,kBAAe,KAAK,CAAC;AAEd,MAAM,WAAW,GAAyB,CAAC,OAAO,EAAE,EAAE;IAC3D,MAAM,MAAM,GAAG,IAAA,oBAAa,EAAC,OAAO,CAAC,CAAC;IACtC,OAAO;QACL,GAAG,MAAM;QACT,GAAG,IAAA,2BAAmB,EAAC,MAAM,CAAC;KAC/B,CAAC;AACJ,CAAC,CAAC;AANW,QAAA,WAAW,eAMtB", "sourcesContent": ["'use client';\nimport {\n  CommonNavigationAction,\n  NavigationAction,\n  ParamListBase,\n  PartialRoute,\n  PartialState,\n  Route,\n  RouterConfigOptions,\n  StackRouter as RNStackRouter,\n  StackActionType,\n  StackNavigationState,\n} from '@react-navigation/native';\nimport {\n  NativeStackNavigationEventMap,\n  NativeStackNavigationOptions,\n  createNativeStackNavigator,\n} from '@react-navigation/native-stack';\nimport { nanoid } from 'nanoid/non-secure';\nimport { ComponentProps } from 'react';\n\nimport { withLayoutContext } from './withLayoutContext';\nimport { SingularOptions, getSingularId } from '../useScreens';\nimport { Protected } from '../views/Protected';\n\ntype GetId = NonNullable<RouterConfigOptions['routeGetIdList'][string]>;\n\nconst NativeStackNavigator = createNativeStackNavigator().Navigator;\n\nconst RNStack = withLayoutContext<\n  NativeStackNavigationOptions,\n  typeof NativeStackNavigator,\n  StackNavigationState<ParamListBase>,\n  NativeStackNavigationEventMap\n>(NativeStackNavigator);\n\nfunction isStackAction(\n  action: NavigationAction\n): action is StackActionType | Extract<CommonNavigationAction, { type: 'NAVIGATE' }> {\n  return (\n    action.type === 'PUSH' ||\n    action.type === 'NAVIGATE' ||\n    action.type === 'POP' ||\n    action.type === 'POP_TO_TOP' ||\n    action.type === 'REPLACE'\n  );\n}\n\n/**\n * React Navigation matches a screen by its name or a 'getID' function that uniquely identifies a screen.\n * When a screen has been uniquely identified, the Stack can only have one instance of that screen.\n *\n * Expo Router allows for a screen to be matched by name and path params, a 'getID' function or a singular id.\n *\n * Instead of reimplementing the entire StackRouter, we can override the getStateForAction method to handle the singular screen logic.\n *\n */\nexport const stackRouterOverride: NonNullable<ComponentProps<typeof RNStack>['UNSTABLE_router']> = (\n  original\n) => {\n  return {\n    getStateForAction: (state, action, options) => {\n      if (action.target && action.target !== state.key) {\n        return null;\n      }\n\n      if (!isStackAction(action)) {\n        return original.getStateForAction(state, action, options);\n      }\n\n      // The dynamic getId added to an action, `router.push('screen', { singular: true })`\n      const actionSingularOptions =\n        action.payload && 'singular' in action.payload\n          ? (action.payload.singular as SingularOptions)\n          : undefined;\n\n      // Handle if 'getID' or 'singular' is set.\n      function getIdFunction(): GetId | undefined {\n        // Actions can be fired by the user, so we do need to validate their structure.\n        if (\n          !('payload' in action) ||\n          !action.payload ||\n          !('name' in action.payload) ||\n          typeof action.payload.name !== 'string'\n        ) {\n          return;\n        }\n\n        const actionName = action.payload.name;\n\n        return (\n          // The dynamic singular added to an action, `router.push('screen', { singular: () => 'id' })`\n          getActionSingularIdFn(actionSingularOptions, actionName) ||\n          // The static getId added as a prop to `<Screen singular />` or `<Screen getId={} />`\n          options.routeGetIdList[actionName]\n        );\n      }\n\n      const { routeParamList } = options;\n\n      switch (action.type) {\n        case 'PUSH':\n        case 'NAVIGATE': {\n          if (!state.routeNames.includes(action.payload.name)) {\n            return null;\n          }\n\n          // START FORK\n          const getId = getIdFunction();\n          // const getId = options.routeGetIdList[action.payload.name];\n          // END FORK\n          const id = getId?.({ params: action.payload.params });\n\n          let route: Route<string> | undefined;\n\n          if (id !== undefined) {\n            route = state.routes.findLast(\n              (route) =>\n                route.name === action.payload.name && id === getId?.({ params: route.params })\n            );\n          } else if (action.type === 'NAVIGATE') {\n            const currentRoute = state.routes[state.index];\n\n            // If the route matches the current one, then navigate to it\n            if (action.payload.name === currentRoute.name) {\n              route = currentRoute;\n            } else if (action.payload.pop) {\n              route = state.routes.findLast((route) => route.name === action.payload.name);\n            }\n          }\n\n          if (!route) {\n            route = state.preloadedRoutes.find(\n              (route) =>\n                route.name === action.payload.name && id === getId?.({ params: route.params })\n            );\n          }\n\n          let params;\n\n          if (action.type === 'NAVIGATE' && action.payload.merge && route) {\n            params =\n              action.payload.params !== undefined ||\n              routeParamList[action.payload.name] !== undefined\n                ? {\n                    ...routeParamList[action.payload.name],\n                    ...route.params,\n                    ...action.payload.params,\n                  }\n                : route.params;\n          } else {\n            params =\n              routeParamList[action.payload.name] !== undefined\n                ? {\n                    ...routeParamList[action.payload.name],\n                    ...action.payload.params,\n                  }\n                : action.payload.params;\n          }\n\n          let routes: Route<string>[];\n\n          if (route) {\n            if (action.type === 'NAVIGATE' && action.payload.pop) {\n              routes = [];\n\n              // Get all routes until the matching one\n              for (const r of state.routes) {\n                if (r.key === route.key) {\n                  routes.push({\n                    ...route,\n                    path: action.payload.path !== undefined ? action.payload.path : route.path,\n                    params,\n                  });\n                  break;\n                }\n\n                routes.push(r);\n              }\n            } else {\n              // START FORK\n              // If there is an id, then filter out the existing route with the same id.\n              // THIS ACTION IS DANGEROUS. This can cause React Native Screens to freeze\n              if (id !== undefined) {\n                routes = state.routes.filter((r) => r.key !== route.key);\n              } else if (action.type === 'NAVIGATE' && state.routes.length > 0) {\n                // The navigation action should only replace the last route if it has the same name and path params.\n                const lastRoute = state.routes[state.routes.length - 1];\n                if (\n                  getSingularId(lastRoute.name, { params: lastRoute.params }) ===\n                  getSingularId(route.name, { params })\n                ) {\n                  routes = state.routes.slice(0, -1);\n                } else {\n                  routes = [...state.routes];\n                }\n              } else {\n                routes = [...state.routes];\n              }\n\n              // If the routes length is the same as the state routes length, then we are navigating to a new route.\n              // Otherwise we are replacing an existing route.\n              const key =\n                routes.length === state.routes.length\n                  ? `${action.payload.name}-${nanoid()}`\n                  : route.key;\n\n              routes.push({\n                ...route,\n                key,\n                path:\n                  action.type === 'NAVIGATE' && action.payload.path !== undefined\n                    ? action.payload.path\n                    : route.path,\n                params,\n              });\n\n              // routes = state.routes.filter((r) => r.key !== route.key);\n              // routes.push({\n              //   ...route,\n              //   path:\n              //     action.type === 'NAVIGATE' && action.payload.path !== undefined\n              //       ? action.payload.path\n              //       : route.path,\n              //   params,\n              // });\n              // END FORK\n            }\n          } else {\n            routes = [\n              ...state.routes,\n              {\n                key: `${action.payload.name}-${nanoid()}`,\n                name: action.payload.name,\n                path: action.type === 'NAVIGATE' ? action.payload.path : undefined,\n                params,\n              },\n            ];\n          }\n\n          // START FORK\n          // return filterSingular(\n          const result = {\n            ...state,\n            index: routes.length - 1,\n            preloadedRoutes: state.preloadedRoutes.filter(\n              (route) => routes[routes.length - 1].key !== route.key\n            ),\n            routes,\n          };\n\n          if (actionSingularOptions) {\n            return filterSingular(result, getId);\n          }\n\n          return result;\n          // return {\n          //   ...state,\n          //   index: routes.length - 1,\n          //   preloadedRoutes: state.preloadedRoutes.filter(\n          //     (route) => routes[routes.length - 1].key !== route.key\n          //   ),\n          //   routes,\n          // };\n          // END FORK\n        }\n        default: {\n          return original.getStateForAction(state, action, options);\n        }\n      }\n    },\n  };\n};\n\nfunction getActionSingularIdFn(\n  actionGetId: SingularOptions | undefined,\n  name: string\n): GetId | undefined {\n  if (typeof actionGetId === 'function') {\n    return (options) => actionGetId(name, options.params ?? {});\n  } else if (actionGetId === true) {\n    return (options) => getSingularId(name, options);\n  }\n\n  return undefined;\n}\n\n/**\n * If there is a dynamic singular on an action, then we need to filter the state to only have singular screens.\n * As multiples may have been added before we did the singular navigation.\n */\nfunction filterSingular<\n  T extends\n    | StackNavigationState<ParamListBase>\n    | PartialState<StackNavigationState<ParamListBase>>\n    | null,\n>(state: T, getId?: GetId): T {\n  if (!state) {\n    return state;\n  }\n\n  if (!state.routes) {\n    return state;\n  }\n\n  const currentIndex = state.index || state.routes.length - 1;\n  const current = state.routes[currentIndex];\n  const name = current.name;\n\n  const id = getId?.({ params: current.params });\n\n  if (!id) {\n    return state;\n  }\n\n  // TypeScript needs a type assertion here for the filter to work.\n  let routes = state.routes as PartialRoute<Route<string, object | undefined>>[];\n  routes = routes.filter((route, index) => {\n    // If the route is the current route, keep it.\n    if (index === currentIndex) {\n      return true;\n    }\n\n    // Remove all other routes with the same name and id.\n    return name !== route.name || id !== getId?.({ params: route.params });\n  });\n\n  return {\n    ...state,\n    index: routes.length - 1,\n    routes,\n  };\n}\n\nconst Stack = Object.assign(\n  (props: ComponentProps<typeof RNStack>) => {\n    return <RNStack {...props} UNSTABLE_router={stackRouterOverride} />;\n  },\n  {\n    Screen: RNStack.Screen as (\n      props: ComponentProps<typeof RNStack.Screen> & { singular?: boolean }\n    ) => null,\n    Protected,\n  }\n);\n\nexport default Stack;\n\nexport const StackRouter: typeof RNStackRouter = (options) => {\n  const router = RNStackRouter(options);\n  return {\n    ...router,\n    ...stackRouterOverride(router),\n  };\n};\n"]}