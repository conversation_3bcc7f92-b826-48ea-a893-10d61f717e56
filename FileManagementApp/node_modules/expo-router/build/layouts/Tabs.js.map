{"version": 3, "file": "Tabs.js", "sourceRoot": "", "sources": ["../../src/layouts/Tabs.tsx"], "names": [], "mappings": ";;;;;;AAAA,8DAAgC;AAKvB,eALF,oBAAI,CAKE;AAJb,4CAAyC;AAEzC,oBAAI,CAAC,MAAM,GAAG,eAAM,CAAC;AAIrB,kBAAe,oBAAI,CAAC", "sourcesContent": ["import Tabs from './TabsClient';\nimport { Screen } from '../views/Screen';\n\nTabs.Screen = Screen;\n\nexport { Tabs };\n\nexport default Tabs;\n"]}