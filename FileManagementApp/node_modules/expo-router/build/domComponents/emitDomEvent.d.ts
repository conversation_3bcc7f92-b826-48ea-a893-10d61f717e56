import { LinkToOptions } from '../global-state/routing';
export declare function emitDomSetParams(params?: Record<string, undefined | string | number | (string | number)[]>): boolean;
export declare function emitDomDismiss(count?: number): boolean;
export declare function emitDomGoBack(): boolean;
export declare function emitDomDismissAll(): boolean;
export declare function emitDomLinkEvent(href: string, options: LinkToOptions): boolean;
//# sourceMappingURL=emitDomEvent.d.ts.map