{"version": 3, "file": "events.js", "sourceRoot": "", "sources": ["../../src/domComponents/events.ts"], "names": [], "mappings": ";;;AAAa,QAAA,gBAAgB,GAAG,eAAe,CAAC;AACnC,QAAA,uBAAuB,GAAG,qBAAqB,CAAC;AAChD,QAAA,mBAAmB,GAAG,kBAAkB,CAAC;AACzC,QAAA,gBAAgB,GAAG,iBAAiB,CAAC;AACrC,QAAA,sBAAsB,GAAG,oBAAoB,CAAC", "sourcesContent": ["export const ROUTER_LINK_TYPE = '$$router_link';\nexport const ROUTER_DISMISS_ALL_TYPE = '$$router_dismissAll';\nexport const ROUTER_DISMISS_TYPE = '$$router_dismiss';\nexport const ROUTER_BACK_TYPE = '$$router_goBack';\nexport const ROUTER_SET_PARAMS_TYPE = '$$router_setParams';\n"]}