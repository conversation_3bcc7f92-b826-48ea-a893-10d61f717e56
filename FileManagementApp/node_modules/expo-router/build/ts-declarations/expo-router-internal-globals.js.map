{"version": 3, "file": "expo-router-internal-globals.js", "sourceRoot": "", "sources": ["../../src/ts-declarations/expo-router-internal-globals.ts"], "names": [], "mappings": ";AAAA,2BAA2B", "sourcesContent": ["/* eslint-disable no-var */\n\ndeclare global {\n  var __EXPO_RSC_RELOAD_LISTENERS__: undefined | (() => void)[];\n  var __EXPO_REFETCH_RSC__: undefined | (() => void);\n  var __EXPO_REFETCH_ROUTE__: undefined | (() => void);\n  var __EXPO_REFETCH_ROUTE_NO_CACHE__: undefined | (() => void);\n  var __expo_platform_header: undefined | string;\n}\n"]}