{"version": 3, "file": "expect.js", "sourceRoot": "", "sources": ["../../src/testing-library/expect.ts"], "names": [], "mappings": ";;;;;AAAA,qEAA6C;AAE7C,kBAAQ,CAAC,aAAa,GAAG,EAAE,CAAC;AAE5B,MAAM,CAAC,MAAM,CAAC;IACZ,cAAc,CAAC,MAAM,EAAE,QAAQ;QAC7B,OAAO,kBAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC1D,CAAC;IACD,wBAAwB,CAAC,MAAM,EAAE,QAAQ;QACvC,OAAO,kBAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,EAAE,EAAE,QAAQ,CAAC,CAAC;IACpE,CAAC;IACD,cAAc,CAAC,MAAM,EAAE,QAAQ;QAC7B,OAAO,kBAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC1D,CAAC;IACD,kBAAkB,CAAC,MAAM,EAAE,QAAQ;QACjC,OAAO,kBAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC9D,CAAC;IACD,iBAAiB,CAAC,MAAM,EAAE,QAAQ;QAChC,OAAO,kBAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC7D,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["import matchers from 'expect/build/matchers';\n\nmatchers.customTesters = [];\n\nexpect.extend({\n  toHavePathname(screen, expected) {\n    return matchers.toEqual(screen.getPathname(), expected);\n  },\n  toHavePathnameWithParams(screen, expected) {\n    return matchers.toEqual(screen.getPathnameWithParams(), expected);\n  },\n  toHaveSegments(screen, expected) {\n    return matchers.toEqual(screen.getSegments(), expected);\n  },\n  toHaveSearchParams(screen, expected) {\n    return matchers.toEqual(screen.getSearchParams(), expected);\n  },\n  toHaveRouterState(screen, expected) {\n    return matchers.toEqual(screen.getRouterState(), expected);\n  },\n});\n"]}