{"version": 3, "file": "mocks.js", "sourceRoot": "", "sources": ["../../src/testing-library/mocks.ts"], "names": [], "mappings": ";;AAAA;;GAEG;AACH,IAAI,CAAC;IACH,OAAO,CAAC,4CAA4C,CAAC,CAAC;AACxD,CAAC;AAAC,MAAM,CAAC,CAAA,CAAC;AAEV,IAAI,CAAC;IACH,OAAO,CAAC,wCAAwC,CAAC,CAAC;AACpD,CAAC;AAAC,MAAM,CAAC,CAAA,CAAC;AAEV,IAAI,CAAC;IACH,OAAO,CAAC,yBAAyB,CAAC,CAAC;IACnC,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACxC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;YAC3D,UAAU,CAAC,OAAO,CAAC,IAAI,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC,yCAAyC;YAC7E,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAAC,MAAM,CAAC,CAAA,CAAC;AAEV,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE;IAC7B,MAAM,MAAM,GAAkC;QAC5C,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;QACrC,SAAS,CAAC,IAAY;YACpB,OAAO,eAAe,GAAG,IAAI,CAAC;QAChC,CAAC;QACD,aAAa;YACX,OAAO,YAAY,CAAC;QACtB,CAAC;QACD,gBAAgB;YACd,OAAO,EAAE,MAAM,KAAI,CAAC,EAAS,CAAC;QAChC,CAAC;KACF,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC,CAAC", "sourcesContent": ["/*\n * Optionally enable @testing-library/jest-native/extend-expect. We use this internally for the `toBeOnTheScreen` matcher()\n */\ntry {\n  require('@testing-library/jest-native/extend-expect');\n} catch {}\n\ntry {\n  require('react-native-gesture-handler/jestSetup');\n} catch {}\n\ntry {\n  require('react-native-reanimated');\n  jest.mock('react-native-reanimated', () => {\n    try {\n      const Reanimated = require('react-native-reanimated/mock');\n      Reanimated.default.call = () => {}; // Override `call` with a no-op if needed\n      return Reanimated;\n    } catch {\n      return {};\n    }\n  });\n} catch {}\n\njest.mock('expo-linking', () => {\n  const module: typeof import('expo-linking') = {\n    ...jest.requireActual('expo-linking'),\n    createURL(path: string) {\n      return 'yourscheme://' + path;\n    },\n    resolveScheme() {\n      return 'yourscheme';\n    },\n    addEventListener() {\n      return { remove() {} } as any;\n    },\n  };\n\n  return module;\n});\n"]}