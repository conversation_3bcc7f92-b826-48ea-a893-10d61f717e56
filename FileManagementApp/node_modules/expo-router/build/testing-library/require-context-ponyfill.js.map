{"version": 3, "file": "require-context-ponyfill.js", "sourceRoot": "", "sources": ["../../src/testing-library/require-context-ponyfill.ts"], "names": [], "mappings": ";;;;;AAYA,iCA6CC;AAzDD,yBAAyB;AACzB,sDAAyB;AACzB,yBAAyB;AACzB,0DAA6B;AAS7B,SAAwB,cAAc,CACpC,IAAI,GAAG,GAAG,EACV,kBAAkB,GAAG,IAAI,EACzB,iBAAiB,GAAG,YAAY,EAChC,QAAiC,EAAE;IAEnC,SAAS,aAAa,CAAC,SAAiB;QACtC,iBAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,IAAY,EAAE,EAAE;YACjD,MAAM,QAAQ,GAAG,mBAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC/C,MAAM,YAAY,GAAG,KAAK,mBAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,CAAC,mBAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAEpF,IAAI,iBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxC,IAAI,kBAAkB;oBAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAEhD,OAAO;YACT,CAAC;YAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC;gBAAE,OAAO;YAElD,KAAK,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,iBAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,aAAa,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IAED,MAAM,OAAO,GAA2B,MAAM,CAAC,MAAM,CACnD,SAAS,MAAM,CAAC,IAAY;QAC1B,OAAO,OAAO,CAAC,mBAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IACxC,CAAC,EACD;QACE,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;QAC9B,OAAO,EAAE,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG;QAC7B,EAAE,EAAE,GAAG;QACP,KAAK,CAAC,IAAY;YAChB,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QACrB,CAAC;QACD,QAAQ,CAAC,IAAY;YACnB,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;KACF,CACF,CAAC;IAEF,OAAO,OAAO,CAAC;AACjB,CAAC", "sourcesContent": ["// @ts-ignore: types node\nimport fs from 'node:fs';\n// @ts-ignore: types node\nimport path from 'node:path';\n\nimport type { RequireContext } from '../types';\n\nexport interface RequireContextPonyFill extends RequireContext {\n  __add(file: string): void;\n  __delete(file: string): void;\n}\n\nexport default function requireContext(\n  base = '.',\n  scanSubDirectories = true,\n  regularExpression = /\\.[tj]sx?$/,\n  files: Record<string, unknown> = {}\n) {\n  function readDirectory(directory: string) {\n    fs.readdirSync(directory).forEach((file: string) => {\n      const fullPath = path.resolve(directory, file);\n      const relativePath = `./${path.relative(base, fullPath).split(path.sep).join('/')}`;\n\n      if (fs.statSync(fullPath).isDirectory()) {\n        if (scanSubDirectories) readDirectory(fullPath);\n\n        return;\n      }\n\n      if (!regularExpression.test(relativePath)) return;\n\n      files[relativePath] = true;\n    });\n  }\n\n  if (fs.existsSync(base)) {\n    readDirectory(base);\n  }\n\n  const context: RequireContextPonyFill = Object.assign(\n    function Module(file: string) {\n      return require(path.join(base, file));\n    },\n    {\n      keys: () => Object.keys(files),\n      resolve: (key: string) => key,\n      id: '0',\n      __add(file: string) {\n        files[file] = true;\n      },\n      __delete(file: string) {\n        delete files[file];\n      },\n    }\n  );\n\n  return context;\n}\n"]}