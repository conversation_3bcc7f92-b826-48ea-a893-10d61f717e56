{"version": 3, "file": "useLinkToPathProps.js", "sourceRoot": "", "sources": ["../../src/link/useLinkToPathProps.tsx"], "names": [], "mappings": ";;AAqCA,qCAsBC;AAED,wDAaC;AAzED,+CAA+D;AAE/D,gEAAiE;AACjE,2EAA+D;AAC/D,qDAAgE;AAChE,0CAAyD;AACzD,sCAAoD;AAEpD,SAAS,yBAAyB,CAChC,CAAwD;IAExD,IAAI,CAAC,EAAE,gBAAgB,EAAE,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IAED;IACE,yBAAyB;IACzB,QAAQ,IAAI,CAAC;QACb,mCAAmC;QACnC,CAAC,CAAC,CAAC,OAAO;QACV,CAAC,CAAC,CAAC,MAAM;QACT,CAAC,CAAC,CAAC,OAAO;QACV,CAAC,CAAC,CAAC,QAAQ;QACX,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,0BAA0B;QAClE,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,0CAA0C;MACzG,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAMD,SAAwB,kBAAkB,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,EAA6B;IACxF,MAAM,OAAO,GAAG,CAAC,KAA6D,EAAE,EAAE;QAChF,IAAI,sBAAsB,CAAC,KAAK,CAAC,EAAE,CAAC;YAClC,IAAI,IAAA,+BAAgB,EAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;gBACpC,OAAO;YACT,CAAC;YACD,IAAA,gBAAM,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACxB,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,YAAY,GAAG,IAAA,qCAA0B,EAAC,IAAI,CAAC,IAAI,GAAG,CAAC;IAE3D,kCAAkC;IAClC,IAAI,CAAC,IAAA,0BAAoB,EAAC,YAAY,CAAC,EAAE,CAAC;QACxC,YAAY,GAAG,IAAA,sCAAa,EAAC,YAAY,CAAC,CAAC;IAC7C,CAAC;IAED,OAAO;QACL,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,MAAe;QACrB,OAAO;KACR,CAAC;AACJ,CAAC;AAED,SAAgB,sBAAsB,CACpC,KAA6D;IAE7D,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QAC1B,OAAO,CAAC,KAAK,EAAE,gBAAgB,CAAC;IAClC,CAAC;IAED,IAAI,KAAK,IAAI,yBAAyB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9C,KAAK,CAAC,cAAc,EAAE,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC", "sourcesContent": ["import { MouseEvent } from 'react';\nimport { GestureResponderEvent, Platform } from 'react-native';\n\nimport { emitDomLinkEvent } from '../domComponents/emitDomEvent';\nimport { appendBaseUrl } from '../fork/getPathFromState-forks';\nimport { linkTo, LinkToOptions } from '../global-state/routing';\nimport { stripGroupSegmentsFromPath } from '../matchers';\nimport { shouldLinkExternally } from '../utils/url';\n\nfunction eventShouldPreventDefault(\n  e: MouseEvent<HTMLAnchorElement> | GestureResponderEvent\n): boolean {\n  if (e?.defaultPrevented) {\n    return false;\n  }\n\n  if (\n    // Only check MouseEvents\n    'button' in e &&\n    // ignore clicks with modifier keys\n    !e.metaKey &&\n    !e.altKey &&\n    !e.ctrlKey &&\n    !e.shiftKey &&\n    (e.button == null || e.button === 0) && // Only accept left clicks\n    [undefined, null, '', 'self'].includes(e.currentTarget.target) // let browser handle \"target=_blank\" etc.\n  ) {\n    return true;\n  }\n\n  return false;\n}\n\ntype UseLinkToPathPropsOptions = LinkToOptions & {\n  href: string;\n};\n\nexport default function useLinkToPathProps({ href, ...options }: UseLinkToPathPropsOptions) {\n  const onPress = (event?: MouseEvent<HTMLAnchorElement> | GestureResponderEvent) => {\n    if (shouldHandleMouseEvent(event)) {\n      if (emitDomLinkEvent(href, options)) {\n        return;\n      }\n      linkTo(href, options);\n    }\n  };\n\n  let strippedHref = stripGroupSegmentsFromPath(href) || '/';\n\n  // Append base url only if needed.\n  if (!shouldLinkExternally(strippedHref)) {\n    strippedHref = appendBaseUrl(strippedHref);\n  }\n\n  return {\n    href: strippedHref,\n    role: 'link' as const,\n    onPress,\n  };\n}\n\nexport function shouldHandleMouseEvent(\n  event?: MouseEvent<HTMLAnchorElement> | GestureResponderEvent\n) {\n  if (Platform.OS !== 'web') {\n    return !event?.defaultPrevented;\n  }\n\n  if (event && eventShouldPreventDefault(event)) {\n    event.preventDefault();\n    return true;\n  }\n\n  return false;\n}\n"]}