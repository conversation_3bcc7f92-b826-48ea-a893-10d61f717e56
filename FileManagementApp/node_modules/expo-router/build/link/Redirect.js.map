{"version": 3, "file": "Redirect.js", "sourceRoot": "", "sources": ["../../src/link/Redirect.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;AA+Db,4BAUC;AAxED,oCAAqC;AAErC,sDAAmD;AAqCnD;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,SAAgB,QAAQ,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,UAAU,EAAiB;IAC/E,MAAM,MAAM,GAAG,IAAA,iBAAS,GAAE,CAAC;IAC3B,IAAA,+BAAc,EAAC,GAAG,EAAE;QAClB,IAAI,CAAC;YACH,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,mBAAmB,EAAE,UAAU,EAAE,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAO,IAAI,CAAC;AACd,CAAC;AAED,kBAAe,QAAQ,CAAC", "sourcesContent": ["'use client';\nimport { useRouter } from '../hooks';\nimport type { Href } from '../types';\nimport { useFocusEffect } from '../useFocusEffect';\n\nexport type RedirectProps = {\n  /**\n   * The path of the route to navigate to. It can either be:\n   * - **string**: A full path like `/profile/settings` or a relative path like `../settings`.\n   * - **object**: An object with a `pathname` and optional `params`. The `pathname` can be\n   * a full path like `/profile/settings` or a relative path like `../settings`. The\n   * params can be an object of key-value pairs.\n   *\n   * @example\n   * ```tsx Dynamic\n   * import { Redirect } from 'expo-router';\n   *\n   * export default function RedirectToAbout() {\n   *  return (\n   *    <Redirect href=\"/about\" />\n   *  );\n   *}\n   * ```\n   */\n  href: Href;\n\n  /**\n   * Relative URL references are either relative to the directory or the document.\n   * By default, relative paths are relative to the document.\n   *\n   * @see [Resolving relative references in Mozilla's documentation](https://developer.mozilla.org/en-US/docs/Web/API/URL_API/Resolving_relative_references).\n   */\n  relativeToDirectory?: boolean;\n\n  /**\n   * Replaces the initial screen with the current route.\n   */\n  withAnchor?: boolean;\n};\n\n/**\n * Redirects to the `href` as soon as the component is mounted.\n *\n * @example\n * ```tsx\n * import { View, Text } from 'react-native';\n * import { Redirect } from 'expo-router';\n *\n * export default function Page() {\n *  const { user } = useAuth();\n *\n *  if (!user) {\n *    return <Redirect href=\"/login\" />;\n *  }\n *\n *  return (\n *    <View>\n *      <Text>Welcome Back!</Text>\n *    </View>\n *  );\n * }\n * ```\n */\nexport function Redirect({ href, relativeToDirectory, withAnchor }: RedirectProps) {\n  const router = useRouter();\n  useFocusEffect(() => {\n    try {\n      router.replace(href, { relativeToDirectory, withAnchor });\n    } catch (error) {\n      console.error(error);\n    }\n  });\n  return null;\n}\n\nexport default Redirect;\n"]}