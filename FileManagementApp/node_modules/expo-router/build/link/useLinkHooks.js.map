{"version": 3, "file": "useLinkHooks.js", "sourceRoot": "", "sources": ["../../src/link/useLinkHooks.ts"], "names": [], "mappings": ";;;AAqOA,kDAoBC;AAzPD,wFAAwF;AACxF,mCAAmC;AACnC,iCAA4C;AAC5C,+CAA0E;AAiO1E,qDAAqD;AACrD,SAAgB,mBAAmB,CAAC,KAAyD;IAC3F,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC,KAAK,CAAC;IACrB,CAAC;IAED,sDAAsD;IACtD,OAAO,IAAA,eAAO,EAAC,GAAG,EAAE;QAClB,IAAI,KAAK,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC,KAAK,CAAC;QACrB,CAAC;QACD,MAAM,QAAQ,GAAG;YACf,KAAK,EAAE,IAAI;YACX,qBAAqB,EAAE,KAAK,CAAC,SAAS;SACvC,CAAC;QAEF,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACpC,CAAC;QACD,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACjC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACrC,CAAC;AAEY,QAAA,YAAY,GAAG,uBAAQ,CAAC,MAAM,CAEzC;IACA,GAAG,EAAE,SAAS,YAAY,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAsB;QAC/E,OAAO,IAAA,eAAO,EAAC,GAAG,EAAE;YAClB,MAAM,SAAS,GAAG;gBAChB,GAAG;gBACH,MAAM;gBACN,QAAQ;aACT,CAAC;YACF,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,SAAS,CAAC;YACnB,CAAC;YACD,OAAO;gBACL,SAAS;aACV,CAAC;QACJ,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,OAAO,EAAE,SAAS,YAAY;QAC5B,OAAO,EAAE,CAAC;IACZ,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["// Fork of @react-navigation/native Link.tsx with `href` and `replace` support added and\n// `to` / `action` support removed.\nimport { useMemo, MouseEvent } from 'react';\nimport { TextProps, GestureResponderEvent, Platform } from 'react-native';\n\nimport { Href } from '../types';\nimport { SingularOptions } from '../useScreens';\n\n// docsMissing\n/**\n * @platform web\n */\nexport type WebAnchorProps = {\n  /**\n   * Specifies where to open the [`href`](#href).\n   *\n   * - **_self**: the current tab.\n   * - **_blank**: opens in a new tab or window.\n   * - **_parent**: opens in the parent browsing context. If no parent, defaults to **_self**.\n   * - **_top**: opens in the highest browsing context ancestor. If no ancestors,\n   * defaults to **_self**.\n   *\n   * This property is passed to the underlying anchor (`<a>`) tag.\n   *\n   * @default '_self'\n   *\n   * @example\n   * ```jsx\n   * <Link href=\"https://expo.dev\" target=\"_blank\">Go to Expo in new tab</Link>\n   * ```\n   */\n  target?: '_self' | '_blank' | '_parent' | '_top' | (string & object);\n\n  /**\n   * Specifies the relationship between the [`href`](#href) and the current route.\n   *\n   * Common values:\n   * - **nofollow**: Indicates to search engines that they should not follow the `href`.\n   * This is often used for user-generated content or links that should not influence\n   * search engine rankings.\n   * - **noopener**: Suggests that the `href` should not have access to the opening\n   * window's `window.opener` object, which is a security measure to prevent potentially\n   * harmful behavior in cases of links that open new tabs or windows.\n   * - **noreferrer**: Requests that the browser does not send the `Referer` HTTP header\n   * when navigating to the `href`. This can enhance user privacy.\n   *\n   * The `rel` property is primarily used for informational and instructive purposes, helping browsers and web\n   * crawlers make better decisions about how to handle and interpret the links on a web\n   * page. It is important to use appropriate `rel` values to ensure that links behave as intended and adhere\n   * to best practices for web development and SEO (Search Engine Optimization).\n   *\n   * This property is passed to the underlying anchor (`<a>`) tag.\n   *\n   * @example\n   * ```jsx\n   * <Link href=\"https://expo.dev\" rel=\"nofollow\">Go to Expo</Link>\n   * ```\n   */\n  rel?: string;\n\n  /**\n   * Specifies that the [`href`](#href) should be downloaded when the user clicks on the\n   * link, instead of navigating to it. It is typically used for links that point to\n   * files that the user should download, such as PDFs, images, documents, and more.\n   *\n   * The value of the `download` property, which represents the filename for the\n   * downloaded file. This property is passed to the underlying anchor (`<a>`) tag.\n   *\n   * @example\n   * ```jsx\n   * <Link href=\"/image.jpg\" download=\"my-image.jpg\">Download image</Link>\n   * ```\n   */\n  download?: string;\n};\n\n// @docsMissing\nexport interface LinkProps extends Omit<TextProps, 'href'>, WebAnchorProps {\n  /**\n   * The path of the route to navigate to. It can either be:\n   * - **string**: A full path like `/profile/settings` or a relative path like `../settings`.\n   * - **object**: An object with a `pathname` and optional `params`. The `pathname` can be\n   * a full path like `/profile/settings` or a relative path like `../settings`. The\n   * params can be an object of key-value pairs.\n   *\n   * @example\n   * ```tsx Dynamic\n   * import { Link } from 'expo-router';\n   * import { View } from 'react-native';\n   *\n   * export default function Route() {\n   *  return (\n   *   <View>\n   *    <Link href=\"/about\">About</Link>\n   *    <Link\n   *     href={{\n   *       pathname: '/user/[id]',\n   *       params: { id: 'bacon' }\n   *     }}>\n   *       View user\n   *    </Link>\n   *   </View>\n   *  );\n   *}\n   * ```\n   */\n  href: Href;\n\n  // TODO(EvanBacon): This may need to be extracted for React Native style support.\n  /**\n   * Used to customize the `Link` component. It will forward all props to the\n   * first child of the `Link`. Note that the child component must accept\n   * `onPress` or `onClick` props. The `href` and `role` are also\n   * passed to the child.\n   *\n   * @example\n   * ```tsx\n   * import { Link } from 'expo-router';\n   * import { Pressable, Text } from 'react-native';\n   *\n   * export default function Route() {\n   *  return (\n   *   <View>\n   *    <Link href=\"/home\" asChild>\n   *      <Pressable>\n   *       <Text>Home</Text>\n   *      </Pressable>\n   *    </Link>\n   *   </View>\n   *  );\n   *}\n   * ```\n   */\n  asChild?: boolean;\n\n  /**\n   * Removes the current route from the history and replace it with the\n   * specified URL. This is useful for [redirects](/router/reference/redirects/).\n   *\n   * @example\n   *```tsx\n   * import { Link } from 'expo-router';\n   * import { View } from 'react-native';\n   *\n   * export default function Route() {\n   *  return (\n   *   <View>\n   *     <Link replace href=\"/feed\">Login</Link>\n   *   </View>\n   *  );\n   *}\n   * ```\n   */\n  replace?: boolean;\n  /**\n   * Always pushes a new route, and never pops or replaces to existing route.\n   * You can push the current route multiple times or with new parameters.\n   *\n   * @example\n   *```tsx\n   * import { Link } from 'expo-router';\n   * import { View } from 'react-native';\n   *\n   * export default function Route() {\n   *  return (\n   *   <View>\n   *     <Link push href=\"/feed\">Login</Link>\n   *   </View>\n   *  );\n   *}\n   * ```\n   */\n  push?: boolean;\n  /**\n   * While in a stack, this will dismiss screens until the provided `href` is reached. If the href is not found,\n   * it will instead replace the current screen with the provided `href`.\n   *\n   * @example\n   *```tsx\n   * import { Link } from 'expo-router';\n   * import { View } from 'react-native';\n   *\n   * export default function Route() {\n   *  return (\n   *   <View>\n   *     <Link dismissTo href=\"/feed\">Close modal</Link>\n   *   </View>\n   *  );\n   *}\n   * ```\n   */\n  dismissTo?: boolean;\n\n  /**\n   * On native, this can be used with CSS interop tools like Nativewind.\n   * On web, this sets the HTML `class` directly.\n   */\n  className?: string;\n\n  onPress?: (event: MouseEvent<HTMLAnchorElement> | GestureResponderEvent) => void;\n\n  /**\n   * Relative URL references are either relative to the directory or the document.\n   * By default, relative paths are relative to the document.\n   *\n   * @see [Resolving relative references in Mozilla's documentation](https://developer.mozilla.org/en-US/docs/Web/API/URL_API/Resolving_relative_references).\n   */\n  relativeToDirectory?: boolean;\n\n  /**\n   * Replaces the initial screen with the current route.\n   */\n  withAnchor?: boolean;\n\n  /**\n   * When navigating in a Stack, if the target is valid then screens in the history that matches\n   * the uniqueness constraint will be removed.\n   *\n   * If used with `push`, the history will be filtered even if no navigation occurs.\n   */\n  dangerouslySingular?: SingularOptions;\n\n  /**\n   * Prefetches the route when the component is rendered on a focused screen.\n   */\n  prefetch?: boolean;\n}\n\n// Mutate the style prop to add the className on web.\nexport function useInteropClassName(props: { style?: TextProps['style']; className?: string }) {\n  if (Platform.OS !== 'web') {\n    return props.style;\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useMemo(() => {\n    if (props.className == null) {\n      return props.style;\n    }\n    const cssStyle = {\n      $$css: true,\n      __routerLinkClassName: props.className,\n    };\n\n    if (Array.isArray(props.style)) {\n      return [...props.style, cssStyle];\n    }\n    return [props.style, cssStyle];\n  }, [props.style, props.className]);\n}\n\nexport const useHrefAttrs = Platform.select<\n  (props: Partial<LinkProps>) => { hrefAttrs?: any } & Partial<LinkProps>\n>({\n  web: function useHrefAttrs({ asChild, rel, target, download }: Partial<LinkProps>) {\n    return useMemo(() => {\n      const hrefAttrs = {\n        rel,\n        target,\n        download,\n      };\n      if (asChild) {\n        return hrefAttrs;\n      }\n      return {\n        hrefAttrs,\n      };\n    }, [asChild, rel, target, download]);\n  },\n  default: function useHrefAttrs() {\n    return {};\n  },\n});\n"]}