{"version": 3, "file": "linking.js", "sourceRoot": "", "sources": ["../../src/link/linking.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA,sCA4BC;AAID,gCAQC;AAgBD,8BA2CC;AAxHD,sDAAwC;AACxC,+CAAwC;AAExC,mEAGoC;AACpC,+DAA4D;AAmHjC,iGAnHlB,mCAAgB,OAmHkB;AAlH3C,+DAA4D;AAkHnD,iGAlHA,mCAAgB,OAkHA;AAjHzB,mDAA8D;AAC9D,8DAAuD;AAIvD,MAAM,QAAQ,GAAG,OAAO,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC;AAEjF,8EAA8E;AAC9E,wEAAwE;AACxE,+EAA+E;AAC/E,8GAA8G;AAC9G,8EAA8E;AAC9E,SAAgB,aAAa;IAG3B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;QACnD,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC9B,CAAC;IACD,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QAC1B,mFAAmF;QACnF,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QACpC,OAAO,CACL,0BAA0B,CAAC,GAAG,CAAC;YAC/B,uFAAuF;YACvF,4DAA4D;YAC5D,UAAU,EAAE,CACb,CAAC;IACJ,CAAC;IAED,qGAAqG;IACrG,OAAO,OAAO,CAAC,OAAO,CAAC,IAAA,qCAAwB,GAAE,CAAC,CAAC,IAAI,CACrD,CAAC,GAAG,EAAE,EAAE,CACN,0BAA0B,CAAC,GAAG,CAAC;QAC/B,uFAAuF;QACvF,4DAA4D;QAC5D,UAAU,EAAE,CACf,CAAC;AACJ,CAAC;AAED,IAAI,QAA4B,CAAC;AAEjC,SAAgB,UAAU;IACxB,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QAC3B,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,GAAG,IAAA,4CAAuB,EAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,2DAA2D;AAC3D,SAAS,0BAA0B,CAA0B,GAAM;IACjE,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACtB,OAAO,GAAG,CAAC;IACb,CAAC;IACD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,IAAA,qDAAgC,EAAC,GAAG,CAAC,CAAC;IACxE,+EAA+E;IAC/E,oFAAoF;IACpF,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,GAAG,EAAE,CAAC;QAClC,OAAO,CAAC,UAAU,EAAE,GAAG,WAAW,CAAM,CAAC;IAC3C,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAgB,SAAS,CACvB,aAAuC,EACvC,SAAuC;IAEvC,OAAO,CAAC,QAA+B,EAAE,EAAE;QACzC,IAAI,QAA0D,CAAC;QAE/D,MAAM,kBAAkB,GAAG,aAAa,EAAE,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC;QAEvE,IAAI,QAAQ,EAAE,CAAC;YACb,mDAAmD;YACnD,QAAQ,GAAG,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;gBAC3B,IAAI,IAAI,GAA8B,0BAA0B,CAAC,GAAG,CAAC,CAAC;gBACtE,IAAI,GAAG,IAAA,mCAAc,EAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBACvC,IAAI,IAAI,IAAI,aAAa,EAAE,kBAAkB,EAAE,CAAC;oBAC9C,IAAI,GAAG,MAAM,aAAa,CAAC,kBAAkB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;gBAChF,CAAC;gBAED,IAAI,IAAI,EAAE,CAAC;oBACT,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACjB,CAAC;YACH,CAAC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;gBAC3B,IAAI,IAAI,GAAG,IAAA,mCAAc,EAAC,GAAG,EAAE,SAAS,CAAC,CAAC;gBAC1C,IAAI,IAAI,IAAI,aAAa,EAAE,kBAAkB,EAAE,CAAC;oBAC9C,IAAI,GAAG,MAAM,aAAa,CAAC,kBAAkB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;gBAChF,CAAC;gBAED,IAAI,IAAI,EAAE,CAAC;oBACT,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACjB,CAAC;YACH,CAAC,CAAC;QACJ,CAAC;QAED,MAAM,YAAY,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE/D,OAAO,GAAG,EAAE;YACV,2FAA2F;YAC3F,YAAY,EAAE,MAAM,EAAE,EAAE,CAAC;YACzB,kBAAkB,EAAE,EAAE,CAAC;QACzB,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { LinkingOptions } from '@react-navigation/native';\nimport * as Linking from 'expo-linking';\nimport { Platform } from 'react-native';\n\nimport {\n  parsePathAndParamsFromExpoGoLink,\n  parsePathFromExpoGoLink,\n} from '../fork/extractPathFromURL';\nimport { getPathFromState } from '../fork/getPathFromState';\nimport { getStateFromPath } from '../fork/getStateFromPath';\nimport { getInitialURLWithTimeout } from '../fork/useLinking';\nimport { applyRedirects } from '../getRoutesRedirects';\nimport { StoreRedirects } from '../global-state/router-store';\nimport { NativeIntent } from '../types';\n\nconst isExpoGo = typeof expo !== 'undefined' && globalThis.expo?.modules?.ExpoGo;\n\n// A custom getInitialURL is used on native to ensure the app always starts at\n// the root path if it's launched from something other than a deep link.\n// This helps keep the native functionality working like the web functionality.\n// For example, if you had a root navigator where the first screen was `/settings` and the second was `/index`\n// then `/index` would be used on web and `/settings` would be used on native.\nexport function getInitialURL(): ReturnType<\n  NonNullable<LinkingOptions<Record<string, unknown>>['getInitialURL']>\n> {\n  if (typeof window === 'undefined') {\n    return '';\n  }\n  if (Platform.OS === 'web' && window.location?.href) {\n    return window.location.href;\n  }\n  if (Platform.OS === 'ios') {\n    // Use the new Expo API for iOS. This has better support for App Clips and handoff.\n    const url = Linking.getLinkingURL();\n    return (\n      parseExpoGoUrlFromListener(url) ??\n      // The path will be nullish in bare apps when the app is launched from the home screen.\n      // TODO(EvanBacon): define some policy around notifications.\n      getRootURL()\n    );\n  }\n\n  // TODO: Figure out if expo-linking on Android has full interop with the React Native implementation.\n  return Promise.resolve(getInitialURLWithTimeout()).then(\n    (url) =>\n      parseExpoGoUrlFromListener(url) ??\n      // The path will be nullish in bare apps when the app is launched from the home screen.\n      // TODO(EvanBacon): define some policy around notifications.\n      getRootURL()\n  );\n}\n\nlet _rootURL: string | undefined;\n\nexport function getRootURL(): string {\n  if (_rootURL === undefined) {\n    _rootURL = Linking.createURL('/');\n    if (isExpoGo) {\n      _rootURL = parsePathFromExpoGoLink(_rootURL);\n    }\n  }\n  return _rootURL;\n}\n\n// Expo Go is weird and requires the root path to be `/--/`\nfunction parseExpoGoUrlFromListener<T extends string | null>(url: T): T {\n  if (!url || !isExpoGo) {\n    return url;\n  }\n  const { pathname, queryString } = parsePathAndParamsFromExpoGoLink(url);\n  // If the URL is defined (default in Expo Go dev apps) and the URL has no path:\n  // `exp://*************:19000/` then use the default `exp://*************:19000/--/`\n  if (!pathname || pathname === '/') {\n    return (getRootURL() + queryString) as T;\n  }\n  return url;\n}\n\nexport function subscribe(\n  nativeLinking: NativeIntent | undefined,\n  redirects: StoreRedirects[] | undefined\n) {\n  return (listener: (url: string) => void) => {\n    let callback: (({ url }: { url: string }) => void) | undefined;\n\n    const legacySubscription = nativeLinking?.legacy_subscribe?.(listener);\n\n    if (isExpoGo) {\n      // This extra work is only done in the Expo Go app.\n      callback = async ({ url }) => {\n        let href: string | undefined | null = parseExpoGoUrlFromListener(url);\n        href = applyRedirects(href, redirects);\n        if (href && nativeLinking?.redirectSystemPath) {\n          href = await nativeLinking.redirectSystemPath({ path: href, initial: false });\n        }\n\n        if (href) {\n          listener(href);\n        }\n      };\n    } else {\n      callback = async ({ url }) => {\n        let href = applyRedirects(url, redirects);\n        if (href && nativeLinking?.redirectSystemPath) {\n          href = await nativeLinking.redirectSystemPath({ path: href, initial: false });\n        }\n\n        if (href) {\n          listener(href);\n        }\n      };\n    }\n\n    const subscription = Linking.addEventListener('url', callback);\n\n    return () => {\n      // https://github.com/facebook/react-native/commit/6d1aca806cee86ad76de771ed3a1cc62982ebcd7\n      subscription?.remove?.();\n      legacySubscription?.();\n    };\n  };\n}\n\nexport { getStateFromPath, getPathFromState };\n"]}