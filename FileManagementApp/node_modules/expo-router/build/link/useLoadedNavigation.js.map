{"version": 3, "file": "useLoadedNavigation.js", "sourceRoot": "", "sources": ["../../src/link/useLoadedNavigation.ts"], "names": [], "mappings": ";;AAUA,kDAuCC;AAED,sDASC;AA5DD,qDAA0F;AAC1F,iCAAiE;AAEjE,+DAAqD;AAMrD,gFAAgF;AAChF,SAAgB,mBAAmB;IACjC,MAAM,UAAU,GAAG,IAAA,sBAAa,GAAE,CAAC;IACnC,MAAM,SAAS,GAAG,IAAA,cAAM,EAAC,IAAI,CAAC,CAAC;IAC/B,MAAM,OAAO,GAAG,IAAA,cAAM,EAA8C,EAAE,CAAC,CAAC;IAExE,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;QACzB,OAAO,GAAG,EAAE;YACV,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;QAC5B,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,KAAK,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE;QAC7B,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACtB,MAAM,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC;YACzC,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC;YACrB,gBAAgB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACpC,QAAQ,CAAC,UAA+B,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;IAEjB,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,IAAI,oBAAK,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAChC,KAAK,EAAE,CAAC;QACV,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,MAAM,IAAI,GAAG,IAAA,mBAAW,EACtB,CAAC,EAA2C,EAAE,EAAE;QAC9C,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzB,IAAI,oBAAK,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAChC,KAAK,EAAE,CAAC;QACV,CAAC;IACH,CAAC,EACD,CAAC,KAAK,CAAC,CACR,CAAC;IAEF,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,qBAAqB;IACnC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,IAAA,gBAAQ,EAA2B,IAAI,CAAC,CAAC;IAC7E,MAAM,cAAc,GAAG,mBAAmB,EAAE,CAAC;IAE7C,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,cAAc,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9C,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,UAAU,CAAC;AACpB,CAAC", "sourcesContent": ["import { NavigationProp, NavigationState, useNavigation } from '@react-navigation/native';\nimport { useCallback, useState, useEffect, useRef } from 'react';\n\nimport { store } from '../global-state/router-store';\n\ntype GenericNavigation = NavigationProp<ReactNavigation.RootParamList> & {\n  getState(): NavigationState | undefined;\n};\n\n/** Returns a callback which is invoked when the navigation state has loaded. */\nexport function useLoadedNavigation() {\n  const navigation = useNavigation();\n  const isMounted = useRef(true);\n  const pending = useRef<((navigation: GenericNavigation) => void)[]>([]);\n\n  useEffect(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n\n  const flush = useCallback(() => {\n    if (isMounted.current) {\n      const pendingCallbacks = pending.current;\n      pending.current = [];\n      pendingCallbacks.forEach((callback) => {\n        callback(navigation as GenericNavigation);\n      });\n    }\n  }, [navigation]);\n\n  useEffect(() => {\n    if (store.navigationRef.current) {\n      flush();\n    }\n  }, [flush]);\n\n  const push = useCallback(\n    (fn: (navigation: GenericNavigation) => void) => {\n      pending.current.push(fn);\n      if (store.navigationRef.current) {\n        flush();\n      }\n    },\n    [flush]\n  );\n\n  return push;\n}\n\nexport function useOptionalNavigation(): GenericNavigation | null {\n  const [navigation, setNavigation] = useState<GenericNavigation | null>(null);\n  const loadNavigation = useLoadedNavigation();\n\n  useEffect(() => {\n    loadNavigation((nav) => setNavigation(nav));\n  }, []);\n\n  return navigation;\n}\n"]}