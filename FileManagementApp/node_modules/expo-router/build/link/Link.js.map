{"version": 3, "file": "Link.js", "sourceRoot": "", "sources": ["../../src/link/Link.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;;;;AACb,wFAAwF;AACxF,mCAAmC;AACnC,iCAA8F;AAC9F,+CAAqE;AAErE,iCAAqC;AACrC,8EAAsD;AAEtD,iDAA8F;AAC9F,0CAAuC;AACvC,qCAAkC;AAQlC,uCAAqD;AAA5C,oGAAA,QAAQ,OAAA;AAEjB;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACU,QAAA,IAAI,GAAG,IAAA,kBAAU,EAAC,cAAc,CAA6B,CAAC;AAE3E,YAAI,CAAC,WAAW,GAAG,kBAAW,CAAC;AAE/B,SAAS,cAAc,CACrB,EACE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS;AACT,yDAAyD;AACzD,mBAAmB,EACnB,OAAO,EACP,GAAG,EACH,MAAM,EACN,QAAQ,EACR,UAAU,EACV,mBAAmB,EAAE,QAAQ,EAC7B,QAAQ,EACR,GAAG,IAAI,EACG,EACZ,GAAuB;IAEvB,qDAAqD;IACrD,MAAM,KAAK,GAAG,IAAA,kCAAmB,EAAC,IAAI,CAAC,CAAC;IAExC,+GAA+G;IAC/G,MAAM,SAAS,GAAG,IAAA,2BAAY,EAAC,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;IAEnE,MAAM,YAAY,GAAG,IAAA,eAAO,EAAC,GAAG,EAAE;QAChC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QACD,OAAO,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC;IAC3B,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAEX,IAAI,KAAK,CAAC;IACV,IAAI,IAAI;QAAE,KAAK,GAAG,MAAM,CAAC;IACzB,IAAI,OAAO;QAAE,KAAK,GAAG,SAAS,CAAC;IAC/B,IAAI,SAAS;QAAE,KAAK,GAAG,QAAQ,CAAC;IAEhC,MAAM,KAAK,GAAG,IAAA,4BAAkB,EAAC;QAC/B,IAAI,EAAE,YAAY;QAClB,KAAK;QACL,mBAAmB;QACnB,UAAU;QACV,mBAAmB,EAAE,QAAQ;KAC9B,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,CAAC,CAAwD,EAAE,EAAE;QAC3E,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;QACD,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,WAAI,CAAC,CAAC,CAAC,mBAAI,CAAC;IAExC,6HAA6H;IAC7H,MAAM,OAAO,GAAG,CACd,CAAC,SAAS,CACR,GAAG,CAAC,CAAC,GAAG,CAAC,CACT,IAAI,KAAK,CAAC,CACV,IAAI,SAAS,CAAC,CACd,IAAI,IAAI,CAAC,CACT,KAAK,CAAC,CAAC,KAAK,CAAC,CACb,IAAI,uBAAQ,CAAC,MAAM,CAAC;QAClB,GAAG,EAAE;YACH,OAAO,EAAE,OAAO;SACV;QACR,OAAO,EAAE,EAAE,OAAO,EAAE;KACrB,CAAC,CAAC,EACH,CACH,CAAC;IAEF,OAAO,QAAQ,CAAC,CAAC,CAAC,CAChB,EACE;MAAA,CAAC,mBAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EACrB;MAAA,CAAC,OAAO,CACV;IAAA,GAAG,CACJ,CAAC,CAAC,CAAC,CACF,OAAO,CACR,CAAC;AACJ,CAAC", "sourcesContent": ["'use client';\n// Fork of @react-navigation/native Link.tsx with `href` and `replace` support added and\n// `to` / `action` support removed.\nimport { PropsWithChildren, forwardRef, useMemo, MouseEvent, ForwardedRef, JSX } from 'react';\nimport { Text, GestureResponderEvent, Platform } from 'react-native';\n\nimport { resolveHref } from './href';\nimport useLinkToPathProps from './useLinkToPathProps';\nimport { Href } from '../types';\nimport { useInteropClassName, useHrefAttrs, LinkProps, WebAnchorProps } from './useLinkHooks';\nimport { Prefetch } from '../Prefetch';\nimport { Slot } from '../ui/Slot';\n\nexport interface LinkComponent {\n  (props: PropsWithChildren<LinkProps>): JSX.Element;\n  /** Helper method to resolve an Href object into a string. */\n  resolveHref: (href: Href) => string;\n}\n\nexport { Redirect, RedirectProps } from './Redirect';\n\n/**\n * Component that renders a link using [`href`](#href) to another route.\n * By default, it accepts children and wraps them in a `<Text>` component.\n *\n * Uses an anchor tag (`<a>`) on web and performs a client-side navigation to preserve\n * the state of the website and navigate faster. The web-only attributes such as `target`,\n * `rel`, and `download` are supported and passed to the anchor tag on web. See\n * [`WebAnchorProps`](#webanchorprops) for more details.\n *\n * > **Note**: Client-side navigation works with both single-page apps,\n * and [static-rendering](/router/reference/static-rendering/).\n *\n * @example\n * ```tsx\n * import { Link } from 'expo-router';\n * import { View } from 'react-native';\n *\n * export default function Route() {\n *  return (\n *   <View>\n *    <Link href=\"/about\">About</Link>\n *   </View>\n *  );\n *}\n * ```\n */\nexport const Link = forwardRef(ExpoRouterLink) as unknown as LinkComponent;\n\nLink.resolveHref = resolveHref;\n\nfunction ExpoRouterLink(\n  {\n    href,\n    replace,\n    push,\n    dismissTo,\n    // TODO: This does not prevent default on the anchor tag.\n    relativeToDirectory,\n    asChild,\n    rel,\n    target,\n    download,\n    withAnchor,\n    dangerouslySingular: singular,\n    prefetch,\n    ...rest\n  }: LinkProps,\n  ref: ForwardedRef<Text>\n) {\n  // Mutate the style prop to add the className on web.\n  const style = useInteropClassName(rest);\n\n  // If not passing asChild, we need to forward the props to the anchor tag using React Native Web's `hrefAttrs`.\n  const hrefAttrs = useHrefAttrs({ asChild, rel, target, download });\n\n  const resolvedHref = useMemo(() => {\n    if (href == null) {\n      throw new Error('Link: href is required');\n    }\n    return resolveHref(href);\n  }, [href]);\n\n  let event;\n  if (push) event = 'PUSH';\n  if (replace) event = 'REPLACE';\n  if (dismissTo) event = 'POP_TO';\n\n  const props = useLinkToPathProps({\n    href: resolvedHref,\n    event,\n    relativeToDirectory,\n    withAnchor,\n    dangerouslySingular: singular,\n  });\n\n  const onPress = (e: MouseEvent<HTMLAnchorElement> | GestureResponderEvent) => {\n    if ('onPress' in rest) {\n      rest.onPress?.(e);\n    }\n    props.onPress(e);\n  };\n\n  const Component = asChild ? Slot : Text;\n\n  // Avoid using createElement directly, favoring JSX, to allow tools like NativeWind to perform custom JSX handling on native.\n  const element = (\n    <Component\n      ref={ref}\n      {...props}\n      {...hrefAttrs}\n      {...rest}\n      style={style}\n      {...Platform.select({\n        web: {\n          onClick: onPress,\n        } as any,\n        default: { onPress },\n      })}\n    />\n  );\n\n  return prefetch ? (\n    <>\n      <Prefetch href={href} />\n      {element}\n    </>\n  ) : (\n    element\n  );\n}\n\nexport { LinkProps, WebAnchorProps };\n"]}