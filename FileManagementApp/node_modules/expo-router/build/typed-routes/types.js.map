{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/typed-routes/types.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * The main routing type for Expo Router.\n *\n * @internal\n * @hidden\n */\nexport namespace ExpoRouter {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-object-type\n  export interface __routes {}\n}\n\nexport type HrefObject = {\n  /** The path of the route. */\n  pathname: string;\n  /** Optional parameters for the route. */\n  params?: UnknownInputParams;\n};\n\n/**\n * @hidden\n */\nexport type HrefInputParamsObject = {\n  /** The path of the route. */\n  pathname: string;\n  /** Optional input parameters for the route. */\n  params?: UnknownInputParams;\n};\n\n/**\n * @hidden\n */\nexport type HrefOutputParamsObject = {\n  /** The path of the route */\n  pathname: string;\n  /** Optional output parameters for the route */\n  params?: UnknownOutputParams;\n};\n\nexport type RelativePathString = `./${string}` | `../${string}` | '..';\nexport type SearchOrHash = `?${string}` | `#${string}`;\nexport type ExternalPathString = `${string}:${string}` | `//${string}`;\nexport type Route = Exclude<\n  Extract<Href, object>['pathname'], // Use the HrefObject, as it doesn't have query params\n  RelativePathString | ExternalPathString\n>;\n\n/**\n * The main routing type for Expo Router. It includes all available routes with strongly\n * typed parameters. It can either be:\n * - **string**: A full path like `/profile/settings` or a relative path like `../settings`.\n * - **object**: An object with a `pathname` and optional `params`. The `pathname` can be\n * a full path like `/profile/settings` or a relative path like `../settings`.\n * The params can be an object of key-value pairs.\n *\n * An Href can either be a string or an object.\n */\nexport type Href<T extends ExpoRouter.__routes = ExpoRouter.__routes> = T extends { href: any }\n  ? T['href']\n  : string | HrefObject;\n\n/*\n * @hidden\n */\nexport type HrefInputParams<T extends ExpoRouter.__routes = ExpoRouter.__routes> = T extends {\n  hrefInputParams: any;\n}\n  ? T['hrefInputParams']\n  : HrefInputParamsObject;\n\n/*\n * @hidden\n */\nexport type HrefOutputParams<T extends ExpoRouter.__routes = ExpoRouter.__routes> = T extends {\n  hrefOutputParams: any;\n}\n  ? T['hrefOutputParams']\n  : HrefOutputParamsObject;\n\n/**\n * @hidden\n */\nexport type RouteInputParams<T extends Route> =\n  Extract<Href, { pathname: T }> extends never\n    ? HrefInputParams extends infer H\n      ? H extends Record<'pathname' | 'params', any>\n        ? T extends H['pathname']\n          ? H['params']\n          : never\n        : never\n      : never\n    : Extract<HrefInputParams, { pathname: T }>['params'];\n\n/**\n * @hidden\n */\nexport type RouteOutputParams<T extends Route> =\n  Extract<HrefOutputParams, { pathname: T }> extends never\n    ? HrefOutputParams extends infer H\n      ? H extends Record<'pathname' | 'params', any>\n        ? T extends H['pathname']\n          ? H['params']\n          : never\n        : never\n      : never\n    : Extract<HrefOutputParams, { pathname: T }>['params'];\n\n/**\n * @hidden\n */\nexport type RouteParams<T extends Route> = RouteOutputParams<T>;\n\n/**\n * Routes can have known inputs (e.g query params).\n * Unlike outputs, inputs can be `undefined` or `null`.\n *\n * @hidden\n */\nexport type UnknownInputParams = Record<\n  string,\n  string | number | undefined | null | (string | number)[]\n>;\n\n/**\n * Routes can have unknown outputs (e.g query params).\n * Unlike inputs, outputs can't be undefined or null.\n *\n * @hidden\n */\nexport type UnknownOutputParams = Record<string, string | string[]>;\n\n/**\n * Return only the RoutePart of a string. If the string has multiple parts return never\n *\n * string   | type\n *| ---------|------|\n *| 123      | 123 |\n *| /123/abc | never |\n *| 123?abc  | never |\n *| ./123    | never |\n *| /123     | never |\n *| 123/../  | never |\n *\n * @hidden\n */\nexport type SingleRoutePart<S extends string | object> = S extends object\n  ? never\n  : S extends `${string}/${string}`\n    ? never\n    : S extends `${string}${SearchOrHash}`\n      ? never\n      : S extends ''\n        ? never\n        : S extends `(${string})`\n          ? never\n          : S extends `[${string}]`\n            ? never\n            : S;\n\n/**\n * @deprecated Use `RouteParams` or `StrictRouteParams` instead.\n *\n * @hidden\n */\nexport type SearchParams<T extends string = never> = RouteParams<T>;\n\n/**\n * @hidden\n */\nexport type RouteSegments<HrefOrSegments extends Route | string[]> = HrefOrSegments extends string[]\n  ? HrefOrSegments\n  : HrefOrSegments extends `.${string}`\n    ? never\n    : HrefOrSegments extends ``\n      ? never\n      : HrefOrSegments extends `/${infer PartA}`\n        ? RouteSegments<PartA>\n        : HrefOrSegments extends `${infer PartA}?${string}`\n          ? RouteSegments<PartA>\n          : HrefOrSegments extends `${infer PartA}#${string}`\n            ? RouteSegments<PartA>\n            : HrefOrSegments extends `${infer PartA}/${infer PartB}`\n              ? [PartA, ...RouteSegments<PartB>]\n              : [HrefOrSegments];\n"]}