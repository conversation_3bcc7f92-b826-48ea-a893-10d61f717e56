{"version": 3, "file": "getRootComponent.js", "sourceRoot": "", "sources": ["../../src/static/getRootComponent.ts"], "names": [], "mappings": ";;AAQA,4CAeC;AAvBD;;;;;GAKG;AACH,qDAA2D;AAE3D,SAAgB,gBAAgB;IAC9B,MAAM,IAAI,GAAG,eAAW,CAAC,IAAI,EAAE,CAAC;IAChC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;QACjB,OAAQ,OAAO,CAAC,QAAQ,CAA6B,CAAC,IAAI,CAAC;IAC7D,CAAC;IACD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,oDAAoD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACzF,CAAC;IACD,MAAM,GAAG,GAAG,IAAA,eAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,IAAI,CAAC,CAAC,CAAC,2CAA2C,CAAC,CAAC;IAChG,CAAC;IAED,OAAO,GAAG,CAAC,OAAO,CAAC;AACrB,CAAC", "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { ctx as rootContext } from 'expo-router/_ctx-html';\n\nexport function getRootComponent() {\n  const keys = rootContext.keys();\n  if (!keys.length) {\n    return (require('./html') as typeof import('./html')).Html;\n  }\n  if (keys.length > 1) {\n    throw new Error(`Multiple components match the root HTML element: ${keys.join(', ')}`);\n  }\n  const exp = rootContext(keys[0]);\n\n  if (!exp.default) {\n    throw new Error(`The root HTML element \"${keys[0]}\" is missing the required default export.`);\n  }\n\n  return exp.default;\n}\n"]}