/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#include <gtest/gtest.h>

#include <exception>
#include <stdexcept>

#include <cxxreact/RecoverableError.h>

using namespace facebook::react;

TEST(RecoverableError, RunRethrowingAsRecoverableRecoverTest) {
  try {
    RecoverableError::runRethrowingAsRecoverable<std::runtime_error>(
        []() { throw std::runtime_error("catch me"); });
    FAIL() << "Unthrown exception";
  } catch (const RecoverableError& err) {
    ASSERT_STREQ(err.what(), "facebook::react::Recoverable: catch me");
  } catch (...) {
    FAIL() << "Uncaught exception";
  }
}

TEST(RecoverableError, RunRethrowingAsRecoverableFallthroughTest) {
  try {
    RecoverableError::runRethrowingAsRecoverable<std::runtime_error>(
        []() { throw std::logic_error("catch me"); });
    FAIL() << "Unthrown exception";
  } catch (const RecoverableError& err) {
    FAIL() << "Recovered exception that should have fallen through";
  } catch (const std::exception& err) {
    ASSERT_STREQ(err.what(), "catch me");
  }
}
