{"version": 3, "file": "ExpoSystemUI.web.js", "sourceRoot": "", "sources": ["../src/ExpoSystemUI.web.ts"], "names": [], "mappings": "AAAA,eAAe;IACb,uBAAuB;QACrB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,cAAc,GAAG,OAAO,CAAC,kDAAkD,CAAC,CAAC;QACnF,OAAO,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IAC7D,CAAC;IACD,uBAAuB,CAAC,KAAoB;QAC1C,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,IAAI,OAAO,CAAC;QACzD,CAAC;IACH,CAAC;CACF,CAAC", "sourcesContent": ["export default {\n  getBackgroundColorAsync() {\n    if (typeof window === 'undefined') {\n      return null;\n    }\n    const normalizeColor = require('react-native-web/dist/cjs/modules/normalizeColor');\n    return normalizeColor(document.body.style.backgroundColor);\n  },\n  setBackgroundColorAsync(color: string | null) {\n    if (typeof window !== 'undefined') {\n      document.body.style.backgroundColor = color ?? 'white';\n    }\n  },\n};\n"]}