{"version": 3, "file": "SystemUI.js", "sourceRoot": "", "sources": ["../src/SystemUI.ts"], "names": [], "mappings": "AAAA,OAAO,EAAc,QAAQ,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAElE,OAAO,YAAY,MAAM,gBAAgB,CAAC;AAE1C;;;;;;;;;GASG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAAC,KAAwB;IACpE,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,OAAO,MAAM,YAAY,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;SAAM,CAAC;QACN,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxE,OAAO,MAAM,YAAY,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IACjE,CAAC;AACH,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB;IAC3C,OAAO,MAAM,YAAY,CAAC,uBAAuB,EAAE,CAAC;AACtD,CAAC", "sourcesContent": ["import { ColorValue, Platform, processColor } from 'react-native';\n\nimport ExpoSystemUI from './ExpoSystemUI';\n\n/**\n * Changes the root view background color.\n * Call this function in the root file outside of your component.\n *\n * @example\n * ```ts\n * SystemUI.setBackgroundColorAsync(\"black\");\n * ```\n * @param color Any valid [CSS 3 (SVG) color](http://www.w3.org/TR/css3-color/#svg-color).\n */\nexport async function setBackgroundColorAsync(color: ColorValue | null): Promise<void> {\n  if (color == null) {\n    return await ExpoSystemUI.setBackgroundColorAsync(null);\n  } else {\n    const colorNumber = Platform.OS === 'web' ? color : processColor(color);\n    return await ExpoSystemUI.setBackgroundColorAsync(colorNumber);\n  }\n}\n\n/**\n * Gets the root view background color.\n *\n * @example\n * ```ts\n * const color = await SystemUI.getBackgroundColorAsync();\n * ```\n * @returns Current root view background color in hex format. Returns `null` if the background color is not set.\n */\nexport async function getBackgroundColorAsync(): Promise<ColorValue | null> {\n  return await ExpoSystemUI.getBackgroundColorAsync();\n}\n"]}