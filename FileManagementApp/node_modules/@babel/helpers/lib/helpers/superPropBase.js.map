{"version": 3, "names": ["_getPrototypeOf", "require", "_superPropBase", "object", "property", "Object", "prototype", "hasOwnProperty", "call", "getPrototypeOf"], "sources": ["../../src/helpers/superPropBase.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport getPrototypeOf from \"./getPrototypeOf.ts\";\n\nexport default function _superPropBase(object: object, property: PropertyKey) {\n  // Yes, this throws if object is null to being with, that's on purpose.\n  while (!Object.prototype.hasOwnProperty.call(object, property)) {\n    object = getPrototypeOf(object);\n    if (object === null) break;\n  }\n  return object;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,eAAA,GAAAC,OAAA;AAEe,SAASC,cAAcA,CAACC,MAAc,EAAEC,QAAqB,EAAE;EAE5E,OAAO,CAACC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,MAAM,EAAEC,QAAQ,CAAC,EAAE;IAC9DD,MAAM,GAAG,IAAAM,uBAAc,EAACN,MAAM,CAAC;IAC/B,IAAIA,MAAM,KAAK,IAAI,EAAE;EACvB;EACA,OAAOA,MAAM;AACf", "ignoreList": []}