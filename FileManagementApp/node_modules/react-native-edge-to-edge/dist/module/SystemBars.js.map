{"version": 3, "names": ["useEffect", "useMemo", "useRef", "Appearance", "Platform", "StatusBar", "useColorScheme", "NativeModule", "isLightColorScheme", "colorScheme", "getColorScheme", "resolveSystemBarStyle", "style", "toNativeBarStyle", "mergeEntriesStack", "entriesStack", "reduce", "prev", "cur", "prop", "statusBarStyle", "undefined", "navigationBarStyle", "statusBarHidden", "navigationBarHidden", "resolveProps", "hidden", "compactStyle", "compactHidden", "statusBar", "navigationBar", "createStackEntry", "props", "updateImmediate", "currentV<PERSON>ues", "setStatusBarStyle", "nativeStyle", "OS", "setBarStyle", "setNavigationBarStyle", "setStatusBarHidden", "setHidden", "setNavigationBarHidden", "updateEntriesStack", "clearImmediate", "setImmediate", "mergedEntries", "pushStackEntry", "entry", "push", "popStackEntry", "index", "indexOf", "splice", "replaceStackEntry", "newEntry", "setStyle", "SystemBars", "stableProps", "stackEntryRef", "current"], "sourceRoot": "../../src", "sources": ["SystemBars.ts"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,cAAc,QAAQ,cAAc;AAC9E,OAAOC,YAAY,MAAM,gCAAgC;AAKzD,SAASC,kBAAkBA,CAAA,EAAG;EAC5B,MAAMC,WAAW,GAAGN,UAAU,EAAEO,cAAc,CAAC,CAAC,IAAI,OAAO;EAC3D,OAAOD,WAAW,KAAK,OAAO;AAChC;AAEA,SAASE,qBAAqBA,CAC5BC,KAAiC,EACf;EAClB,QAAQA,KAAK;IACX,KAAK,MAAM;MACT,OAAOJ,kBAAkB,CAAC,CAAC,GAAG,MAAM,GAAG,OAAO;IAChD,KAAK,UAAU;MACb,OAAOA,kBAAkB,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM;IAChD;MACE,OAAOI,KAAK;EAChB;AACF;AAEA,SAASC,gBAAgBA,CACvBD,KAAuB,EACuB;EAC9C,OAAOA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,MAAM,GAAG,GAAGA,KAAK,UAAU,GAAG,SAAS;AAC/E;;AAEA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACC,YAA+B,EAAE;EAC1D,OAAOA,YAAY,CAACC,MAAM,CAMxB,CAACC,IAAI,EAAEC,GAAG,KAAK;IACb,KAAK,MAAMC,IAAI,IAAID,GAAG,EAAE;MACtB,IAAIA,GAAG,CAACC,IAAI,CAA0B,IAAI,IAAI,EAAE;QAC9C;QACAF,IAAI,CAACE,IAAI,CAAC,GAAGD,GAAG,CAACC,IAAI,CAAC;MACxB;IACF;IACA,OAAOF,IAAI;EACb,CAAC,EACD;IACEG,cAAc,EAAEC,SAAS;IACzBC,kBAAkB,EAAED,SAAS;IAC7BE,eAAe,EAAE,KAAK;IACtBC,mBAAmB,EAAE;EACvB,CACF,CAAC;AACH;AAEA,SAASC,YAAYA,CAAC;EAAEC,MAAM;EAAEd;AAAuB,CAAC,EAAE;EACxD,MAAMe,YAAY,GAAG,OAAOf,KAAK,KAAK,QAAQ;EAC9C,MAAMgB,aAAa,GAAG,OAAOF,MAAM,KAAK,SAAS;EAEjD,OAAO;IACLN,cAAc,EAAEO,YAAY,GAAGf,KAAK,GAAGA,KAAK,EAAEiB,SAAS;IACvDP,kBAAkB,EAAEK,YAAY,GAAGf,KAAK,GAAGA,KAAK,EAAEkB,aAAa;IAC/DP,eAAe,EAAEK,aAAa,GAAGF,MAAM,GAAGA,MAAM,EAAEG,SAAS;IAC3DL,mBAAmB,EAAEI,aAAa,GAAGF,MAAM,GAAGA,MAAM,EAAEI;EACxD,CAAC;AACH;;AAEA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAsB,EAAmB;EACjE,OAAOP,YAAY,CAACO,KAAK,CAAC;AAC5B;AAEA,MAAMjB,YAA+B,GAAG,EAAE;;AAE1C;AACA,IAAIkB,eAAwC,GAAG,IAAI;;AAEnD;AACA,MAAMC,aAKL,GAAG;EACFd,cAAc,EAAEC,SAAS;EACzBC,kBAAkB,EAAED,SAAS;EAC7BE,eAAe,EAAE,KAAK;EACtBC,mBAAmB,EAAE;AACvB,CAAC;AAED,SAASW,iBAAiBA,CAACvB,KAAuB,EAAE;EAClD,IAAIA,KAAK,KAAKsB,aAAa,CAACd,cAAc,EAAE;IAC1Cc,aAAa,CAACd,cAAc,GAAGR,KAAK;IAEpC,MAAMwB,WAAW,GAAGvB,gBAAgB,CAACD,KAAK,CAAC;IAE3C,IAAIR,QAAQ,CAACiC,EAAE,KAAK,SAAS,EAAE;MAC7B9B,YAAY,EAAE4B,iBAAiB,CAACC,WAAW,CAAC;IAC9C,CAAC,MAAM,IAAIhC,QAAQ,CAACiC,EAAE,KAAK,KAAK,EAAE;MAChChC,SAAS,CAACiC,WAAW,CAACF,WAAW,EAAE,IAAI,CAAC;IAC1C;EACF;AACF;AAEA,SAASG,qBAAqBA,CAAC3B,KAAuB,EAAE;EACtD,IAAIA,KAAK,KAAKsB,aAAa,CAACZ,kBAAkB,EAAE;IAC9CY,aAAa,CAACZ,kBAAkB,GAAGV,KAAK;IAExC,IAAIR,QAAQ,CAACiC,EAAE,KAAK,SAAS,EAAE;MAC7B,MAAMD,WAAW,GAAGvB,gBAAgB,CAACD,KAAK,CAAC;MAC3CL,YAAY,EAAEgC,qBAAqB,CAACH,WAAW,CAAC;IAClD;EACF;AACF;AAEA,SAASI,kBAAkBA,CAACd,MAAe,EAAE;EAC3C,IAAIA,MAAM,KAAKQ,aAAa,CAACX,eAAe,EAAE;IAC5CW,aAAa,CAACX,eAAe,GAAGG,MAAM;IAEtC,IAAItB,QAAQ,CAACiC,EAAE,KAAK,SAAS,EAAE;MAC7B9B,YAAY,EAAEiC,kBAAkB,CAACd,MAAM,CAAC;IAC1C,CAAC,MAAM,IAAItB,QAAQ,CAACiC,EAAE,KAAK,KAAK,EAAE;MAChChC,SAAS,CAACoC,SAAS,CAACf,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IACvC;EACF;AACF;AAEA,SAASgB,sBAAsBA,CAAChB,MAAe,EAAE;EAC/C,IAAIA,MAAM,KAAKQ,aAAa,CAACV,mBAAmB,EAAE;IAChDU,aAAa,CAACV,mBAAmB,GAAGE,MAAM;IAE1C,IAAItB,QAAQ,CAACiC,EAAE,KAAK,SAAS,EAAE;MAC7B9B,YAAY,EAAEmC,sBAAsB,CAAChB,MAAM,CAAC;IAC9C;EACF;AACF;;AAEA;AACA;AACA;AACA,SAASiB,kBAAkBA,CAAA,EAAG;EAC5B,IAAIvC,QAAQ,CAACiC,EAAE,KAAK,SAAS,IAAIjC,QAAQ,CAACiC,EAAE,KAAK,KAAK,EAAE;IACtD,IAAIJ,eAAe,IAAI,IAAI,EAAE;MAC3BW,cAAc,CAACX,eAAe,CAAC;IACjC;IAEAA,eAAe,GAAGY,YAAY,CAAC,MAAM;MACnC,MAAMC,aAAa,GAAGhC,iBAAiB,CAACC,YAAY,CAAC;MACrD,MAAM;QAAEQ,eAAe;QAAEC;MAAoB,CAAC,GAAGsB,aAAa;MAE9D,MAAM1B,cAAc,GAAGT,qBAAqB,CAC1CmC,aAAa,CAAC1B,cAChB,CAAC;MACD,MAAME,kBAAkB,GAAGX,qBAAqB,CAC9CmC,aAAa,CAACxB,kBAChB,CAAC;MAEDa,iBAAiB,CAACf,cAAc,CAAC;MACjCmB,qBAAqB,CAACjB,kBAAkB,CAAC;MACzCkB,kBAAkB,CAACjB,eAAe,CAAC;MACnCmB,sBAAsB,CAAClB,mBAAmB,CAAC;IAC7C,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,cAAcA,CAACf,KAAsB,EAAmB;EAC/D,MAAMgB,KAAK,GAAGjB,gBAAgB,CAACC,KAAK,CAAC;EACrCjB,YAAY,CAACkC,IAAI,CAACD,KAAK,CAAC;EACxBL,kBAAkB,CAAC,CAAC;EACpB,OAAOK,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACF,KAAsB,EAAQ;EACnD,MAAMG,KAAK,GAAGpC,YAAY,CAACqC,OAAO,CAACJ,KAAK,CAAC;EACzC,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;IAChBpC,YAAY,CAACsC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EAC/B;EACAR,kBAAkB,CAAC,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,iBAAiBA,CACxBN,KAAsB,EACtBhB,KAAsB,EACL;EACjB,MAAMuB,QAAQ,GAAGxB,gBAAgB,CAACC,KAAK,CAAC;EACxC,MAAMmB,KAAK,GAAGpC,YAAY,CAACqC,OAAO,CAACJ,KAAK,CAAC;EACzC,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;IAChBpC,YAAY,CAACoC,KAAK,CAAC,GAAGI,QAAQ;EAChC;EACAZ,kBAAkB,CAAC,CAAC;EACpB,OAAOY,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAC5C,KAA+B,EAAE;EACjD,MAAMoB,KAAK,GAAGP,YAAY,CAAC;IAAEb;EAAM,CAAC,CAAC;EAErC,MAAMQ,cAAc,GAAGT,qBAAqB,CAACqB,KAAK,CAACZ,cAAc,CAAC;EAClE,MAAME,kBAAkB,GAAGX,qBAAqB,CAACqB,KAAK,CAACV,kBAAkB,CAAC;EAE1E,IAAI,OAAOF,cAAc,KAAK,QAAQ,EAAE;IACtCe,iBAAiB,CAACf,cAAc,CAAC;EACnC;EACA,IAAI,OAAOE,kBAAkB,KAAK,QAAQ,EAAE;IAC1CiB,qBAAqB,CAACjB,kBAAkB,CAAC;EAC3C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASmB,SAASA,CAACf,MAAiC,EAAE;EACpD,MAAM;IAAEH,eAAe;IAAEC;EAAoB,CAAC,GAAGC,YAAY,CAAC;IAAEC;EAAO,CAAC,CAAC;EAEzE,IAAI,OAAOH,eAAe,KAAK,SAAS,EAAE;IACxCiB,kBAAkB,CAACjB,eAAe,CAAC;EACrC;EACA,IAAI,OAAOC,mBAAmB,KAAK,SAAS,EAAE;IAC5CkB,sBAAsB,CAAClB,mBAAmB,CAAC;EAC7C;AACF;AAEA,OAAO,SAASiC,UAAUA,CAACzB,KAAsB,EAAE;EACjD,MAAM;IACJZ,cAAc;IACdE,kBAAkB;IAClBC,eAAe;IACfC;EACF,CAAC,GAAGC,YAAY,CAACO,KAAK,CAAC;EAEvB,MAAM0B,WAAW,GAAGzD,OAAO,CACzB,OAAO;IACLW,KAAK,EACHQ,cAAc,KAAKE,kBAAkB,GACjCF,cAAc,GACd;MAAES,SAAS,EAAET,cAAc;MAAEU,aAAa,EAAER;IAAmB,CAAC;IACtEI,MAAM,EACJH,eAAe,KAAKC,mBAAmB,GACnCD,eAAe,GACf;MAAEM,SAAS,EAAEN,eAAe;MAAEO,aAAa,EAAEN;IAAoB;EACzE,CAAC,CAAC,EACF,CAACJ,cAAc,EAAEE,kBAAkB,EAAEC,eAAe,EAAEC,mBAAmB,CAC3E,CAAC;EAED,MAAMf,WAAW,GAAGH,cAAc,CAAC,CAAC;EACpC,MAAMqD,aAAa,GAAGzD,MAAM,CAAyB,IAAI,CAAC;EAE1DF,SAAS,CAAC,MAAM;IACd;IACA;IACA;IACA;IACA2D,aAAa,CAACC,OAAO,GAAGb,cAAc,CAACW,WAAW,CAAC;IAEnD,OAAO,MAAM;MACX;MACA;MACA,IAAIC,aAAa,CAACC,OAAO,EAAE;QACzBV,aAAa,CAACS,aAAa,CAACC,OAAO,CAAC;MACtC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN5D,SAAS,CAAC,MAAM;IACd,IAAI2D,aAAa,CAACC,OAAO,EAAE;MACzBD,aAAa,CAACC,OAAO,GAAGN,iBAAiB,CACvCK,aAAa,CAACC,OAAO,EACrBF,WACF,CAAC;IACH;EACF,CAAC,EAAE,CAACjD,WAAW,EAAEiD,WAAW,CAAC,CAAC;EAE9B,OAAO,IAAI;AACb;AAEAD,UAAU,CAACV,cAAc,GAAGA,cAAc;AAC1CU,UAAU,CAACP,aAAa,GAAGA,aAAa;AACxCO,UAAU,CAACH,iBAAiB,GAAGA,iBAAiB;AAChDG,UAAU,CAACD,QAAQ,GAAGA,QAAQ;AAC9BC,UAAU,CAAChB,SAAS,GAAGA,SAAS", "ignoreList": []}