{"version": 3, "names": ["_react", "require", "_reactNative", "_NativeEdgeToEdgeModule", "_interopRequireDefault", "e", "__esModule", "default", "isLightColorScheme", "colorScheme", "Appearance", "getColorScheme", "resolveSystemBarStyle", "style", "toNativeBarStyle", "mergeEntriesStack", "entriesStack", "reduce", "prev", "cur", "prop", "statusBarStyle", "undefined", "navigationBarStyle", "statusBarHidden", "navigationBarHidden", "resolveProps", "hidden", "compactStyle", "compactHidden", "statusBar", "navigationBar", "createStackEntry", "props", "updateImmediate", "currentV<PERSON>ues", "setStatusBarStyle", "nativeStyle", "Platform", "OS", "NativeModule", "StatusBar", "setBarStyle", "setNavigationBarStyle", "setStatusBarHidden", "setHidden", "setNavigationBarHidden", "updateEntriesStack", "clearImmediate", "setImmediate", "mergedEntries", "pushStackEntry", "entry", "push", "popStackEntry", "index", "indexOf", "splice", "replaceStackEntry", "newEntry", "setStyle", "SystemBars", "stableProps", "useMemo", "useColorScheme", "stackEntryRef", "useRef", "useEffect", "current"], "sourceRoot": "../../src", "sources": ["SystemBars.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,uBAAA,GAAAC,sBAAA,CAAAH,OAAA;AAA0D,SAAAG,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAK1D,SAASG,kBAAkBA,CAAA,EAAG;EAC5B,MAAMC,WAAW,GAAGC,uBAAU,EAAEC,cAAc,CAAC,CAAC,IAAI,OAAO;EAC3D,OAAOF,WAAW,KAAK,OAAO;AAChC;AAEA,SAASG,qBAAqBA,CAC5BC,KAAiC,EACf;EAClB,QAAQA,KAAK;IACX,KAAK,MAAM;MACT,OAAOL,kBAAkB,CAAC,CAAC,GAAG,MAAM,GAAG,OAAO;IAChD,KAAK,UAAU;MACb,OAAOA,kBAAkB,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM;IAChD;MACE,OAAOK,KAAK;EAChB;AACF;AAEA,SAASC,gBAAgBA,CACvBD,KAAuB,EACuB;EAC9C,OAAOA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,MAAM,GAAG,GAAGA,KAAK,UAAU,GAAG,SAAS;AAC/E;;AAEA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACC,YAA+B,EAAE;EAC1D,OAAOA,YAAY,CAACC,MAAM,CAMxB,CAACC,IAAI,EAAEC,GAAG,KAAK;IACb,KAAK,MAAMC,IAAI,IAAID,GAAG,EAAE;MACtB,IAAIA,GAAG,CAACC,IAAI,CAA0B,IAAI,IAAI,EAAE;QAC9C;QACAF,IAAI,CAACE,IAAI,CAAC,GAAGD,GAAG,CAACC,IAAI,CAAC;MACxB;IACF;IACA,OAAOF,IAAI;EACb,CAAC,EACD;IACEG,cAAc,EAAEC,SAAS;IACzBC,kBAAkB,EAAED,SAAS;IAC7BE,eAAe,EAAE,KAAK;IACtBC,mBAAmB,EAAE;EACvB,CACF,CAAC;AACH;AAEA,SAASC,YAAYA,CAAC;EAAEC,MAAM;EAAEd;AAAuB,CAAC,EAAE;EACxD,MAAMe,YAAY,GAAG,OAAOf,KAAK,KAAK,QAAQ;EAC9C,MAAMgB,aAAa,GAAG,OAAOF,MAAM,KAAK,SAAS;EAEjD,OAAO;IACLN,cAAc,EAAEO,YAAY,GAAGf,KAAK,GAAGA,KAAK,EAAEiB,SAAS;IACvDP,kBAAkB,EAAEK,YAAY,GAAGf,KAAK,GAAGA,KAAK,EAAEkB,aAAa;IAC/DP,eAAe,EAAEK,aAAa,GAAGF,MAAM,GAAGA,MAAM,EAAEG,SAAS;IAC3DL,mBAAmB,EAAEI,aAAa,GAAGF,MAAM,GAAGA,MAAM,EAAEI;EACxD,CAAC;AACH;;AAEA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAsB,EAAmB;EACjE,OAAOP,YAAY,CAACO,KAAK,CAAC;AAC5B;AAEA,MAAMjB,YAA+B,GAAG,EAAE;;AAE1C;AACA,IAAIkB,eAAwC,GAAG,IAAI;;AAEnD;AACA,MAAMC,aAKL,GAAG;EACFd,cAAc,EAAEC,SAAS;EACzBC,kBAAkB,EAAED,SAAS;EAC7BE,eAAe,EAAE,KAAK;EACtBC,mBAAmB,EAAE;AACvB,CAAC;AAED,SAASW,iBAAiBA,CAACvB,KAAuB,EAAE;EAClD,IAAIA,KAAK,KAAKsB,aAAa,CAACd,cAAc,EAAE;IAC1Cc,aAAa,CAACd,cAAc,GAAGR,KAAK;IAEpC,MAAMwB,WAAW,GAAGvB,gBAAgB,CAACD,KAAK,CAAC;IAE3C,IAAIyB,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAC7BC,+BAAY,EAAEJ,iBAAiB,CAACC,WAAW,CAAC;IAC9C,CAAC,MAAM,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MAChCE,sBAAS,CAACC,WAAW,CAACL,WAAW,EAAE,IAAI,CAAC;IAC1C;EACF;AACF;AAEA,SAASM,qBAAqBA,CAAC9B,KAAuB,EAAE;EACtD,IAAIA,KAAK,KAAKsB,aAAa,CAACZ,kBAAkB,EAAE;IAC9CY,aAAa,CAACZ,kBAAkB,GAAGV,KAAK;IAExC,IAAIyB,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAC7B,MAAMF,WAAW,GAAGvB,gBAAgB,CAACD,KAAK,CAAC;MAC3C2B,+BAAY,EAAEG,qBAAqB,CAACN,WAAW,CAAC;IAClD;EACF;AACF;AAEA,SAASO,kBAAkBA,CAACjB,MAAe,EAAE;EAC3C,IAAIA,MAAM,KAAKQ,aAAa,CAACX,eAAe,EAAE;IAC5CW,aAAa,CAACX,eAAe,GAAGG,MAAM;IAEtC,IAAIW,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAC7BC,+BAAY,EAAEI,kBAAkB,CAACjB,MAAM,CAAC;IAC1C,CAAC,MAAM,IAAIW,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MAChCE,sBAAS,CAACI,SAAS,CAAClB,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IACvC;EACF;AACF;AAEA,SAASmB,sBAAsBA,CAACnB,MAAe,EAAE;EAC/C,IAAIA,MAAM,KAAKQ,aAAa,CAACV,mBAAmB,EAAE;IAChDU,aAAa,CAACV,mBAAmB,GAAGE,MAAM;IAE1C,IAAIW,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAC7BC,+BAAY,EAAEM,sBAAsB,CAACnB,MAAM,CAAC;IAC9C;EACF;AACF;;AAEA;AACA;AACA;AACA,SAASoB,kBAAkBA,CAAA,EAAG;EAC5B,IAAIT,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAID,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACtD,IAAIL,eAAe,IAAI,IAAI,EAAE;MAC3Bc,cAAc,CAACd,eAAe,CAAC;IACjC;IAEAA,eAAe,GAAGe,YAAY,CAAC,MAAM;MACnC,MAAMC,aAAa,GAAGnC,iBAAiB,CAACC,YAAY,CAAC;MACrD,MAAM;QAAEQ,eAAe;QAAEC;MAAoB,CAAC,GAAGyB,aAAa;MAE9D,MAAM7B,cAAc,GAAGT,qBAAqB,CAC1CsC,aAAa,CAAC7B,cAChB,CAAC;MACD,MAAME,kBAAkB,GAAGX,qBAAqB,CAC9CsC,aAAa,CAAC3B,kBAChB,CAAC;MAEDa,iBAAiB,CAACf,cAAc,CAAC;MACjCsB,qBAAqB,CAACpB,kBAAkB,CAAC;MACzCqB,kBAAkB,CAACpB,eAAe,CAAC;MACnCsB,sBAAsB,CAACrB,mBAAmB,CAAC;IAC7C,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0B,cAAcA,CAAClB,KAAsB,EAAmB;EAC/D,MAAMmB,KAAK,GAAGpB,gBAAgB,CAACC,KAAK,CAAC;EACrCjB,YAAY,CAACqC,IAAI,CAACD,KAAK,CAAC;EACxBL,kBAAkB,CAAC,CAAC;EACpB,OAAOK,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACF,KAAsB,EAAQ;EACnD,MAAMG,KAAK,GAAGvC,YAAY,CAACwC,OAAO,CAACJ,KAAK,CAAC;EACzC,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;IAChBvC,YAAY,CAACyC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EAC/B;EACAR,kBAAkB,CAAC,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,iBAAiBA,CACxBN,KAAsB,EACtBnB,KAAsB,EACL;EACjB,MAAM0B,QAAQ,GAAG3B,gBAAgB,CAACC,KAAK,CAAC;EACxC,MAAMsB,KAAK,GAAGvC,YAAY,CAACwC,OAAO,CAACJ,KAAK,CAAC;EACzC,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;IAChBvC,YAAY,CAACuC,KAAK,CAAC,GAAGI,QAAQ;EAChC;EACAZ,kBAAkB,CAAC,CAAC;EACpB,OAAOY,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAC/C,KAA+B,EAAE;EACjD,MAAMoB,KAAK,GAAGP,YAAY,CAAC;IAAEb;EAAM,CAAC,CAAC;EAErC,MAAMQ,cAAc,GAAGT,qBAAqB,CAACqB,KAAK,CAACZ,cAAc,CAAC;EAClE,MAAME,kBAAkB,GAAGX,qBAAqB,CAACqB,KAAK,CAACV,kBAAkB,CAAC;EAE1E,IAAI,OAAOF,cAAc,KAAK,QAAQ,EAAE;IACtCe,iBAAiB,CAACf,cAAc,CAAC;EACnC;EACA,IAAI,OAAOE,kBAAkB,KAAK,QAAQ,EAAE;IAC1CoB,qBAAqB,CAACpB,kBAAkB,CAAC;EAC3C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASsB,SAASA,CAAClB,MAAiC,EAAE;EACpD,MAAM;IAAEH,eAAe;IAAEC;EAAoB,CAAC,GAAGC,YAAY,CAAC;IAAEC;EAAO,CAAC,CAAC;EAEzE,IAAI,OAAOH,eAAe,KAAK,SAAS,EAAE;IACxCoB,kBAAkB,CAACpB,eAAe,CAAC;EACrC;EACA,IAAI,OAAOC,mBAAmB,KAAK,SAAS,EAAE;IAC5CqB,sBAAsB,CAACrB,mBAAmB,CAAC;EAC7C;AACF;AAEO,SAASoC,UAAUA,CAAC5B,KAAsB,EAAE;EACjD,MAAM;IACJZ,cAAc;IACdE,kBAAkB;IAClBC,eAAe;IACfC;EACF,CAAC,GAAGC,YAAY,CAACO,KAAK,CAAC;EAEvB,MAAM6B,WAAW,GAAG,IAAAC,cAAO,EACzB,OAAO;IACLlD,KAAK,EACHQ,cAAc,KAAKE,kBAAkB,GACjCF,cAAc,GACd;MAAES,SAAS,EAAET,cAAc;MAAEU,aAAa,EAAER;IAAmB,CAAC;IACtEI,MAAM,EACJH,eAAe,KAAKC,mBAAmB,GACnCD,eAAe,GACf;MAAEM,SAAS,EAAEN,eAAe;MAAEO,aAAa,EAAEN;IAAoB;EACzE,CAAC,CAAC,EACF,CAACJ,cAAc,EAAEE,kBAAkB,EAAEC,eAAe,EAAEC,mBAAmB,CAC3E,CAAC;EAED,MAAMhB,WAAW,GAAG,IAAAuD,2BAAc,EAAC,CAAC;EACpC,MAAMC,aAAa,GAAG,IAAAC,aAAM,EAAyB,IAAI,CAAC;EAE1D,IAAAC,gBAAS,EAAC,MAAM;IACd;IACA;IACA;IACA;IACAF,aAAa,CAACG,OAAO,GAAGjB,cAAc,CAACW,WAAW,CAAC;IAEnD,OAAO,MAAM;MACX;MACA;MACA,IAAIG,aAAa,CAACG,OAAO,EAAE;QACzBd,aAAa,CAACW,aAAa,CAACG,OAAO,CAAC;MACtC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAAD,gBAAS,EAAC,MAAM;IACd,IAAIF,aAAa,CAACG,OAAO,EAAE;MACzBH,aAAa,CAACG,OAAO,GAAGV,iBAAiB,CACvCO,aAAa,CAACG,OAAO,EACrBN,WACF,CAAC;IACH;EACF,CAAC,EAAE,CAACrD,WAAW,EAAEqD,WAAW,CAAC,CAAC;EAE9B,OAAO,IAAI;AACb;AAEAD,UAAU,CAACV,cAAc,GAAGA,cAAc;AAC1CU,UAAU,CAACP,aAAa,GAAGA,aAAa;AACxCO,UAAU,CAACH,iBAAiB,GAAGA,iBAAiB;AAChDG,UAAU,CAACD,QAAQ,GAAGA,QAAQ;AAC9BC,UAAU,CAAChB,SAAS,GAAGA,SAAS", "ignoreList": []}