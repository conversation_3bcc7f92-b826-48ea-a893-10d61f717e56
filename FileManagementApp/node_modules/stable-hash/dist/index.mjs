const o=new WeakMap;let u=0;export function stableHash(t){const i=typeof t,s=t&&t.constructor,c=s==Date;if(Object(t)===t&&!c&&s!=RegExp){let e=o.get(t);if(e)return e;e=++u+"~",o.set(t,e);let n;if(s==Array){for(e="@",n=0;n<t.length;n++)e+=stableHash(t[n])+",";o.set(t,e)}else if(s==Object){e="#";const f=Object.keys(t).sort();for(;(n=f.pop())!==void 0;)t[n]!==void 0&&(e+=n+":"+stableHash(t[n])+",");o.set(t,e)}return e}return c?t.toJSON():i=="symbol"?t.toString():i=="string"?JSON.stringify(t):""+t}export default stableHash;
