{"version": 3, "file": "Config.types.js", "names": ["_configTypes", "data", "require", "ProjectPrivacy", "exports"], "sources": ["../src/Config.types.ts"], "sourcesContent": ["import { ModConfig } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\n\nexport { ExpoConfig };\n\nexport type PackageJSONConfig = { dependencies?: Record<string, string>; [key: string]: any };\n\nexport interface ProjectConfig {\n  /**\n   * Fully evaluated Expo config with default values injected.\n   */\n  exp: ExpoConfig;\n  /**\n   * Dynamic config for processing native files during the generation process.\n   */\n  mods?: ModConfig | null;\n  /**\n   * Project package.json object with default values injected.\n   */\n  pkg: PackageJSONConfig;\n  /**\n   * Unaltered static config (app.config.json, app.json, or custom json config).\n   * For legacy, an empty object will be returned even if no static config exists.\n   */\n  rootConfig: AppJSONConfig;\n  /**\n   * Path to the static json config file if it exists.\n   * If a project has an app.config.js and an app.json then app.json will be returned.\n   * If a project has an app.config.json and an app.json then app.config.json will be returned.\n   * Returns null if no static config file exists.\n   */\n  staticConfigPath: string | null;\n  /**\n   * Path to an app.config.js or app.config.ts.\n   * Returns null if no dynamic config file exists.\n   */\n  dynamicConfigPath: string | null;\n\n  /**\n   * Returns the type of the value exported from the dynamic config.\n   * This can be used to determine if the dynamic config is potentially extending a static config when (v === 'function').\n   * Returns null if no dynamic config file exists.\n   */\n  dynamicConfigObjectType: string | null;\n  /**\n   * Returns true if both a static and dynamic config are present, and the dynamic config is applied on top of the static.\n   * This is only used for expo-doctor diagnostic warnings. This flag may be true even in cases where all static config values are used.\n   * It only checks against a typical pattern for layering static and dynamic config, e.g.,:\n   * module.exports = ({ config }) => {\n      return {\n        ...config,\n        name: 'name overridden by dynamic config',\n      };\n    };\n   */\n  hasUnusedStaticConfig: boolean;\n}\nexport type AppJSONConfig = { expo: ExpoConfig; [key: string]: any };\nexport type BareAppConfig = { name: string; [key: string]: any };\nexport type HookArguments = {\n  config: any;\n  url: any;\n  exp: ExpoConfig;\n  iosBundle: string | Uint8Array;\n  iosSourceMap: string | null;\n  iosManifest: any;\n  iosManifestUrl: string;\n  androidBundle: string | Uint8Array;\n  androidSourceMap: string | null;\n  androidManifest: any;\n  androidManifestUrl: string;\n  projectRoot: string;\n  log: (msg: any) => void;\n};\n\nexport type ExpoGoConfig = {\n  mainModuleName: string;\n  // A string that flipper checks to determine if Metro bundler is running\n  // by adding it to the manifest, we can trick Flipper into working properly.\n  // https://github.com/facebook/flipper/blob/9ca8bee208b7bfe2b8c0dab8eb4b79688a0c84bc/desktop/app/src/dispatcher/metroDevice.tsx#L37\n  __flipperHack: 'React Native packager is running';\n  debuggerHost: string;\n  developer: {\n    tool: string | null;\n    projectRoot?: string;\n  };\n  packagerOpts: {\n    [key: string]: any;\n  };\n};\n\nexport type EASConfig = {\n  projectId?: string;\n};\n\nexport type ClientScopingConfig = {\n  scopeKey?: string;\n};\n\nexport interface ExpoUpdatesManifestAsset {\n  url: string;\n  key: string;\n  contentType: string;\n  hash?: string;\n}\n\nexport interface ExpoUpdatesManifest {\n  id: string;\n  createdAt: string;\n  runtimeVersion: string;\n  launchAsset: ExpoUpdatesManifestAsset;\n  assets: ExpoUpdatesManifestAsset[];\n  metadata: { [key: string]: string };\n  extra: ClientScopingConfig & {\n    expoClient?: ExpoConfig & {\n      /**\n       * Only present during development using @expo/cli.\n       */\n      hostUri?: string;\n    };\n    expoGo?: ExpoGoConfig;\n    eas?: EASConfig;\n  };\n}\n\nexport type Hook = {\n  file: string;\n  config: any;\n};\n\nexport type HookType = 'postPublish' | 'postExport';\n\nexport enum ProjectPrivacy {\n  PUBLIC = 'public',\n  UNLISTED = 'unlisted',\n}\n\nexport type Platform = 'android' | 'ios' | 'web';\nexport type ProjectTarget = 'managed' | 'bare';\n\nexport type ConfigErrorCode =\n  | 'NO_APP_JSON'\n  | 'NOT_OBJECT'\n  | 'NO_EXPO'\n  | 'MODULE_NOT_FOUND'\n  | 'DEPRECATED'\n  | 'INVALID_MODE'\n  | 'INVALID_FORMAT'\n  | 'INVALID_PLUGIN'\n  | 'INVALID_CONFIG'\n  | 'ENTRY_NOT_FOUND';\n\nexport type ConfigContext = {\n  projectRoot: string;\n  /**\n   * The static config path either app.json, app.config.json, or a custom user-defined config.\n   */\n  staticConfigPath: string | null;\n  packageJsonPath: string | null;\n  config: Partial<ExpoConfig>;\n};\n\nexport type GetConfigOptions = {\n  isPublicConfig?: boolean;\n  /**\n   * Should the config `mods` be preserved in the config? Used for compiling mods in the eject command.\n   *\n   * @default false\n   */\n  isModdedConfig?: boolean;\n  skipSDKVersionRequirement?: boolean;\n  /**\n   * Dangerously skip resolving plugins.\n   */\n  skipPlugins?: boolean;\n  strict?: boolean;\n};\n\nexport type WriteConfigOptions = { dryRun?: boolean };\n\nexport type ConfigFilePaths = { staticConfigPath: string | null; dynamicConfigPath: string | null };\n"], "mappings": ";;;;;;;;;;;;AACA,SAAAA,aAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,YAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAgD,IAmIpCE,cAAc,GAAAC,OAAA,CAAAD,cAAA,0BAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAA,OAAdA,cAAc;AAAA", "ignoreList": []}