{"version": 3, "file": "env.js", "names": ["_getenv", "data", "require", "Env", "EXPO_NO_METRO_WORKSPACE_ROOT", "string", "console", "warn", "boolish", "env", "exports"], "sources": ["../../src/paths/env.ts"], "sourcesContent": ["import { boolish, string } from 'getenv';\n\nclass Env {\n  /** Disable auto server root detection for Metro. This will not change the server root to the workspace root. */\n  get EXPO_NO_METRO_WORKSPACE_ROOT(): boolean {\n    if (string('EXPO_USE_METRO_WORKSPACE_ROOT', '')) {\n      console.warn(\n        'EXPO_USE_METRO_WORKSPACE_ROOT is enabled by default, use EXPO_NO_METRO_WORKSPACE_ROOT instead to disable.'\n      );\n    }\n\n    return boolish('EXPO_NO_METRO_WORKSPACE_ROOT', false);\n  }\n}\n\nexport const env = new Env();\n"], "mappings": ";;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAME,GAAG,CAAC;EACR;EACA,IAAIC,4BAA4BA,CAAA,EAAY;IAC1C,IAAI,IAAAC,gBAAM,EAAC,+BAA+B,EAAE,EAAE,CAAC,EAAE;MAC/CC,OAAO,CAACC,IAAI,CACV,2GACF,CAAC;IACH;IAEA,OAAO,IAAAC,iBAAO,EAAC,8BAA8B,EAAE,KAAK,CAAC;EACvD;AACF;AAEO,MAAMC,GAAG,GAAAC,OAAA,CAAAD,GAAA,GAAG,IAAIN,GAAG,CAAC,CAAC", "ignoreList": []}