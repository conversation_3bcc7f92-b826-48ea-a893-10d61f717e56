{"version": 3, "file": "mod-compiler.js", "names": ["_debug", "data", "_interopRequireDefault", "require", "_path", "_createBaseMod", "_withAndroidBaseMods", "_withIosBaseMods", "_Xcodeproj", "_errors", "Warnings", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "debug", "Debug", "withDefaultBaseMods", "config", "props", "withIosBaseMods", "withAndroidBaseMods", "withIntrospectionBaseMods", "saveToInternal", "skipEmptyMod", "mods", "platform", "keys", "key", "isIntrospective", "compileModsAsync", "introspect", "evalModsAsync", "sortMods", "commands", "precedences", "seen", "Set", "dedupedCommands", "filter", "duplicate", "add", "sort", "keyA", "keyB", "precedenceA", "precedenceB", "getRawClone", "freeze", "JSON", "parse", "stringify", "ios", "dangerous", "xcodeproj", "finalized", "projectRoot", "platforms", "assertMissingModProviders", "ignoreExistingNativeFiles", "modRawConfig", "platformName", "entries", "includes", "length", "map", "name", "join", "platformProjectRoot", "path", "projectName", "getHackyProjectName", "undefined", "modName", "mod", "modRequest", "is<PERSON><PERSON><PERSON>", "errorMessage", "PluginError", "addWarningForPlatform", "results", "modResults", "assertModResults"], "sources": ["../../src/plugins/mod-compiler.ts"], "sourcesContent": ["import Debug from 'debug';\nimport path from 'path';\n\nimport { assertModResults, ForwardedBaseModOptions } from './createBaseMod';\nimport { withAndroidBaseMods } from './withAndroidBaseMods';\nimport { withIosBaseMods } from './withIosBaseMods';\nimport { ExportedConfig, Mod, ModConfig, ModPlatform } from '../Plugin.types';\nimport { getHackyProjectName } from '../ios/utils/Xcodeproj';\nimport { PluginError } from '../utils/errors';\nimport * as Warnings from '../utils/warnings';\n\nconst debug = Debug('expo:config-plugins:mod-compiler');\n\nexport function withDefaultBaseMods(\n  config: ExportedConfig,\n  props: ForwardedBaseModOptions = {}\n): ExportedConfig {\n  config = withIosBaseMods(config, props);\n  config = withAndroidBaseMods(config, props);\n  return config;\n}\n\n/**\n * Get a prebuild config that safely evaluates mods without persisting any changes to the file system.\n * Currently this only supports infoPlist, entitlements, androidManifest, strings, gradleProperties, and expoPlist mods.\n * This plugin should be evaluated directly:\n */\nexport function withIntrospectionBaseMods(\n  config: ExportedConfig,\n  props: ForwardedBaseModOptions = {}\n): ExportedConfig {\n  config = withIosBaseMods(config, {\n    saveToInternal: true,\n    // This writing optimization can be skipped since we never write in introspection mode.\n    // Including empty mods will ensure that all mods get introspected.\n    skipEmptyMod: false,\n    ...props,\n  });\n  config = withAndroidBaseMods(config, {\n    saveToInternal: true,\n    skipEmptyMod: false,\n    ...props,\n  });\n\n  if (config.mods) {\n    // Remove all mods that don't have an introspection base mod, for instance `dangerous` mods.\n    for (const platform of Object.keys(config.mods) as ModPlatform[]) {\n      // const platformPreserve = preserve[platform];\n      for (const key of Object.keys(config.mods[platform] || {})) {\n        // @ts-ignore\n        if (!config.mods[platform]?.[key]?.isIntrospective) {\n          debug(`removing non-idempotent mod: ${platform}.${key}`);\n          // @ts-ignore\n          delete config.mods[platform]?.[key];\n        }\n      }\n    }\n  }\n\n  return config;\n}\n\n/**\n *\n * @param projectRoot\n * @param config\n */\nexport async function compileModsAsync(\n  config: ExportedConfig,\n  props: {\n    projectRoot: string;\n    platforms?: ModPlatform[];\n    introspect?: boolean;\n    assertMissingModProviders?: boolean;\n    ignoreExistingNativeFiles?: boolean;\n  }\n): Promise<ExportedConfig> {\n  if (props.introspect === true) {\n    config = withIntrospectionBaseMods(config);\n  } else {\n    config = withDefaultBaseMods(config);\n  }\n  return await evalModsAsync(config, props);\n}\n\nexport function sortMods(\n  commands: [string, any][],\n  precedences: Record<string, number>\n): [string, any][] {\n  const seen = new Set();\n  const dedupedCommands = commands.filter(([key]) => {\n    const duplicate = seen.has(key);\n    seen.add(key);\n    return !duplicate;\n  });\n\n  return dedupedCommands.sort(([keyA], [keyB]) => {\n    const precedenceA = precedences[keyA] || 0;\n    const precedenceB = precedences[keyB] || 0;\n    return precedenceA - precedenceB;\n  });\n}\n\nfunction getRawClone({ mods, ...config }: ExportedConfig) {\n  // Configs should be fully serializable, so we can clone them without worrying about\n  // the mods.\n  return Object.freeze(JSON.parse(JSON.stringify(config)));\n}\n\nconst precedences: Record<string, Record<string, number>> = {\n  ios: {\n    // dangerous runs first\n    dangerous: -2,\n    // run the XcodeProject mod second because many plugins attempt to read from it.\n    xcodeproj: -1,\n    // put the finalized mod at the last\n    finalized: 1,\n  },\n};\n/**\n * A generic plugin compiler.\n *\n * @param config\n */\nexport async function evalModsAsync(\n  config: ExportedConfig,\n  {\n    projectRoot,\n    introspect,\n    platforms,\n    assertMissingModProviders,\n    ignoreExistingNativeFiles = false,\n  }: {\n    projectRoot: string;\n    introspect?: boolean;\n    platforms?: ModPlatform[];\n    /**\n     * Throw errors when mods are missing providers.\n     * @default true\n     */\n    assertMissingModProviders?: boolean;\n    /** Ignore any existing native files, only use the generated prebuild results. */\n    ignoreExistingNativeFiles?: boolean;\n  }\n): Promise<ExportedConfig> {\n  const modRawConfig = getRawClone(config);\n  for (const [platformName, platform] of Object.entries(config.mods ?? ({} as ModConfig))) {\n    if (platforms && !platforms.includes(platformName as any)) {\n      debug(`skip platform: ${platformName}`);\n      continue;\n    }\n\n    let entries = Object.entries(platform);\n    if (entries.length) {\n      // Move dangerous item to the first position and finalized item to the last position if it exists.\n      // This ensures that all dangerous code runs first and finalized applies last.\n      entries = sortMods(entries, precedences[platformName] ?? { dangerous: -1, finalized: 1 });\n      debug(`run in order: ${entries.map(([name]) => name).join(', ')}`);\n      const platformProjectRoot = path.join(projectRoot, platformName);\n      const projectName =\n        platformName === 'ios' ? getHackyProjectName(projectRoot, config) : undefined;\n\n      for (const [modName, mod] of entries) {\n        const modRequest = {\n          projectRoot,\n          projectName,\n          platformProjectRoot,\n          platform: platformName as ModPlatform,\n          modName,\n          introspect: !!introspect,\n          ignoreExistingNativeFiles,\n        };\n\n        if (!(mod as Mod).isProvider) {\n          // In strict mode, throw an error.\n          const errorMessage = `Initial base modifier for \"${platformName}.${modName}\" is not a provider and therefore will not provide modResults to child mods`;\n          if (assertMissingModProviders !== false) {\n            throw new PluginError(errorMessage, 'MISSING_PROVIDER');\n          } else {\n            Warnings.addWarningForPlatform(\n              platformName as ModPlatform,\n              `${platformName}.${modName}`,\n              `Skipping: Initial base modifier for \"${platformName}.${modName}\" is not a provider and therefore will not provide modResults to child mods. This may be due to an outdated version of Expo CLI.`\n            );\n            // In loose mode, just skip the mod entirely.\n            continue;\n          }\n        }\n\n        const results = await (mod as Mod)({\n          ...config,\n          modResults: null,\n          modRequest,\n          modRawConfig,\n        });\n\n        // Sanity check to help locate non compliant mods.\n        config = assertModResults(results, platformName, modName);\n        // @ts-ignore: `modResults` is added for modifications\n        delete config.modResults;\n        // @ts-ignore: `modRequest` is added for modifications\n        delete config.modRequest;\n        // @ts-ignore: `modRawConfig` is added for modifications\n        delete config.modRawConfig;\n      }\n    }\n  }\n\n  return config;\n}\n"], "mappings": ";;;;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,eAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,cAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,qBAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,oBAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,iBAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,gBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAO,WAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,UAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,QAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,OAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,SAAA;EAAA,MAAAT,IAAA,GAAAU,uBAAA,CAAAR,OAAA;EAAAO,QAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA8C,SAAAW,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAnB,uBAAAW,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAE9C,MAAMmB,KAAK,GAAG,IAAAC,gBAAK,EAAC,kCAAkC,CAAC;AAEhD,SAASC,mBAAmBA,CACjCC,MAAsB,EACtBC,KAA8B,GAAG,CAAC,CAAC,EACnB;EAChBD,MAAM,GAAG,IAAAE,kCAAe,EAACF,MAAM,EAAEC,KAAK,CAAC;EACvCD,MAAM,GAAG,IAAAG,0CAAmB,EAACH,MAAM,EAAEC,KAAK,CAAC;EAC3C,OAAOD,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASI,yBAAyBA,CACvCJ,MAAsB,EACtBC,KAA8B,GAAG,CAAC,CAAC,EACnB;EAChBD,MAAM,GAAG,IAAAE,kCAAe,EAACF,MAAM,EAAE;IAC/BK,cAAc,EAAE,IAAI;IACpB;IACA;IACAC,YAAY,EAAE,KAAK;IACnB,GAAGL;EACL,CAAC,CAAC;EACFD,MAAM,GAAG,IAAAG,0CAAmB,EAACH,MAAM,EAAE;IACnCK,cAAc,EAAE,IAAI;IACpBC,YAAY,EAAE,KAAK;IACnB,GAAGL;EACL,CAAC,CAAC;EAEF,IAAID,MAAM,CAACO,IAAI,EAAE;IACf;IACA,KAAK,MAAMC,QAAQ,IAAInB,MAAM,CAACoB,IAAI,CAACT,MAAM,CAACO,IAAI,CAAC,EAAmB;MAChE;MACA,KAAK,MAAMG,GAAG,IAAIrB,MAAM,CAACoB,IAAI,CAACT,MAAM,CAACO,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QAC1D;QACA,IAAI,CAACR,MAAM,CAACO,IAAI,CAACC,QAAQ,CAAC,GAAGE,GAAG,CAAC,EAAEC,eAAe,EAAE;UAClDd,KAAK,CAAC,gCAAgCW,QAAQ,IAAIE,GAAG,EAAE,CAAC;UACxD;UACA,OAAOV,MAAM,CAACO,IAAI,CAACC,QAAQ,CAAC,GAAGE,GAAG,CAAC;QACrC;MACF;IACF;EACF;EAEA,OAAOV,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACO,eAAeY,gBAAgBA,CACpCZ,MAAsB,EACtBC,KAMC,EACwB;EACzB,IAAIA,KAAK,CAACY,UAAU,KAAK,IAAI,EAAE;IAC7Bb,MAAM,GAAGI,yBAAyB,CAACJ,MAAM,CAAC;EAC5C,CAAC,MAAM;IACLA,MAAM,GAAGD,mBAAmB,CAACC,MAAM,CAAC;EACtC;EACA,OAAO,MAAMc,aAAa,CAACd,MAAM,EAAEC,KAAK,CAAC;AAC3C;AAEO,SAASc,QAAQA,CACtBC,QAAyB,EACzBC,WAAmC,EAClB;EACjB,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtB,MAAMC,eAAe,GAAGJ,QAAQ,CAACK,MAAM,CAAC,CAAC,CAACX,GAAG,CAAC,KAAK;IACjD,MAAMY,SAAS,GAAGJ,IAAI,CAAClC,GAAG,CAAC0B,GAAG,CAAC;IAC/BQ,IAAI,CAACK,GAAG,CAACb,GAAG,CAAC;IACb,OAAO,CAACY,SAAS;EACnB,CAAC,CAAC;EAEF,OAAOF,eAAe,CAACI,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAACC,IAAI,CAAC,KAAK;IAC9C,MAAMC,WAAW,GAAGV,WAAW,CAACQ,IAAI,CAAC,IAAI,CAAC;IAC1C,MAAMG,WAAW,GAAGX,WAAW,CAACS,IAAI,CAAC,IAAI,CAAC;IAC1C,OAAOC,WAAW,GAAGC,WAAW;EAClC,CAAC,CAAC;AACJ;AAEA,SAASC,WAAWA,CAAC;EAAEtB,IAAI;EAAE,GAAGP;AAAuB,CAAC,EAAE;EACxD;EACA;EACA,OAAOX,MAAM,CAACyC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACjC,MAAM,CAAC,CAAC,CAAC;AAC1D;AAEA,MAAMiB,WAAmD,GAAG;EAC1DiB,GAAG,EAAE;IACH;IACAC,SAAS,EAAE,CAAC,CAAC;IACb;IACAC,SAAS,EAAE,CAAC,CAAC;IACb;IACAC,SAAS,EAAE;EACb;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,eAAevB,aAAaA,CACjCd,MAAsB,EACtB;EACEsC,WAAW;EACXzB,UAAU;EACV0B,SAAS;EACTC,yBAAyB;EACzBC,yBAAyB,GAAG;AAY9B,CAAC,EACwB;EACzB,MAAMC,YAAY,GAAGb,WAAW,CAAC7B,MAAM,CAAC;EACxC,KAAK,MAAM,CAAC2C,YAAY,EAAEnC,QAAQ,CAAC,IAAInB,MAAM,CAACuD,OAAO,CAAC5C,MAAM,CAACO,IAAI,IAAK,CAAC,CAAe,CAAC,EAAE;IACvF,IAAIgC,SAAS,IAAI,CAACA,SAAS,CAACM,QAAQ,CAACF,YAAmB,CAAC,EAAE;MACzD9C,KAAK,CAAC,kBAAkB8C,YAAY,EAAE,CAAC;MACvC;IACF;IAEA,IAAIC,OAAO,GAAGvD,MAAM,CAACuD,OAAO,CAACpC,QAAQ,CAAC;IACtC,IAAIoC,OAAO,CAACE,MAAM,EAAE;MAClB;MACA;MACAF,OAAO,GAAG7B,QAAQ,CAAC6B,OAAO,EAAE3B,WAAW,CAAC0B,YAAY,CAAC,IAAI;QAAER,SAAS,EAAE,CAAC,CAAC;QAAEE,SAAS,EAAE;MAAE,CAAC,CAAC;MACzFxC,KAAK,CAAC,iBAAiB+C,OAAO,CAACG,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,KAAKA,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAClE,MAAMC,mBAAmB,GAAGC,eAAI,CAACF,IAAI,CAACX,WAAW,EAAEK,YAAY,CAAC;MAChE,MAAMS,WAAW,GACfT,YAAY,KAAK,KAAK,GAAG,IAAAU,gCAAmB,EAACf,WAAW,EAAEtC,MAAM,CAAC,GAAGsD,SAAS;MAE/E,KAAK,MAAM,CAACC,OAAO,EAAEC,GAAG,CAAC,IAAIZ,OAAO,EAAE;QACpC,MAAMa,UAAU,GAAG;UACjBnB,WAAW;UACXc,WAAW;UACXF,mBAAmB;UACnB1C,QAAQ,EAAEmC,YAA2B;UACrCY,OAAO;UACP1C,UAAU,EAAE,CAAC,CAACA,UAAU;UACxB4B;QACF,CAAC;QAED,IAAI,CAAEe,GAAG,CAASE,UAAU,EAAE;UAC5B;UACA,MAAMC,YAAY,GAAG,8BAA8BhB,YAAY,IAAIY,OAAO,6EAA6E;UACvJ,IAAIf,yBAAyB,KAAK,KAAK,EAAE;YACvC,MAAM,KAAIoB,qBAAW,EAACD,YAAY,EAAE,kBAAkB,CAAC;UACzD,CAAC,MAAM;YACLpF,QAAQ,CAAD,CAAC,CAACsF,qBAAqB,CAC5BlB,YAAY,EACZ,GAAGA,YAAY,IAAIY,OAAO,EAAE,EAC5B,wCAAwCZ,YAAY,IAAIY,OAAO,kIACjE,CAAC;YACD;YACA;UACF;QACF;QAEA,MAAMO,OAAO,GAAG,MAAON,GAAG,CAAS;UACjC,GAAGxD,MAAM;UACT+D,UAAU,EAAE,IAAI;UAChBN,UAAU;UACVf;QACF,CAAC,CAAC;;QAEF;QACA1C,MAAM,GAAG,IAAAgE,iCAAgB,EAACF,OAAO,EAAEnB,YAAY,EAAEY,OAAO,CAAC;QACzD;QACA,OAAOvD,MAAM,CAAC+D,UAAU;QACxB;QACA,OAAO/D,MAAM,CAACyD,UAAU;QACxB;QACA,OAAOzD,MAAM,CAAC0C,YAAY;MAC5B;IACF;EACF;EAEA,OAAO1C,MAAM;AACf", "ignoreList": []}