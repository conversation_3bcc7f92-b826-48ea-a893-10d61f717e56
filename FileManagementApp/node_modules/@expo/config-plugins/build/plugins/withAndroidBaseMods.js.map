{"version": 3, "file": "withAndroidBaseMods.js", "names": ["_fs", "data", "require", "_path", "_interopRequireDefault", "_createBaseMod", "_android", "_XML", "_sortObject", "e", "__esModule", "default", "readFile", "writeFile", "promises", "getAndroidManifestTemplate", "config", "parseXMLAsync", "android", "package", "sortAndroidManifest", "obj", "manifest", "sortObject", "reverseSortString", "Array", "isArray", "sort", "a", "b", "$", "application", "map", "sortObjWithOrder", "defaultProviders", "dangerous", "provider", "getFilePath", "read", "filePath", "modResults", "write", "finalized", "isIntrospective", "modRequest", "platformProjectRoot", "path", "join", "Manifest", "readAndroidManifestAsync", "error", "introspect", "writeAndroidManifestAsync", "gradleProperties", "Properties", "parsePropertiesFile", "propertiesListToString", "strings", "projectRoot", "Strings", "getProjectStringsXMLPathAsync", "Resources", "readResourcesXMLAsync", "resources", "writeXMLAsync", "xml", "colors", "Colors", "getProjectColorsXMLPathAsync", "colorsNight", "kind", "styles", "Styles", "getProjectStylesXMLPathAsync", "fallback", "projectBuildGradle", "Paths", "getProjectBuildGradleFilePath", "getFileInfo", "contents", "<PERSON><PERSON><PERSON><PERSON>", "getSettingsGradleFilePath", "appBuildGradle", "getAppBuildGradleFilePath", "mainActivity", "getProjectFilePath", "mainApplication", "withAndroidBaseMods", "providers", "props", "withGeneratedBaseMods", "platform", "getAndroidModFileProviders"], "sources": ["../../src/plugins/withAndroidBaseMods.ts"], "sourcesContent": ["import { promises } from 'fs';\nimport path from 'path';\n\nimport { ForwardedBaseModOptions, provider, withGeneratedBaseMods } from './createBaseMod';\nimport { ExportedConfig, ModConfig } from '../Plugin.types';\nimport { Colors, Manifest, Paths, Properties, Resources, Strings, Styles } from '../android';\nimport { AndroidManifest } from '../android/Manifest';\nimport { parseXMLAsync, writeXMLAsync } from '../utils/XML';\nimport { reverseSortString, sortObject, sortObjWithOrder } from '../utils/sortObject';\n\nconst { readFile, writeFile } = promises;\n\ntype AndroidModName = keyof Required<ModConfig>['android'];\n\nfunction getAndroidManifestTemplate(config: ExportedConfig) {\n  // Keep in sync with https://github.com/expo/expo/blob/main/templates/expo-template-bare-minimum/android/app/src/main/AndroidManifest.xml\n  // TODO: Read from remote template when possible\n  return parseXMLAsync(`\n  <manifest xmlns:android=\"http://schemas.android.com/apk/res/android\" package=\"${\n    config.android?.package ?? 'com.placeholder.appid'\n  }\">\n\n    <uses-permission android:name=\"android.permission.INTERNET\"/>\n    <!-- OPTIONAL PERMISSIONS, REMOVE WHATEVER YOU DO NOT NEED -->\n    <uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>\n    <uses-permission android:name=\"android.permission.VIBRATE\"/>\n    <!-- These require runtime permissions on M -->\n    <uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>\n    <uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>\n    <!-- END OPTIONAL PERMISSIONS -->\n\n    <queries>\n      <!-- Support checking for http(s) links via the Linking API -->\n      <intent>\n        <action android:name=\"android.intent.action.VIEW\" />\n        <category android:name=\"android.intent.category.BROWSABLE\" />\n        <data android:scheme=\"https\" />\n      </intent>\n    </queries>\n\n    <application android:name=\".MainApplication\" android:label=\"@string/app_name\" android:icon=\"@mipmap/ic_launcher\" android:roundIcon=\"@mipmap/ic_launcher_round\" android:allowBackup=\"false\" android:theme=\"@style/AppTheme\" android:supportsRtl=\"true\">\n      <activity android:name=\".MainActivity\" android:configChanges=\"keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode\" android:launchMode=\"singleTask\" android:windowSoftInputMode=\"adjustResize\" android:theme=\"@style/Theme.App.SplashScreen\" android:exported=\"true\">\n        <intent-filter>\n          <action android:name=\"android.intent.action.MAIN\"/>\n          <category android:name=\"android.intent.category.LAUNCHER\"/>\n        </intent-filter>\n      </activity>\n    </application>\n  </manifest>\n  `) as Promise<AndroidManifest>;\n}\n\nexport function sortAndroidManifest(obj: AndroidManifest) {\n  if (obj.manifest) {\n    // Reverse sort so application is last and permissions are first\n    obj.manifest = sortObject(obj.manifest, reverseSortString);\n\n    if (Array.isArray(obj.manifest['uses-permission'])) {\n      // Sort permissions alphabetically\n      obj.manifest['uses-permission'].sort((a, b) => {\n        if (a.$['android:name'] < b.$['android:name']) return -1;\n        if (a.$['android:name'] > b.$['android:name']) return 1;\n        return 0;\n      });\n    }\n\n    if (Array.isArray(obj.manifest.application)) {\n      // reverse sort applications so activity is towards the end and meta-data is towards the front.\n      obj.manifest.application = obj.manifest.application.map((application) => {\n        application = sortObjWithOrder(application, ['meta-data', 'service', 'activity']);\n\n        if (Array.isArray(application['meta-data'])) {\n          // Sort metadata alphabetically\n          application['meta-data'].sort((a, b) => {\n            if (a.$['android:name'] < b.$['android:name']) return -1;\n            if (a.$['android:name'] > b.$['android:name']) return 1;\n            return 0;\n          });\n        }\n        return application;\n      });\n    }\n  }\n  return obj;\n}\n\nconst defaultProviders = {\n  dangerous: provider<unknown>({\n    getFilePath() {\n      return '';\n    },\n    async read() {\n      return { filePath: '', modResults: {} };\n    },\n    async write() {},\n  }),\n  finalized: provider<unknown>({\n    getFilePath() {\n      return '';\n    },\n    async read() {\n      return { filePath: '', modResults: {} };\n    },\n    async write() {},\n  }),\n  // Append a rule to supply gradle.properties data to mods on `mods.android.gradleProperties`\n  manifest: provider<Manifest.AndroidManifest>({\n    isIntrospective: true,\n    getFilePath({ modRequest: { platformProjectRoot } }) {\n      return path.join(platformProjectRoot, 'app/src/main/AndroidManifest.xml');\n    },\n    async read(filePath, config) {\n      try {\n        return await Manifest.readAndroidManifestAsync(filePath);\n      } catch (error: any) {\n        if (!config.modRequest.introspect) {\n          throw error;\n        }\n      }\n      return await getAndroidManifestTemplate(config);\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) return;\n      await Manifest.writeAndroidManifestAsync(filePath, sortAndroidManifest(modResults));\n    },\n  }),\n\n  // Append a rule to supply gradle.properties data to mods on `mods.android.gradleProperties`\n  gradleProperties: provider<Properties.PropertiesItem[]>({\n    isIntrospective: true,\n\n    getFilePath({ modRequest: { platformProjectRoot } }) {\n      return path.join(platformProjectRoot, 'gradle.properties');\n    },\n    async read(filePath, config) {\n      try {\n        return await Properties.parsePropertiesFile(await readFile(filePath, 'utf8'));\n      } catch (error) {\n        if (!config.modRequest.introspect) {\n          throw error;\n        }\n      }\n      return [];\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) return;\n      await writeFile(filePath, Properties.propertiesListToString(modResults));\n    },\n  }),\n\n  // Append a rule to supply strings.xml data to mods on `mods.android.strings`\n  strings: provider<Resources.ResourceXML>({\n    isIntrospective: true,\n\n    async getFilePath({ modRequest: { projectRoot, introspect } }) {\n      try {\n        return await Strings.getProjectStringsXMLPathAsync(projectRoot);\n      } catch (error: any) {\n        if (!introspect) {\n          throw error;\n        }\n      }\n      return '';\n    },\n\n    async read(filePath, config) {\n      try {\n        return await Resources.readResourcesXMLAsync({ path: filePath });\n      } catch (error) {\n        if (!config.modRequest.introspect) {\n          throw error;\n        }\n      }\n      return { resources: {} };\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) return;\n      await writeXMLAsync({ path: filePath, xml: modResults });\n    },\n  }),\n\n  colors: provider<Resources.ResourceXML>({\n    isIntrospective: true,\n\n    async getFilePath({ modRequest: { projectRoot, introspect } }) {\n      try {\n        return await Colors.getProjectColorsXMLPathAsync(projectRoot);\n      } catch (error: any) {\n        if (!introspect) {\n          throw error;\n        }\n      }\n      return '';\n    },\n\n    async read(filePath, { modRequest: { introspect } }) {\n      try {\n        return await Resources.readResourcesXMLAsync({ path: filePath });\n      } catch (error: any) {\n        if (!introspect) {\n          throw error;\n        }\n      }\n      return { resources: {} };\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) return;\n      await writeXMLAsync({ path: filePath, xml: modResults });\n    },\n  }),\n\n  colorsNight: provider<Resources.ResourceXML>({\n    isIntrospective: true,\n\n    async getFilePath({ modRequest: { projectRoot, introspect } }) {\n      try {\n        return await Colors.getProjectColorsXMLPathAsync(projectRoot, { kind: 'values-night' });\n      } catch (error: any) {\n        if (!introspect) {\n          throw error;\n        }\n      }\n      return '';\n    },\n    async read(filePath, config) {\n      try {\n        return await Resources.readResourcesXMLAsync({ path: filePath });\n      } catch (error: any) {\n        if (!config.modRequest.introspect) {\n          throw error;\n        }\n      }\n      return { resources: {} };\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) return;\n      await writeXMLAsync({ path: filePath, xml: modResults });\n    },\n  }),\n\n  styles: provider<Resources.ResourceXML>({\n    isIntrospective: true,\n\n    async getFilePath({ modRequest: { projectRoot, introspect } }) {\n      try {\n        return await Styles.getProjectStylesXMLPathAsync(projectRoot);\n      } catch (error: any) {\n        if (!introspect) {\n          throw error;\n        }\n      }\n      return '';\n    },\n    async read(filePath, config) {\n      let styles: Resources.ResourceXML = { resources: {} };\n\n      try {\n        // Adds support for `tools:x`\n        styles = await Resources.readResourcesXMLAsync({\n          path: filePath,\n          fallback: `<?xml version=\"1.0\" encoding=\"utf-8\"?><resources xmlns:tools=\"http://schemas.android.com/tools\"></resources>`,\n        });\n      } catch (error: any) {\n        if (!config.modRequest.introspect) {\n          throw error;\n        }\n      }\n\n      // Ensure support for tools is added...\n      if (!styles.resources.$) {\n        styles.resources.$ = {};\n      }\n      if (!styles.resources.$?.['xmlns:tools']) {\n        styles.resources.$['xmlns:tools'] = 'http://schemas.android.com/tools';\n      }\n      return styles;\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) return;\n      await writeXMLAsync({ path: filePath, xml: modResults });\n    },\n  }),\n\n  projectBuildGradle: provider<Paths.GradleProjectFile>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      return Paths.getProjectBuildGradleFilePath(projectRoot);\n    },\n    async read(filePath) {\n      return Paths.getFileInfo(filePath);\n    },\n    async write(filePath, { modResults: { contents } }) {\n      await writeFile(filePath, contents);\n    },\n  }),\n\n  settingsGradle: provider<Paths.GradleProjectFile>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      return Paths.getSettingsGradleFilePath(projectRoot);\n    },\n    async read(filePath) {\n      return Paths.getFileInfo(filePath);\n    },\n    async write(filePath, { modResults: { contents } }) {\n      await writeFile(filePath, contents);\n    },\n  }),\n\n  appBuildGradle: provider<Paths.GradleProjectFile>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      return Paths.getAppBuildGradleFilePath(projectRoot);\n    },\n    async read(filePath) {\n      return Paths.getFileInfo(filePath);\n    },\n    async write(filePath, { modResults: { contents } }) {\n      await writeFile(filePath, contents);\n    },\n  }),\n\n  mainActivity: provider<Paths.ApplicationProjectFile>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      return Paths.getProjectFilePath(projectRoot, 'MainActivity');\n    },\n    async read(filePath) {\n      return Paths.getFileInfo(filePath);\n    },\n    async write(filePath, { modResults: { contents } }) {\n      await writeFile(filePath, contents);\n    },\n  }),\n\n  mainApplication: provider<Paths.ApplicationProjectFile>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      return Paths.getProjectFilePath(projectRoot, 'MainApplication');\n    },\n    async read(filePath) {\n      return Paths.getFileInfo(filePath);\n    },\n    async write(filePath, { modResults: { contents } }) {\n      await writeFile(filePath, contents);\n    },\n  }),\n};\n\ntype AndroidDefaultProviders = typeof defaultProviders;\n\nexport function withAndroidBaseMods(\n  config: ExportedConfig,\n  {\n    providers,\n    ...props\n  }: ForwardedBaseModOptions & { providers?: Partial<AndroidDefaultProviders> } = {}\n): ExportedConfig {\n  return withGeneratedBaseMods<AndroidModName>(config, {\n    ...props,\n    platform: 'android',\n    providers: providers ?? getAndroidModFileProviders(),\n  });\n}\n\nexport function getAndroidModFileProviders() {\n  return defaultProviders;\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,eAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,cAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,SAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,QAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,KAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,IAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,YAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,WAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsF,SAAAG,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEtF,MAAM;EAAEG,QAAQ;EAAEC;AAAU,CAAC,GAAGC,cAAQ;AAIxC,SAASC,0BAA0BA,CAACC,MAAsB,EAAE;EAC1D;EACA;EACA,OAAO,IAAAC,oBAAa,EAAC;AACvB,kFACID,MAAM,CAACE,OAAO,EAAEC,OAAO,IAAI,uBAAuB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACG,CAAC;AACJ;AAEO,SAASC,mBAAmBA,CAACC,GAAoB,EAAE;EACxD,IAAIA,GAAG,CAACC,QAAQ,EAAE;IAChB;IACAD,GAAG,CAACC,QAAQ,GAAG,IAAAC,wBAAU,EAACF,GAAG,CAACC,QAAQ,EAAEE,+BAAiB,CAAC;IAE1D,IAAIC,KAAK,CAACC,OAAO,CAACL,GAAG,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE;MAClD;MACAD,GAAG,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC7C,IAAID,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC;QACxD,IAAIF,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC;QACvD,OAAO,CAAC;MACV,CAAC,CAAC;IACJ;IAEA,IAAIL,KAAK,CAACC,OAAO,CAACL,GAAG,CAACC,QAAQ,CAACS,WAAW,CAAC,EAAE;MAC3C;MACAV,GAAG,CAACC,QAAQ,CAACS,WAAW,GAAGV,GAAG,CAACC,QAAQ,CAACS,WAAW,CAACC,GAAG,CAAED,WAAW,IAAK;QACvEA,WAAW,GAAG,IAAAE,8BAAgB,EAACF,WAAW,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QAEjF,IAAIN,KAAK,CAACC,OAAO,CAACK,WAAW,CAAC,WAAW,CAAC,CAAC,EAAE;UAC3C;UACAA,WAAW,CAAC,WAAW,CAAC,CAACJ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACtC,IAAID,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC;YACxD,IAAIF,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC;YACvD,OAAO,CAAC;UACV,CAAC,CAAC;QACJ;QACA,OAAOC,WAAW;MACpB,CAAC,CAAC;IACJ;EACF;EACA,OAAOV,GAAG;AACZ;AAEA,MAAMa,gBAAgB,GAAG;EACvBC,SAAS,EAAE,IAAAC,yBAAQ,EAAU;IAC3BC,WAAWA,CAAA,EAAG;MACZ,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,IAAIA,CAAA,EAAG;MACX,OAAO;QAAEC,QAAQ,EAAE,EAAE;QAAEC,UAAU,EAAE,CAAC;MAAE,CAAC;IACzC,CAAC;IACD,MAAMC,KAAKA,CAAA,EAAG,CAAC;EACjB,CAAC,CAAC;EACFC,SAAS,EAAE,IAAAN,yBAAQ,EAAU;IAC3BC,WAAWA,CAAA,EAAG;MACZ,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,IAAIA,CAAA,EAAG;MACX,OAAO;QAAEC,QAAQ,EAAE,EAAE;QAAEC,UAAU,EAAE,CAAC;MAAE,CAAC;IACzC,CAAC;IACD,MAAMC,KAAKA,CAAA,EAAG,CAAC;EACjB,CAAC,CAAC;EACF;EACAnB,QAAQ,EAAE,IAAAc,yBAAQ,EAA2B;IAC3CO,eAAe,EAAE,IAAI;IACrBN,WAAWA,CAAC;MAAEO,UAAU,EAAE;QAAEC;MAAoB;IAAE,CAAC,EAAE;MACnD,OAAOC,eAAI,CAACC,IAAI,CAACF,mBAAmB,EAAE,kCAAkC,CAAC;IAC3E,CAAC;IACD,MAAMP,IAAIA,CAACC,QAAQ,EAAEvB,MAAM,EAAE;MAC3B,IAAI;QACF,OAAO,MAAMgC,mBAAQ,CAACC,wBAAwB,CAACV,QAAQ,CAAC;MAC1D,CAAC,CAAC,OAAOW,KAAU,EAAE;QACnB,IAAI,CAAClC,MAAM,CAAC4B,UAAU,CAACO,UAAU,EAAE;UACjC,MAAMD,KAAK;QACb;MACF;MACA,OAAO,MAAMnC,0BAA0B,CAACC,MAAM,CAAC;IACjD,CAAC;IACD,MAAMyB,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU;MAAEI,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;MAChB,MAAMH,mBAAQ,CAACI,yBAAyB,CAACb,QAAQ,EAAEnB,mBAAmB,CAACoB,UAAU,CAAC,CAAC;IACrF;EACF,CAAC,CAAC;EAEF;EACAa,gBAAgB,EAAE,IAAAjB,yBAAQ,EAA8B;IACtDO,eAAe,EAAE,IAAI;IAErBN,WAAWA,CAAC;MAAEO,UAAU,EAAE;QAAEC;MAAoB;IAAE,CAAC,EAAE;MACnD,OAAOC,eAAI,CAACC,IAAI,CAACF,mBAAmB,EAAE,mBAAmB,CAAC;IAC5D,CAAC;IACD,MAAMP,IAAIA,CAACC,QAAQ,EAAEvB,MAAM,EAAE;MAC3B,IAAI;QACF,OAAO,MAAMsC,qBAAU,CAACC,mBAAmB,CAAC,MAAM3C,QAAQ,CAAC2B,QAAQ,EAAE,MAAM,CAAC,CAAC;MAC/E,CAAC,CAAC,OAAOW,KAAK,EAAE;QACd,IAAI,CAAClC,MAAM,CAAC4B,UAAU,CAACO,UAAU,EAAE;UACjC,MAAMD,KAAK;QACb;MACF;MACA,OAAO,EAAE;IACX,CAAC;IACD,MAAMT,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU;MAAEI,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;MAChB,MAAMtC,SAAS,CAAC0B,QAAQ,EAAEe,qBAAU,CAACE,sBAAsB,CAAChB,UAAU,CAAC,CAAC;IAC1E;EACF,CAAC,CAAC;EAEF;EACAiB,OAAO,EAAE,IAAArB,yBAAQ,EAAwB;IACvCO,eAAe,EAAE,IAAI;IAErB,MAAMN,WAAWA,CAAC;MAAEO,UAAU,EAAE;QAAEc,WAAW;QAAEP;MAAW;IAAE,CAAC,EAAE;MAC7D,IAAI;QACF,OAAO,MAAMQ,kBAAO,CAACC,6BAA6B,CAACF,WAAW,CAAC;MACjE,CAAC,CAAC,OAAOR,KAAU,EAAE;QACnB,IAAI,CAACC,UAAU,EAAE;UACf,MAAMD,KAAK;QACb;MACF;MACA,OAAO,EAAE;IACX,CAAC;IAED,MAAMZ,IAAIA,CAACC,QAAQ,EAAEvB,MAAM,EAAE;MAC3B,IAAI;QACF,OAAO,MAAM6C,oBAAS,CAACC,qBAAqB,CAAC;UAAEhB,IAAI,EAAEP;QAAS,CAAC,CAAC;MAClE,CAAC,CAAC,OAAOW,KAAK,EAAE;QACd,IAAI,CAAClC,MAAM,CAAC4B,UAAU,CAACO,UAAU,EAAE;UACjC,MAAMD,KAAK;QACb;MACF;MACA,OAAO;QAAEa,SAAS,EAAE,CAAC;MAAE,CAAC;IAC1B,CAAC;IACD,MAAMtB,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU;MAAEI,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;MAChB,MAAM,IAAAa,oBAAa,EAAC;QAAElB,IAAI,EAAEP,QAAQ;QAAE0B,GAAG,EAAEzB;MAAW,CAAC,CAAC;IAC1D;EACF,CAAC,CAAC;EAEF0B,MAAM,EAAE,IAAA9B,yBAAQ,EAAwB;IACtCO,eAAe,EAAE,IAAI;IAErB,MAAMN,WAAWA,CAAC;MAAEO,UAAU,EAAE;QAAEc,WAAW;QAAEP;MAAW;IAAE,CAAC,EAAE;MAC7D,IAAI;QACF,OAAO,MAAMgB,iBAAM,CAACC,4BAA4B,CAACV,WAAW,CAAC;MAC/D,CAAC,CAAC,OAAOR,KAAU,EAAE;QACnB,IAAI,CAACC,UAAU,EAAE;UACf,MAAMD,KAAK;QACb;MACF;MACA,OAAO,EAAE;IACX,CAAC;IAED,MAAMZ,IAAIA,CAACC,QAAQ,EAAE;MAAEK,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MACnD,IAAI;QACF,OAAO,MAAMU,oBAAS,CAACC,qBAAqB,CAAC;UAAEhB,IAAI,EAAEP;QAAS,CAAC,CAAC;MAClE,CAAC,CAAC,OAAOW,KAAU,EAAE;QACnB,IAAI,CAACC,UAAU,EAAE;UACf,MAAMD,KAAK;QACb;MACF;MACA,OAAO;QAAEa,SAAS,EAAE,CAAC;MAAE,CAAC;IAC1B,CAAC;IACD,MAAMtB,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU;MAAEI,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;MAChB,MAAM,IAAAa,oBAAa,EAAC;QAAElB,IAAI,EAAEP,QAAQ;QAAE0B,GAAG,EAAEzB;MAAW,CAAC,CAAC;IAC1D;EACF,CAAC,CAAC;EAEF6B,WAAW,EAAE,IAAAjC,yBAAQ,EAAwB;IAC3CO,eAAe,EAAE,IAAI;IAErB,MAAMN,WAAWA,CAAC;MAAEO,UAAU,EAAE;QAAEc,WAAW;QAAEP;MAAW;IAAE,CAAC,EAAE;MAC7D,IAAI;QACF,OAAO,MAAMgB,iBAAM,CAACC,4BAA4B,CAACV,WAAW,EAAE;UAAEY,IAAI,EAAE;QAAe,CAAC,CAAC;MACzF,CAAC,CAAC,OAAOpB,KAAU,EAAE;QACnB,IAAI,CAACC,UAAU,EAAE;UACf,MAAMD,KAAK;QACb;MACF;MACA,OAAO,EAAE;IACX,CAAC;IACD,MAAMZ,IAAIA,CAACC,QAAQ,EAAEvB,MAAM,EAAE;MAC3B,IAAI;QACF,OAAO,MAAM6C,oBAAS,CAACC,qBAAqB,CAAC;UAAEhB,IAAI,EAAEP;QAAS,CAAC,CAAC;MAClE,CAAC,CAAC,OAAOW,KAAU,EAAE;QACnB,IAAI,CAAClC,MAAM,CAAC4B,UAAU,CAACO,UAAU,EAAE;UACjC,MAAMD,KAAK;QACb;MACF;MACA,OAAO;QAAEa,SAAS,EAAE,CAAC;MAAE,CAAC;IAC1B,CAAC;IACD,MAAMtB,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU;MAAEI,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;MAChB,MAAM,IAAAa,oBAAa,EAAC;QAAElB,IAAI,EAAEP,QAAQ;QAAE0B,GAAG,EAAEzB;MAAW,CAAC,CAAC;IAC1D;EACF,CAAC,CAAC;EAEF+B,MAAM,EAAE,IAAAnC,yBAAQ,EAAwB;IACtCO,eAAe,EAAE,IAAI;IAErB,MAAMN,WAAWA,CAAC;MAAEO,UAAU,EAAE;QAAEc,WAAW;QAAEP;MAAW;IAAE,CAAC,EAAE;MAC7D,IAAI;QACF,OAAO,MAAMqB,iBAAM,CAACC,4BAA4B,CAACf,WAAW,CAAC;MAC/D,CAAC,CAAC,OAAOR,KAAU,EAAE;QACnB,IAAI,CAACC,UAAU,EAAE;UACf,MAAMD,KAAK;QACb;MACF;MACA,OAAO,EAAE;IACX,CAAC;IACD,MAAMZ,IAAIA,CAACC,QAAQ,EAAEvB,MAAM,EAAE;MAC3B,IAAIuD,MAA6B,GAAG;QAAER,SAAS,EAAE,CAAC;MAAE,CAAC;MAErD,IAAI;QACF;QACAQ,MAAM,GAAG,MAAMV,oBAAS,CAACC,qBAAqB,CAAC;UAC7ChB,IAAI,EAAEP,QAAQ;UACdmC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOxB,KAAU,EAAE;QACnB,IAAI,CAAClC,MAAM,CAAC4B,UAAU,CAACO,UAAU,EAAE;UACjC,MAAMD,KAAK;QACb;MACF;;MAEA;MACA,IAAI,CAACqB,MAAM,CAACR,SAAS,CAACjC,CAAC,EAAE;QACvByC,MAAM,CAACR,SAAS,CAACjC,CAAC,GAAG,CAAC,CAAC;MACzB;MACA,IAAI,CAACyC,MAAM,CAACR,SAAS,CAACjC,CAAC,GAAG,aAAa,CAAC,EAAE;QACxCyC,MAAM,CAACR,SAAS,CAACjC,CAAC,CAAC,aAAa,CAAC,GAAG,kCAAkC;MACxE;MACA,OAAOyC,MAAM;IACf,CAAC;IACD,MAAM9B,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU;MAAEI,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;MAChB,MAAM,IAAAa,oBAAa,EAAC;QAAElB,IAAI,EAAEP,QAAQ;QAAE0B,GAAG,EAAEzB;MAAW,CAAC,CAAC;IAC1D;EACF,CAAC,CAAC;EAEFmC,kBAAkB,EAAE,IAAAvC,yBAAQ,EAA0B;IACpDC,WAAWA,CAAC;MAAEO,UAAU,EAAE;QAAEc;MAAY;IAAE,CAAC,EAAE;MAC3C,OAAOkB,gBAAK,CAACC,6BAA6B,CAACnB,WAAW,CAAC;IACzD,CAAC;IACD,MAAMpB,IAAIA,CAACC,QAAQ,EAAE;MACnB,OAAOqC,gBAAK,CAACE,WAAW,CAACvC,QAAQ,CAAC;IACpC,CAAC;IACD,MAAME,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU,EAAE;QAAEuC;MAAS;IAAE,CAAC,EAAE;MAClD,MAAMlE,SAAS,CAAC0B,QAAQ,EAAEwC,QAAQ,CAAC;IACrC;EACF,CAAC,CAAC;EAEFC,cAAc,EAAE,IAAA5C,yBAAQ,EAA0B;IAChDC,WAAWA,CAAC;MAAEO,UAAU,EAAE;QAAEc;MAAY;IAAE,CAAC,EAAE;MAC3C,OAAOkB,gBAAK,CAACK,yBAAyB,CAACvB,WAAW,CAAC;IACrD,CAAC;IACD,MAAMpB,IAAIA,CAACC,QAAQ,EAAE;MACnB,OAAOqC,gBAAK,CAACE,WAAW,CAACvC,QAAQ,CAAC;IACpC,CAAC;IACD,MAAME,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU,EAAE;QAAEuC;MAAS;IAAE,CAAC,EAAE;MAClD,MAAMlE,SAAS,CAAC0B,QAAQ,EAAEwC,QAAQ,CAAC;IACrC;EACF,CAAC,CAAC;EAEFG,cAAc,EAAE,IAAA9C,yBAAQ,EAA0B;IAChDC,WAAWA,CAAC;MAAEO,UAAU,EAAE;QAAEc;MAAY;IAAE,CAAC,EAAE;MAC3C,OAAOkB,gBAAK,CAACO,yBAAyB,CAACzB,WAAW,CAAC;IACrD,CAAC;IACD,MAAMpB,IAAIA,CAACC,QAAQ,EAAE;MACnB,OAAOqC,gBAAK,CAACE,WAAW,CAACvC,QAAQ,CAAC;IACpC,CAAC;IACD,MAAME,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU,EAAE;QAAEuC;MAAS;IAAE,CAAC,EAAE;MAClD,MAAMlE,SAAS,CAAC0B,QAAQ,EAAEwC,QAAQ,CAAC;IACrC;EACF,CAAC,CAAC;EAEFK,YAAY,EAAE,IAAAhD,yBAAQ,EAA+B;IACnDC,WAAWA,CAAC;MAAEO,UAAU,EAAE;QAAEc;MAAY;IAAE,CAAC,EAAE;MAC3C,OAAOkB,gBAAK,CAACS,kBAAkB,CAAC3B,WAAW,EAAE,cAAc,CAAC;IAC9D,CAAC;IACD,MAAMpB,IAAIA,CAACC,QAAQ,EAAE;MACnB,OAAOqC,gBAAK,CAACE,WAAW,CAACvC,QAAQ,CAAC;IACpC,CAAC;IACD,MAAME,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU,EAAE;QAAEuC;MAAS;IAAE,CAAC,EAAE;MAClD,MAAMlE,SAAS,CAAC0B,QAAQ,EAAEwC,QAAQ,CAAC;IACrC;EACF,CAAC,CAAC;EAEFO,eAAe,EAAE,IAAAlD,yBAAQ,EAA+B;IACtDC,WAAWA,CAAC;MAAEO,UAAU,EAAE;QAAEc;MAAY;IAAE,CAAC,EAAE;MAC3C,OAAOkB,gBAAK,CAACS,kBAAkB,CAAC3B,WAAW,EAAE,iBAAiB,CAAC;IACjE,CAAC;IACD,MAAMpB,IAAIA,CAACC,QAAQ,EAAE;MACnB,OAAOqC,gBAAK,CAACE,WAAW,CAACvC,QAAQ,CAAC;IACpC,CAAC;IACD,MAAME,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU,EAAE;QAAEuC;MAAS;IAAE,CAAC,EAAE;MAClD,MAAMlE,SAAS,CAAC0B,QAAQ,EAAEwC,QAAQ,CAAC;IACrC;EACF,CAAC;AACH,CAAC;AAIM,SAASQ,mBAAmBA,CACjCvE,MAAsB,EACtB;EACEwE,SAAS;EACT,GAAGC;AACuE,CAAC,GAAG,CAAC,CAAC,EAClE;EAChB,OAAO,IAAAC,sCAAqB,EAAiB1E,MAAM,EAAE;IACnD,GAAGyE,KAAK;IACRE,QAAQ,EAAE,SAAS;IACnBH,SAAS,EAAEA,SAAS,IAAII,0BAA0B,CAAC;EACrD,CAAC,CAAC;AACJ;AAEO,SAASA,0BAA0BA,CAAA,EAAG;EAC3C,OAAO1D,gBAAgB;AACzB", "ignoreList": []}