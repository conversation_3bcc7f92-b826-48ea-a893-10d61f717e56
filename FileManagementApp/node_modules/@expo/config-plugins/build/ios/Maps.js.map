{"version": 3, "file": "Maps.js", "names": ["_path", "data", "_interopRequireDefault", "require", "_resolveFrom", "_iosPlugins", "_generateCode", "e", "__esModule", "default", "debug", "MATCH_INIT", "exports", "withGoogleMapsKey", "createInfoPlistPlugin", "setGoogleMapsApiKey", "withMaps", "config", "<PERSON><PERSON><PERSON><PERSON>", "getGoogleMapsApiKey", "withMapsCocoaPods", "useGoogleMaps", "withGoogleMapsAppDelegate", "ios", "googleMapsApiKey", "GMSApiKey", "infoPlist", "addGoogleMapsAppDelegateImport", "src", "newSrc", "mergeContents", "tag", "join", "anchor", "offset", "comment", "removeGoogleMapsAppDelegateImport", "removeContents", "addGoogleMapsAppDelegateInit", "removeGoogleMapsAppDelegateInit", "addMapsCocoaPods", "removeMapsCocoaPods", "isReactNativeMapsInstalled", "projectRoot", "resolved", "resolveFrom", "silent", "path", "dirname", "isReactNativeMapsAutolinked", "with<PERSON><PERSON><PERSON><PERSON>", "googleMapsPath", "modRequest", "isLinked", "results", "modResults", "contents", "error", "code", "Error", "didMerge", "<PERSON><PERSON><PERSON><PERSON>", "withAppDelegate", "language"], "sources": ["../../src/ios/Maps.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { ConfigPlugin, InfoPlist } from '../Plugin.types';\nimport { createInfoPlistPlugin, withAppDelegate, withPodfile } from '../plugins/ios-plugins';\nimport { mergeContents, MergeResults, removeContents } from '../utils/generateCode';\n\nconst debug = require('debug')('expo:config-plugins:ios:maps') as typeof console.log;\n\nexport const MATCH_INIT = /\\bsuper\\.application\\(\\w+?, didFinishLaunchingWithOptions: \\w+?\\)/g;\n\nconst withGoogleMapsKey = createInfoPlistPlugin(setGoogleMapsApiKey, 'withGoogleMapsKey');\n\nexport const withMaps: ConfigPlugin = (config) => {\n  config = withGoogleMapsKey(config);\n\n  const apiKey = getGoogleMapsApiKey(config);\n  // Technically adds react-native-maps (Apple maps) and google maps.\n\n  debug('Google Maps API Key:', apiKey);\n  config = withMapsCocoaPods(config, { useGoogleMaps: !!apiKey });\n\n  // Adds/Removes AppDelegate setup for Google Maps API on iOS\n  config = withGoogleMapsAppDelegate(config, { apiKey });\n\n  return config;\n};\n\nexport function getGoogleMapsApiKey(config: Pick<ExpoConfig, 'ios'>) {\n  return config.ios?.config?.googleMapsApiKey ?? null;\n}\n\nexport function setGoogleMapsApiKey(\n  config: Pick<ExpoConfig, 'ios'>,\n  { GMSApiKey, ...infoPlist }: InfoPlist\n): InfoPlist {\n  const apiKey = getGoogleMapsApiKey(config);\n\n  if (apiKey === null) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    GMSApiKey: apiKey,\n  };\n}\n\nexport function addGoogleMapsAppDelegateImport(src: string): MergeResults {\n  const newSrc = ['#if canImport(GoogleMaps)', 'import GoogleMaps', '#endif'];\n\n  return mergeContents({\n    tag: 'react-native-maps-import',\n    src,\n    newSrc: newSrc.join('\\n'),\n    anchor: /@UIApplicationMain/,\n    offset: 0,\n    comment: '//',\n  });\n}\n\nexport function removeGoogleMapsAppDelegateImport(src: string): MergeResults {\n  return removeContents({\n    tag: 'react-native-maps-import',\n    src,\n  });\n}\n\nexport function addGoogleMapsAppDelegateInit(src: string, apiKey: string): MergeResults {\n  const newSrc = ['#if canImport(GoogleMaps)', `GMSServices.provideAPIKey(\"${apiKey}\")`, '#endif'];\n\n  return mergeContents({\n    tag: 'react-native-maps-init',\n    src,\n    newSrc: newSrc.join('\\n'),\n    anchor: MATCH_INIT,\n    offset: 0,\n    comment: '//',\n  });\n}\n\nexport function removeGoogleMapsAppDelegateInit(src: string): MergeResults {\n  return removeContents({\n    tag: 'react-native-maps-init',\n    src,\n  });\n}\n\n/**\n * @param src The contents of the Podfile.\n * @returns Podfile with Google Maps added.\n */\nexport function addMapsCocoaPods(src: string): MergeResults {\n  return mergeContents({\n    tag: 'react-native-maps',\n    src,\n    newSrc: `  pod 'react-native-google-maps', path: File.dirname(\\`node --print \"require.resolve('react-native-maps/package.json')\"\\`)`,\n    anchor: /use_native_modules/,\n    offset: 0,\n    comment: '#',\n  });\n}\n\nexport function removeMapsCocoaPods(src: string): MergeResults {\n  return removeContents({\n    tag: 'react-native-maps',\n    src,\n  });\n}\n\nfunction isReactNativeMapsInstalled(projectRoot: string): string | null {\n  const resolved = resolveFrom.silent(projectRoot, 'react-native-maps/package.json');\n  return resolved ? path.dirname(resolved) : null;\n}\n\nfunction isReactNativeMapsAutolinked(config: Pick<ExpoConfig, '_internal'>): boolean {\n  // Only add the native code changes if we know that the package is going to be linked natively.\n  // This is specifically for monorepo support where one app might have react-native-maps (adding it to the node_modules)\n  // but another app will not have it installed in the package.json, causing it to not be linked natively.\n  // This workaround only exists because react-native-maps doesn't have a config plugin vendored in the package.\n\n  // TODO: `react-native-maps` doesn't use Expo autolinking so we cannot safely disable the module.\n  return true;\n\n  // return (\n  //   !config._internal?.autolinkedModules ||\n  //   config._internal.autolinkedModules.includes('react-native-maps')\n  // );\n}\n\nconst withMapsCocoaPods: ConfigPlugin<{ useGoogleMaps: boolean }> = (config, { useGoogleMaps }) => {\n  return withPodfile(config, async (config) => {\n    // Only add the block if react-native-maps is installed in the project (best effort).\n    // Generally prebuild runs after a yarn install so this should always work as expected.\n    const googleMapsPath = isReactNativeMapsInstalled(config.modRequest.projectRoot);\n    const isLinked = isReactNativeMapsAutolinked(config);\n    debug('Is Expo Autolinked:', isLinked);\n    debug('react-native-maps path:', googleMapsPath);\n\n    let results: MergeResults;\n\n    if (isLinked && googleMapsPath && useGoogleMaps) {\n      try {\n        results = addMapsCocoaPods(config.modResults.contents);\n      } catch (error: any) {\n        if (error.code === 'ERR_NO_MATCH') {\n          throw new Error(\n            `Cannot add react-native-maps to the project's ios/Podfile because it's malformed. Report this with a copy of your project Podfile: https://github.com/expo/expo/issues`\n          );\n        }\n        throw error;\n      }\n    } else {\n      // If the package is no longer installed, then remove the block.\n      results = removeMapsCocoaPods(config.modResults.contents);\n    }\n\n    if (results.didMerge || results.didClear) {\n      config.modResults.contents = results.contents;\n    }\n\n    return config;\n  });\n};\n\nconst withGoogleMapsAppDelegate: ConfigPlugin<{ apiKey: string | null }> = (config, { apiKey }) => {\n  return withAppDelegate(config, (config) => {\n    if (\n      !apiKey ||\n      !isReactNativeMapsAutolinked(config) ||\n      !isReactNativeMapsInstalled(config.modRequest.projectRoot)\n    ) {\n      config.modResults.contents = removeGoogleMapsAppDelegateImport(\n        config.modResults.contents\n      ).contents;\n      config.modResults.contents = removeGoogleMapsAppDelegateInit(\n        config.modResults.contents\n      ).contents;\n      return config;\n    }\n\n    if (config.modResults.language !== 'swift') {\n      throw new Error(\n        `Cannot setup Google Maps because the project AppDelegate is not a supported language: ${config.modResults.language}`\n      );\n    }\n\n    try {\n      config.modResults.contents = addGoogleMapsAppDelegateImport(\n        config.modResults.contents\n      ).contents;\n      config.modResults.contents = addGoogleMapsAppDelegateInit(\n        config.modResults.contents,\n        apiKey\n      ).contents;\n    } catch (error: any) {\n      if (error.code === 'ERR_NO_MATCH') {\n        throw new Error(\n          `Cannot add Google Maps to the project's AppDelegate because it's malformed. Report this with a copy of your project AppDelegate: https://github.com/expo/expo/issues`\n        );\n      }\n      throw error;\n    }\n    return config;\n  });\n};\n"], "mappings": ";;;;;;;;;;;;;;;AACA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,aAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,YAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAI,YAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,WAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,cAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,aAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAoF,SAAAC,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEpF,MAAMG,KAAK,GAAGP,OAAO,CAAC,OAAO,CAAC,CAAC,8BAA8B,CAAuB;AAE7E,MAAMQ,UAAU,GAAAC,OAAA,CAAAD,UAAA,GAAG,oEAAoE;AAE9F,MAAME,iBAAiB,GAAG,IAAAC,mCAAqB,EAACC,mBAAmB,EAAE,mBAAmB,CAAC;AAElF,MAAMC,QAAsB,GAAIC,MAAM,IAAK;EAChDA,MAAM,GAAGJ,iBAAiB,CAACI,MAAM,CAAC;EAElC,MAAMC,MAAM,GAAGC,mBAAmB,CAACF,MAAM,CAAC;EAC1C;;EAEAP,KAAK,CAAC,sBAAsB,EAAEQ,MAAM,CAAC;EACrCD,MAAM,GAAGG,iBAAiB,CAACH,MAAM,EAAE;IAAEI,aAAa,EAAE,CAAC,CAACH;EAAO,CAAC,CAAC;;EAE/D;EACAD,MAAM,GAAGK,yBAAyB,CAACL,MAAM,EAAE;IAAEC;EAAO,CAAC,CAAC;EAEtD,OAAOD,MAAM;AACf,CAAC;AAACL,OAAA,CAAAI,QAAA,GAAAA,QAAA;AAEK,SAASG,mBAAmBA,CAACF,MAA+B,EAAE;EACnE,OAAOA,MAAM,CAACM,GAAG,EAAEN,MAAM,EAAEO,gBAAgB,IAAI,IAAI;AACrD;AAEO,SAAST,mBAAmBA,CACjCE,MAA+B,EAC/B;EAAEQ,SAAS;EAAE,GAAGC;AAAqB,CAAC,EAC3B;EACX,MAAMR,MAAM,GAAGC,mBAAmB,CAACF,MAAM,CAAC;EAE1C,IAAIC,MAAM,KAAK,IAAI,EAAE;IACnB,OAAOQ,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZD,SAAS,EAAEP;EACb,CAAC;AACH;AAEO,SAASS,8BAA8BA,CAACC,GAAW,EAAgB;EACxE,MAAMC,MAAM,GAAG,CAAC,2BAA2B,EAAE,mBAAmB,EAAE,QAAQ,CAAC;EAE3E,OAAO,IAAAC,6BAAa,EAAC;IACnBC,GAAG,EAAE,0BAA0B;IAC/BH,GAAG;IACHC,MAAM,EAAEA,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC;IACzBC,MAAM,EAAE,oBAAoB;IAC5BC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AAEO,SAASC,iCAAiCA,CAACR,GAAW,EAAgB;EAC3E,OAAO,IAAAS,8BAAc,EAAC;IACpBN,GAAG,EAAE,0BAA0B;IAC/BH;EACF,CAAC,CAAC;AACJ;AAEO,SAASU,4BAA4BA,CAACV,GAAW,EAAEV,MAAc,EAAgB;EACtF,MAAMW,MAAM,GAAG,CAAC,2BAA2B,EAAE,8BAA8BX,MAAM,IAAI,EAAE,QAAQ,CAAC;EAEhG,OAAO,IAAAY,6BAAa,EAAC;IACnBC,GAAG,EAAE,wBAAwB;IAC7BH,GAAG;IACHC,MAAM,EAAEA,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC;IACzBC,MAAM,EAAEtB,UAAU;IAClBuB,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AAEO,SAASI,+BAA+BA,CAACX,GAAW,EAAgB;EACzE,OAAO,IAAAS,8BAAc,EAAC;IACpBN,GAAG,EAAE,wBAAwB;IAC7BH;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACO,SAASY,gBAAgBA,CAACZ,GAAW,EAAgB;EAC1D,OAAO,IAAAE,6BAAa,EAAC;IACnBC,GAAG,EAAE,mBAAmB;IACxBH,GAAG;IACHC,MAAM,EAAE,4HAA4H;IACpII,MAAM,EAAE,oBAAoB;IAC5BC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AAEO,SAASM,mBAAmBA,CAACb,GAAW,EAAgB;EAC7D,OAAO,IAAAS,8BAAc,EAAC;IACpBN,GAAG,EAAE,mBAAmB;IACxBH;EACF,CAAC,CAAC;AACJ;AAEA,SAASc,0BAA0BA,CAACC,WAAmB,EAAiB;EACtE,MAAMC,QAAQ,GAAGC,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,gCAAgC,CAAC;EAClF,OAAOC,QAAQ,GAAGG,eAAI,CAACC,OAAO,CAACJ,QAAQ,CAAC,GAAG,IAAI;AACjD;AAEA,SAASK,2BAA2BA,CAAChC,MAAqC,EAAW;EACnF;EACA;EACA;EACA;;EAEA;EACA,OAAO,IAAI;;EAEX;EACA;EACA;EACA;AACF;AAEA,MAAMG,iBAA2D,GAAGA,CAACH,MAAM,EAAE;EAAEI;AAAc,CAAC,KAAK;EACjG,OAAO,IAAA6B,yBAAW,EAACjC,MAAM,EAAE,MAAOA,MAAM,IAAK;IAC3C;IACA;IACA,MAAMkC,cAAc,GAAGT,0BAA0B,CAACzB,MAAM,CAACmC,UAAU,CAACT,WAAW,CAAC;IAChF,MAAMU,QAAQ,GAAGJ,2BAA2B,CAAChC,MAAM,CAAC;IACpDP,KAAK,CAAC,qBAAqB,EAAE2C,QAAQ,CAAC;IACtC3C,KAAK,CAAC,yBAAyB,EAAEyC,cAAc,CAAC;IAEhD,IAAIG,OAAqB;IAEzB,IAAID,QAAQ,IAAIF,cAAc,IAAI9B,aAAa,EAAE;MAC/C,IAAI;QACFiC,OAAO,GAAGd,gBAAgB,CAACvB,MAAM,CAACsC,UAAU,CAACC,QAAQ,CAAC;MACxD,CAAC,CAAC,OAAOC,KAAU,EAAE;QACnB,IAAIA,KAAK,CAACC,IAAI,KAAK,cAAc,EAAE;UACjC,MAAM,IAAIC,KAAK,CACb,wKACF,CAAC;QACH;QACA,MAAMF,KAAK;MACb;IACF,CAAC,MAAM;MACL;MACAH,OAAO,GAAGb,mBAAmB,CAACxB,MAAM,CAACsC,UAAU,CAACC,QAAQ,CAAC;IAC3D;IAEA,IAAIF,OAAO,CAACM,QAAQ,IAAIN,OAAO,CAACO,QAAQ,EAAE;MACxC5C,MAAM,CAACsC,UAAU,CAACC,QAAQ,GAAGF,OAAO,CAACE,QAAQ;IAC/C;IAEA,OAAOvC,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAED,MAAMK,yBAAkE,GAAGA,CAACL,MAAM,EAAE;EAAEC;AAAO,CAAC,KAAK;EACjG,OAAO,IAAA4C,6BAAe,EAAC7C,MAAM,EAAGA,MAAM,IAAK;IACzC,IACE,CAACC,MAAM,IACP,CAAC+B,2BAA2B,CAAChC,MAAM,CAAC,IACpC,CAACyB,0BAA0B,CAACzB,MAAM,CAACmC,UAAU,CAACT,WAAW,CAAC,EAC1D;MACA1B,MAAM,CAACsC,UAAU,CAACC,QAAQ,GAAGpB,iCAAiC,CAC5DnB,MAAM,CAACsC,UAAU,CAACC,QACpB,CAAC,CAACA,QAAQ;MACVvC,MAAM,CAACsC,UAAU,CAACC,QAAQ,GAAGjB,+BAA+B,CAC1DtB,MAAM,CAACsC,UAAU,CAACC,QACpB,CAAC,CAACA,QAAQ;MACV,OAAOvC,MAAM;IACf;IAEA,IAAIA,MAAM,CAACsC,UAAU,CAACQ,QAAQ,KAAK,OAAO,EAAE;MAC1C,MAAM,IAAIJ,KAAK,CACb,yFAAyF1C,MAAM,CAACsC,UAAU,CAACQ,QAAQ,EACrH,CAAC;IACH;IAEA,IAAI;MACF9C,MAAM,CAACsC,UAAU,CAACC,QAAQ,GAAG7B,8BAA8B,CACzDV,MAAM,CAACsC,UAAU,CAACC,QACpB,CAAC,CAACA,QAAQ;MACVvC,MAAM,CAACsC,UAAU,CAACC,QAAQ,GAAGlB,4BAA4B,CACvDrB,MAAM,CAACsC,UAAU,CAACC,QAAQ,EAC1BtC,MACF,CAAC,CAACsC,QAAQ;IACZ,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnB,IAAIA,KAAK,CAACC,IAAI,KAAK,cAAc,EAAE;QACjC,MAAM,IAAIC,KAAK,CACb,sKACF,CAAC;MACH;MACA,MAAMF,KAAK;IACb;IACA,OAAOxC,MAAM;EACf,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}