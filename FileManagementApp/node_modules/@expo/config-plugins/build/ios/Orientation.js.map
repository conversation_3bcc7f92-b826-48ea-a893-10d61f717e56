{"version": 3, "file": "Orientation.js", "names": ["_iosPlugins", "data", "require", "withOrientation", "exports", "createInfoPlistPluginWithPropertyGuard", "setOrientation", "infoPlistProperty", "expoConfigProperty", "getOrientation", "config", "orientation", "PORTRAIT_ORIENTATIONS", "LANDSCAPE_ORIENTATIONS", "getUISupportedInterfaceOrientations", "infoPlist", "UISupportedInterfaceOrientations"], "sources": ["../../src/ios/Orientation.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { InfoPlist, InterfaceOrientation } from './IosConfig.types';\nimport { createInfoPlistPluginWithPropertyGuard } from '../plugins/ios-plugins';\n\nexport const withOrientation = createInfoPlistPluginWithPropertyGuard(\n  setOrientation,\n  {\n    infoPlistProperty: 'UISupportedInterfaceOrientations',\n    expoConfigProperty: 'orientation',\n  },\n  'withOrientation'\n);\n\nexport function getOrientation(config: Pick<ExpoConfig, 'orientation'>) {\n  return config.orientation ?? null;\n}\n\nexport const PORTRAIT_ORIENTATIONS: InterfaceOrientation[] = [\n  'UIInterfaceOrientationPortrait',\n  'UIInterfaceOrientationPortraitUpsideDown',\n];\n\nexport const LANDSCAPE_ORIENTATIONS: InterfaceOrientation[] = [\n  'UIInterfaceOrientationLandscapeLeft',\n  'UIInterfaceOrientationLandscapeRight',\n];\n\nfunction getUISupportedInterfaceOrientations(orientation: string | null): InterfaceOrientation[] {\n  if (orientation === 'portrait') {\n    return PORTRAIT_ORIENTATIONS;\n  } else if (orientation === 'landscape') {\n    return LANDSCAPE_ORIENTATIONS;\n  } else {\n    return [...PORTRAIT_ORIENTATIONS, ...LANDSCAPE_ORIENTATIONS];\n  }\n}\n\nexport function setOrientation(\n  config: Pick<ExpoConfig, 'orientation'>,\n  infoPlist: InfoPlist\n): InfoPlist {\n  const orientation = getOrientation(config);\n\n  return {\n    ...infoPlist,\n    UISupportedInterfaceOrientations: getUISupportedInterfaceOrientations(orientation),\n  };\n}\n"], "mappings": ";;;;;;;;;AAGA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAME,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG,IAAAE,oDAAsC,EACnEC,cAAc,EACd;EACEC,iBAAiB,EAAE,kCAAkC;EACrDC,kBAAkB,EAAE;AACtB,CAAC,EACD,iBACF,CAAC;AAEM,SAASC,cAAcA,CAACC,MAAuC,EAAE;EACtE,OAAOA,MAAM,CAACC,WAAW,IAAI,IAAI;AACnC;AAEO,MAAMC,qBAA6C,GAAAR,OAAA,CAAAQ,qBAAA,GAAG,CAC3D,gCAAgC,EAChC,0CAA0C,CAC3C;AAEM,MAAMC,sBAA8C,GAAAT,OAAA,CAAAS,sBAAA,GAAG,CAC5D,qCAAqC,EACrC,sCAAsC,CACvC;AAED,SAASC,mCAAmCA,CAACH,WAA0B,EAA0B;EAC/F,IAAIA,WAAW,KAAK,UAAU,EAAE;IAC9B,OAAOC,qBAAqB;EAC9B,CAAC,MAAM,IAAID,WAAW,KAAK,WAAW,EAAE;IACtC,OAAOE,sBAAsB;EAC/B,CAAC,MAAM;IACL,OAAO,CAAC,GAAGD,qBAAqB,EAAE,GAAGC,sBAAsB,CAAC;EAC9D;AACF;AAEO,SAASP,cAAcA,CAC5BI,MAAuC,EACvCK,SAAoB,EACT;EACX,MAAMJ,WAAW,GAAGF,cAAc,CAACC,MAAM,CAAC;EAE1C,OAAO;IACL,GAAGK,SAAS;IACZC,gCAAgC,EAAEF,mCAAmC,CAACH,WAAW;EACnF,CAAC;AACH", "ignoreList": []}