{"version": 3, "file": "Xcodeproj.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_path", "_slugify", "_xcode", "_pbxFile", "_string", "_warnings", "Paths", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "getProjectName", "projectRoot", "sourceRoot", "getSourceRoot", "path", "basename", "resolvePathOrProject", "projectRootOrProject", "getPbxproj", "sanitizedName", "name", "sanitizedNameForProjects", "slugify", "replace", "normalize", "getHackyProjectName", "config", "projectName", "assert", "createProjectFileForGroup", "filepath", "group", "file", "pbxFile", "conflictingFile", "children", "find", "child", "comment", "addResourceFileToGroup", "groupName", "isBuildFile", "project", "verbose", "targetUuid", "addFileToGroupAndLink", "addFileToProject", "addToPbxFileReferenceSection", "addToPbxBuildFileSection", "addToPbxResourcesBuildPhase", "addBuildSourceFileToGroup", "addToPbxSourcesBuildPhase", "pbxGroupByPathOrAssert", "addWarningIOS", "target", "applicationNativeTarget", "get<PERSON><PERSON><PERSON>", "uuid", "generateUuid", "fileRef", "push", "value", "getApplicationNativeTarget", "String", "addFramework", "framework", "splitPath", "split", "findGroup", "undefined", "findGroupInsideGroup", "foundGroup", "getPBXGroupByKey", "firstProject", "getFirstProject", "mainGroup", "components", "nextGroup", "Error", "ensureGroupRecursively", "<PERSON><PERSON><PERSON><PERSON>", "topMostGroup", "pathComponent", "pbxCreateGroup", "pbxGroupByName", "projectPath", "getPBXProjectPath", "xcode", "parseSync", "getProductName", "productName", "targetName", "get<PERSON><PERSON>t<PERSON>arget", "firstTarget", "getProjectSection", "pbxProjectSection", "getXCConfigurationListEntries", "lists", "pbxXCConfigurationList", "entries", "filter", "isNotComment", "getBuildConfigurationsForListId", "configurationListId", "configurationListEntries", "configurationList", "key", "buildConfigurations", "map", "pbxXCBuildConfigurationSection", "isBuildConfig", "includes", "getBuildConfigurationForListIdAndName", "buildConfiguration", "xcBuildConfigurationEntry", "trimQuotes", "sectionItem", "isa", "isNotTestHost", "buildSettings", "TEST_HOST", "endsWith", "unquote", "match", "resolveXcodeBuildSetting", "lookup", "parsedValue", "variable", "transformations", "slice", "lookedUp", "resolved", "for<PERSON>ach", "modifier", "toLowerCase", "toUpperCase", "extname", "dirname", "b", "extensionIndex", "lastIndexOf", "resolve"], "sources": ["../../../src/ios/utils/Xcodeproj.ts"], "sourcesContent": ["/**\n * Copyright © 2023-present 650 Industries, Inc. (aka Expo)\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport { ExpoConfig } from '@expo/config-types';\nimport assert from 'assert';\nimport path from 'path';\nimport slugify from 'slugify';\nimport xcode, {\n  PBXFile,\n  PBXGroup,\n  PBXNativeTarget,\n  PBXProject,\n  UUID,\n  XCBuildConfiguration,\n  XCConfigurationList,\n  XcodeProject,\n} from 'xcode';\nimport pbxFile from 'xcode/lib/pbxFile';\n\nimport { trimQuotes } from './string';\nimport { addWarningIOS } from '../../utils/warnings';\nimport * as Paths from '../Paths';\n\nexport type ProjectSectionEntry = [string, PBXProject];\n\nexport type NativeTargetSection = Record<string, PBXNativeTarget>;\n\nexport type NativeTargetSectionEntry = [string, PBXNativeTarget];\n\nexport type ConfigurationLists = Record<string, XCConfigurationList>;\n\nexport type ConfigurationListEntry = [string, XCConfigurationList];\n\nexport type ConfigurationSectionEntry = [string, XCBuildConfiguration];\n\nexport function getProjectName(projectRoot: string) {\n  const sourceRoot = Paths.getSourceRoot(projectRoot);\n  return path.basename(sourceRoot);\n}\n\nexport function resolvePathOrProject(\n  projectRootOrProject: string | XcodeProject\n): XcodeProject | null {\n  if (typeof projectRootOrProject === 'string') {\n    try {\n      return getPbxproj(projectRootOrProject);\n    } catch {\n      return null;\n    }\n  }\n  return projectRootOrProject;\n}\n\n// TODO: come up with a better solution for using app.json expo.name in various places\nexport function sanitizedName(name: string) {\n  // Default to the name `app` when every safe character has been sanitized\n  return sanitizedNameForProjects(name) || sanitizedNameForProjects(slugify(name)) || 'app';\n}\n\nfunction sanitizedNameForProjects(name: string) {\n  return name\n    .replace(/[\\W_]+/g, '')\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '');\n}\n\n// TODO: it's silly and kind of fragile that we look at app config to determine\n// the ios project paths. Overall this function needs to be revamped, just a\n// placeholder for now! Make this more robust when we support applying config\n// at any time (currently it's only applied on eject).\nexport function getHackyProjectName(projectRoot: string, config: ExpoConfig): string {\n  // Attempt to get the current ios folder name (apply).\n  try {\n    return getProjectName(projectRoot);\n  } catch {\n    // If no iOS project exists then create a new one (eject).\n    const projectName = config.name;\n    assert(projectName, 'Your project needs a name in app.json/app.config.js.');\n    return sanitizedName(projectName);\n  }\n}\n\nfunction createProjectFileForGroup({ filepath, group }: { filepath: string; group: PBXGroup }) {\n  const file = new pbxFile(filepath);\n\n  const conflictingFile = group.children.find((child) => child.comment === file.basename);\n  if (conflictingFile) {\n    // This can happen when a file like the GoogleService-Info.plist needs to be added and the eject command is run twice.\n    // Not much we can do here since it might be a conflicting file.\n    return null;\n  }\n  return file;\n}\n\n/**\n * Add a resource file (ex: `SplashScreen.storyboard`, `Images.xcassets`) to an Xcode project.\n * This is akin to creating a new code file in Xcode with `⌘+n`.\n */\nexport function addResourceFileToGroup({\n  filepath,\n  groupName,\n  // Should add to `PBXBuildFile Section`\n  isBuildFile,\n  project,\n  verbose,\n  targetUuid,\n}: {\n  filepath: string;\n  groupName: string;\n  isBuildFile?: boolean;\n  project: XcodeProject;\n  verbose?: boolean;\n  targetUuid?: string;\n}): XcodeProject {\n  return addFileToGroupAndLink({\n    filepath,\n    groupName,\n    project,\n    verbose,\n    targetUuid,\n    addFileToProject({ project, file }) {\n      project.addToPbxFileReferenceSection(file);\n      if (isBuildFile) {\n        project.addToPbxBuildFileSection(file);\n      }\n      project.addToPbxResourcesBuildPhase(file);\n    },\n  });\n}\n\n/**\n * Add a build source file (ex: `AppDelegate.m`, `ViewController.swift`) to an Xcode project.\n * This is akin to creating a new code file in Xcode with `⌘+n`.\n */\nexport function addBuildSourceFileToGroup({\n  filepath,\n  groupName,\n  project,\n  verbose,\n  targetUuid,\n}: {\n  filepath: string;\n  groupName: string;\n  project: XcodeProject;\n  verbose?: boolean;\n  targetUuid?: string;\n}): XcodeProject {\n  return addFileToGroupAndLink({\n    filepath,\n    groupName,\n    project,\n    verbose,\n    targetUuid,\n    addFileToProject({ project, file }) {\n      project.addToPbxFileReferenceSection(file);\n      project.addToPbxBuildFileSection(file);\n      project.addToPbxSourcesBuildPhase(file);\n    },\n  });\n}\n\n// TODO(brentvatne): I couldn't figure out how to do this with an existing\n// higher level function exposed by the xcode library, but we should find out how to do\n// that and replace this with it\nexport function addFileToGroupAndLink({\n  filepath,\n  groupName,\n  project,\n  verbose,\n  addFileToProject,\n  targetUuid,\n}: {\n  filepath: string;\n  groupName: string;\n  project: XcodeProject;\n  verbose?: boolean;\n  targetUuid?: string;\n  addFileToProject: (props: { file: PBXFile; project: XcodeProject }) => void;\n}): XcodeProject {\n  const group = pbxGroupByPathOrAssert(project, groupName);\n\n  const file = createProjectFileForGroup({ filepath, group });\n\n  if (!file) {\n    if (verbose) {\n      // This can happen when a file like the GoogleService-Info.plist needs to be added and the eject command is run twice.\n      // Not much we can do here since it might be a conflicting file.\n      addWarningIOS(\n        'ios-xcode-project',\n        `Skipped adding duplicate file \"${filepath}\" to PBXGroup named \"${groupName}\"`\n      );\n    }\n    return project;\n  }\n\n  if (targetUuid != null) {\n    file.target = targetUuid;\n  } else {\n    const applicationNativeTarget = project.getTarget('com.apple.product-type.application');\n    file.target = applicationNativeTarget?.uuid;\n  }\n\n  file.uuid = project.generateUuid();\n  file.fileRef = project.generateUuid();\n\n  addFileToProject({ project, file });\n\n  group.children.push({\n    value: file.fileRef,\n    comment: file.basename,\n  });\n  return project;\n}\n\nexport function getApplicationNativeTarget({\n  project,\n  projectName,\n}: {\n  project: XcodeProject;\n  projectName: string;\n}) {\n  const applicationNativeTarget = project.getTarget('com.apple.product-type.application');\n  assert(\n    applicationNativeTarget,\n    `Couldn't locate application PBXNativeTarget in '.xcodeproj' file.`\n  );\n  assert(\n    String(applicationNativeTarget.target.name) === projectName,\n    `Application native target name mismatch. Expected ${projectName}, but found ${applicationNativeTarget.target.name}.`\n  );\n  return applicationNativeTarget;\n}\n\n/**\n * Add a framework to the default app native target.\n *\n * @param projectName Name of the PBX project.\n * @param framework String ending in `.framework`, i.e. `StoreKit.framework`\n */\nexport function addFramework({\n  project,\n  projectName,\n  framework,\n}: {\n  project: XcodeProject;\n  projectName: string;\n  framework: string;\n}) {\n  const target = getApplicationNativeTarget({ project, projectName });\n  return project.addFramework(framework, { target: target.uuid });\n}\n\nfunction splitPath(path: string): string[] {\n  // TODO: Should we account for other platforms that may not use `/`\n  return path.split('/');\n}\n\nconst findGroup = (\n  group: PBXGroup | undefined,\n  name: string\n):\n  | {\n      value: UUID;\n      comment?: string;\n    }\n  | undefined => {\n  if (!group) {\n    return undefined;\n  }\n\n  return group.children.find((group) => group.comment === name);\n};\n\nfunction findGroupInsideGroup(\n  project: XcodeProject,\n  group: PBXGroup | undefined,\n  name: string\n): null | PBXGroup {\n  const foundGroup = findGroup(group, name);\n  if (foundGroup) {\n    return project.getPBXGroupByKey(foundGroup.value) ?? null;\n  }\n  return null;\n}\n\nfunction pbxGroupByPathOrAssert(project: XcodeProject, path: string): PBXGroup {\n  const { firstProject } = project.getFirstProject();\n\n  let group = project.getPBXGroupByKey(firstProject.mainGroup);\n\n  const components = splitPath(path);\n  for (const name of components) {\n    const nextGroup = findGroupInsideGroup(project, group, name);\n    if (nextGroup) {\n      group = nextGroup;\n    } else {\n      break;\n    }\n  }\n\n  if (!group) {\n    throw Error(`Xcode PBXGroup with name \"${path}\" could not be found in the Xcode project.`);\n  }\n\n  return group;\n}\n\nexport function ensureGroupRecursively(project: XcodeProject, filepath: string): PBXGroup | null {\n  const components = splitPath(filepath);\n  const hasChild = (group: PBXGroup, name: string) =>\n    group.children.find(({ comment }) => comment === name);\n  const { firstProject } = project.getFirstProject();\n\n  let topMostGroup = project.getPBXGroupByKey(firstProject.mainGroup);\n\n  for (const pathComponent of components) {\n    if (topMostGroup && !hasChild(topMostGroup, pathComponent)) {\n      topMostGroup.children.push({\n        comment: pathComponent,\n        value: project.pbxCreateGroup(pathComponent, '\"\"'),\n      });\n    }\n    topMostGroup = project.pbxGroupByName(pathComponent);\n  }\n  return topMostGroup ?? null;\n}\n\n/**\n * Get the pbxproj for the given path\n */\nexport function getPbxproj(projectRoot: string): XcodeProject {\n  const projectPath = Paths.getPBXProjectPath(projectRoot);\n  const project = xcode.project(projectPath);\n  project.parseSync();\n  return project;\n}\n\n/**\n * Get the productName for a project, if the name is using a variable `$(TARGET_NAME)`, then attempt to get the value of that variable.\n *\n * @param project\n */\nexport function getProductName(project: XcodeProject): string {\n  let productName = '$(TARGET_NAME)';\n  try {\n    // If the product name is numeric, this will fail (it's a getter).\n    // If the bundle identifier' final component is only numeric values, then the PRODUCT_NAME\n    // will be a numeric value, this results in a bug where the product name isn't useful,\n    // i.e. `com.bacon.001` -> `1` -- in this case, use the first target name.\n    productName = project.productName;\n  } catch {}\n\n  if (productName === '$(TARGET_NAME)') {\n    const targetName = project.getFirstTarget()?.firstTarget?.productName;\n    productName = targetName ?? productName;\n  }\n\n  return productName;\n}\n\nexport function getProjectSection(project: XcodeProject) {\n  return project.pbxProjectSection();\n}\n\nexport function getXCConfigurationListEntries(project: XcodeProject): ConfigurationListEntry[] {\n  const lists = project.pbxXCConfigurationList();\n  return Object.entries(lists).filter(isNotComment);\n}\n\nexport function getBuildConfigurationsForListId(\n  project: XcodeProject,\n  configurationListId: string\n): ConfigurationSectionEntry[] {\n  const configurationListEntries = getXCConfigurationListEntries(project);\n  const [, configurationList] = configurationListEntries.find(\n    ([key]) => key === configurationListId\n  ) as ConfigurationListEntry;\n\n  const buildConfigurations = configurationList.buildConfigurations.map((i) => i.value);\n\n  return Object.entries(project.pbxXCBuildConfigurationSection())\n    .filter(isNotComment)\n    .filter(isBuildConfig)\n    .filter(([key]: ConfigurationSectionEntry) => buildConfigurations.includes(key));\n}\n\nexport function getBuildConfigurationForListIdAndName(\n  project: XcodeProject,\n  {\n    configurationListId,\n    buildConfiguration,\n  }: { configurationListId: string; buildConfiguration: string }\n): ConfigurationSectionEntry {\n  const xcBuildConfigurationEntry = getBuildConfigurationsForListId(\n    project,\n    configurationListId\n  ).find((i) => trimQuotes(i[1].name) === buildConfiguration);\n  if (!xcBuildConfigurationEntry) {\n    throw new Error(\n      `Build configuration '${buildConfiguration}' does not exist in list with id '${configurationListId}'`\n    );\n  }\n  return xcBuildConfigurationEntry;\n}\n\nexport function isBuildConfig([, sectionItem]: ConfigurationSectionEntry): boolean {\n  return sectionItem.isa === 'XCBuildConfiguration';\n}\n\nexport function isNotTestHost([, sectionItem]: ConfigurationSectionEntry): boolean {\n  return !sectionItem.buildSettings.TEST_HOST;\n}\n\nexport function isNotComment([key]:\n  | ConfigurationSectionEntry\n  | ProjectSectionEntry\n  | ConfigurationListEntry\n  | NativeTargetSectionEntry): boolean {\n  return !key.endsWith(`_comment`);\n}\n\n// Remove surrounding double quotes if they exist.\nexport function unquote(value: string): string {\n  // projects with numeric names will fail due to a bug in the xcode package.\n  if (typeof value === 'number') {\n    value = String(value);\n  }\n  return value.match(/^\"(.*)\"$/)?.[1] ?? value;\n}\n\nexport function resolveXcodeBuildSetting(\n  value: string,\n  lookup: (buildSetting: string) => string | undefined\n): string {\n  const parsedValue = value?.replace(/\\$\\(([^()]*|\\([^)]*\\))\\)/g, (match) => {\n    // Remove the `$(` and `)`, then split modifier(s) from the variable name.\n    const [variable, ...transformations] = match.slice(2, -1).split(':');\n    // Resolve the variable recursively.\n    let lookedUp = lookup(variable);\n    if (lookedUp) {\n      lookedUp = resolveXcodeBuildSetting(lookedUp, lookup);\n    }\n    let resolved = lookedUp;\n\n    // Ref: http://codeworkshop.net/posts/xcode-build-setting-transformations\n    transformations.forEach((modifier) => {\n      switch (modifier) {\n        case 'lower':\n          // A lowercase representation.\n          resolved = resolved?.toLowerCase();\n          break;\n        case 'upper':\n          // An uppercase representation.\n          resolved = resolved?.toUpperCase();\n          break;\n        case 'suffix':\n          if (resolved) {\n            // The extension of a path including the '.' divider.\n            resolved = path.extname(resolved);\n          }\n          break;\n        case 'file':\n          if (resolved) {\n            // The file portion of a path.\n            resolved = path.basename(resolved);\n          }\n          break;\n        case 'dir':\n          if (resolved) {\n            // The directory portion of a path.\n            resolved = path.dirname(resolved);\n          }\n          break;\n        case 'base':\n          if (resolved) {\n            // The base name of a path - the last path component with any extension removed.\n            const b = path.basename(resolved);\n            const extensionIndex = b.lastIndexOf('.');\n            resolved = extensionIndex === -1 ? b : b.slice(0, extensionIndex);\n          }\n          break;\n        case 'rfc1034identifier':\n          // A representation suitable for use in a DNS name.\n\n          // TODO: Check the spec if there is one, this is just what we had before.\n          resolved = resolved?.replace(/[^a-zA-Z0-9]/g, '-');\n          // resolved = resolved.replace(/[\\/\\*\\s]/g, '-');\n          break;\n        case 'c99extidentifier':\n          // Like identifier, but with support for extended characters allowed by C99. Added in Xcode 6.\n          // TODO: Check the spec if there is one.\n          resolved = resolved?.replace(/[-\\s]/g, '_');\n          break;\n        case 'standardizepath':\n          if (resolved) {\n            // The equivalent of calling stringByStandardizingPath on the string.\n            // https://developer.apple.com/documentation/foundation/nsstring/1407194-standardizingpath\n            resolved = path.resolve(resolved);\n          }\n          break;\n        default:\n          resolved ||= modifier.match(/default=(.*)/)?.[1];\n          break;\n      }\n    });\n\n    return resolveXcodeBuildSetting(resolved ?? '', lookup);\n  });\n\n  if (parsedValue !== value) {\n    return resolveXcodeBuildSetting(parsedValue, lookup);\n  }\n  return value;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,SAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,QAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,OAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,MAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAUA,SAAAM,SAAA;EAAA,MAAAN,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAI,QAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,UAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,SAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,MAAA;EAAA,MAAAT,IAAA,GAAAU,uBAAA,CAAAR,OAAA;EAAAO,KAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAkC,SAAAW,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAnB,uBAAAW,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAzBlC;AACA;AACA;AACA;AACA;AACA;;AAkCO,SAASmB,cAAcA,CAACC,WAAmB,EAAE;EAClD,MAAMC,UAAU,GAAGxB,KAAK,CAAD,CAAC,CAACyB,aAAa,CAACF,WAAW,CAAC;EACnD,OAAOG,eAAI,CAACC,QAAQ,CAACH,UAAU,CAAC;AAClC;AAEO,SAASI,oBAAoBA,CAClCC,oBAA2C,EACtB;EACrB,IAAI,OAAOA,oBAAoB,KAAK,QAAQ,EAAE;IAC5C,IAAI;MACF,OAAOC,UAAU,CAACD,oBAAoB,CAAC;IACzC,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF;EACA,OAAOA,oBAAoB;AAC7B;;AAEA;AACO,SAASE,aAAaA,CAACC,IAAY,EAAE;EAC1C;EACA,OAAOC,wBAAwB,CAACD,IAAI,CAAC,IAAIC,wBAAwB,CAAC,IAAAC,kBAAO,EAACF,IAAI,CAAC,CAAC,IAAI,KAAK;AAC3F;AAEA,SAASC,wBAAwBA,CAACD,IAAY,EAAE;EAC9C,OAAOA,IAAI,CACRG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CACtBC,SAAS,CAAC,KAAK,CAAC,CAChBD,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;AACpC;;AAEA;AACA;AACA;AACA;AACO,SAASE,mBAAmBA,CAACd,WAAmB,EAAEe,MAAkB,EAAU;EACnF;EACA,IAAI;IACF,OAAOhB,cAAc,CAACC,WAAW,CAAC;EACpC,CAAC,CAAC,MAAM;IACN;IACA,MAAMgB,WAAW,GAAGD,MAAM,CAACN,IAAI;IAC/B,IAAAQ,iBAAM,EAACD,WAAW,EAAE,sDAAsD,CAAC;IAC3E,OAAOR,aAAa,CAACQ,WAAW,CAAC;EACnC;AACF;AAEA,SAASE,yBAAyBA,CAAC;EAAEC,QAAQ;EAAEC;AAA6C,CAAC,EAAE;EAC7F,MAAMC,IAAI,GAAG,KAAIC,kBAAO,EAACH,QAAQ,CAAC;EAElC,MAAMI,eAAe,GAAGH,KAAK,CAACI,QAAQ,CAACC,IAAI,CAAEC,KAAK,IAAKA,KAAK,CAACC,OAAO,KAAKN,IAAI,CAACjB,QAAQ,CAAC;EACvF,IAAImB,eAAe,EAAE;IACnB;IACA;IACA,OAAO,IAAI;EACb;EACA,OAAOF,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACO,SAASO,sBAAsBA,CAAC;EACrCT,QAAQ;EACRU,SAAS;EACT;EACAC,WAAW;EACXC,OAAO;EACPC,OAAO;EACPC;AAQF,CAAC,EAAgB;EACf,OAAOC,qBAAqB,CAAC;IAC3Bf,QAAQ;IACRU,SAAS;IACTE,OAAO;IACPC,OAAO;IACPC,UAAU;IACVE,gBAAgBA,CAAC;MAAEJ,OAAO;MAAEV;IAAK,CAAC,EAAE;MAClCU,OAAO,CAACK,4BAA4B,CAACf,IAAI,CAAC;MAC1C,IAAIS,WAAW,EAAE;QACfC,OAAO,CAACM,wBAAwB,CAAChB,IAAI,CAAC;MACxC;MACAU,OAAO,CAACO,2BAA2B,CAACjB,IAAI,CAAC;IAC3C;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACO,SAASkB,yBAAyBA,CAAC;EACxCpB,QAAQ;EACRU,SAAS;EACTE,OAAO;EACPC,OAAO;EACPC;AAOF,CAAC,EAAgB;EACf,OAAOC,qBAAqB,CAAC;IAC3Bf,QAAQ;IACRU,SAAS;IACTE,OAAO;IACPC,OAAO;IACPC,UAAU;IACVE,gBAAgBA,CAAC;MAAEJ,OAAO;MAAEV;IAAK,CAAC,EAAE;MAClCU,OAAO,CAACK,4BAA4B,CAACf,IAAI,CAAC;MAC1CU,OAAO,CAACM,wBAAwB,CAAChB,IAAI,CAAC;MACtCU,OAAO,CAACS,yBAAyB,CAACnB,IAAI,CAAC;IACzC;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACO,SAASa,qBAAqBA,CAAC;EACpCf,QAAQ;EACRU,SAAS;EACTE,OAAO;EACPC,OAAO;EACPG,gBAAgB;EAChBF;AAQF,CAAC,EAAgB;EACf,MAAMb,KAAK,GAAGqB,sBAAsB,CAACV,OAAO,EAAEF,SAAS,CAAC;EAExD,MAAMR,IAAI,GAAGH,yBAAyB,CAAC;IAAEC,QAAQ;IAAEC;EAAM,CAAC,CAAC;EAE3D,IAAI,CAACC,IAAI,EAAE;IACT,IAAIW,OAAO,EAAE;MACX;MACA;MACA,IAAAU,yBAAa,EACX,mBAAmB,EACnB,kCAAkCvB,QAAQ,wBAAwBU,SAAS,GAC7E,CAAC;IACH;IACA,OAAOE,OAAO;EAChB;EAEA,IAAIE,UAAU,IAAI,IAAI,EAAE;IACtBZ,IAAI,CAACsB,MAAM,GAAGV,UAAU;EAC1B,CAAC,MAAM;IACL,MAAMW,uBAAuB,GAAGb,OAAO,CAACc,SAAS,CAAC,oCAAoC,CAAC;IACvFxB,IAAI,CAACsB,MAAM,GAAGC,uBAAuB,EAAEE,IAAI;EAC7C;EAEAzB,IAAI,CAACyB,IAAI,GAAGf,OAAO,CAACgB,YAAY,CAAC,CAAC;EAClC1B,IAAI,CAAC2B,OAAO,GAAGjB,OAAO,CAACgB,YAAY,CAAC,CAAC;EAErCZ,gBAAgB,CAAC;IAAEJ,OAAO;IAAEV;EAAK,CAAC,CAAC;EAEnCD,KAAK,CAACI,QAAQ,CAACyB,IAAI,CAAC;IAClBC,KAAK,EAAE7B,IAAI,CAAC2B,OAAO;IACnBrB,OAAO,EAAEN,IAAI,CAACjB;EAChB,CAAC,CAAC;EACF,OAAO2B,OAAO;AAChB;AAEO,SAASoB,0BAA0BA,CAAC;EACzCpB,OAAO;EACPf;AAIF,CAAC,EAAE;EACD,MAAM4B,uBAAuB,GAAGb,OAAO,CAACc,SAAS,CAAC,oCAAoC,CAAC;EACvF,IAAA5B,iBAAM,EACJ2B,uBAAuB,EACvB,mEACF,CAAC;EACD,IAAA3B,iBAAM,EACJmC,MAAM,CAACR,uBAAuB,CAACD,MAAM,CAAClC,IAAI,CAAC,KAAKO,WAAW,EAC3D,qDAAqDA,WAAW,eAAe4B,uBAAuB,CAACD,MAAM,CAAClC,IAAI,GACpH,CAAC;EACD,OAAOmC,uBAAuB;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASS,YAAYA,CAAC;EAC3BtB,OAAO;EACPf,WAAW;EACXsC;AAKF,CAAC,EAAE;EACD,MAAMX,MAAM,GAAGQ,0BAA0B,CAAC;IAAEpB,OAAO;IAAEf;EAAY,CAAC,CAAC;EACnE,OAAOe,OAAO,CAACsB,YAAY,CAACC,SAAS,EAAE;IAAEX,MAAM,EAAEA,MAAM,CAACG;EAAK,CAAC,CAAC;AACjE;AAEA,SAASS,SAASA,CAACpD,IAAY,EAAY;EACzC;EACA,OAAOA,IAAI,CAACqD,KAAK,CAAC,GAAG,CAAC;AACxB;AAEA,MAAMC,SAAS,GAAGA,CAChBrC,KAA2B,EAC3BX,IAAY,KAMG;EACf,IAAI,CAACW,KAAK,EAAE;IACV,OAAOsC,SAAS;EAClB;EAEA,OAAOtC,KAAK,CAACI,QAAQ,CAACC,IAAI,CAAEL,KAAK,IAAKA,KAAK,CAACO,OAAO,KAAKlB,IAAI,CAAC;AAC/D,CAAC;AAED,SAASkD,oBAAoBA,CAC3B5B,OAAqB,EACrBX,KAA2B,EAC3BX,IAAY,EACK;EACjB,MAAMmD,UAAU,GAAGH,SAAS,CAACrC,KAAK,EAAEX,IAAI,CAAC;EACzC,IAAImD,UAAU,EAAE;IACd,OAAO7B,OAAO,CAAC8B,gBAAgB,CAACD,UAAU,CAACV,KAAK,CAAC,IAAI,IAAI;EAC3D;EACA,OAAO,IAAI;AACb;AAEA,SAAST,sBAAsBA,CAACV,OAAqB,EAAE5B,IAAY,EAAY;EAC7E,MAAM;IAAE2D;EAAa,CAAC,GAAG/B,OAAO,CAACgC,eAAe,CAAC,CAAC;EAElD,IAAI3C,KAAK,GAAGW,OAAO,CAAC8B,gBAAgB,CAACC,YAAY,CAACE,SAAS,CAAC;EAE5D,MAAMC,UAAU,GAAGV,SAAS,CAACpD,IAAI,CAAC;EAClC,KAAK,MAAMM,IAAI,IAAIwD,UAAU,EAAE;IAC7B,MAAMC,SAAS,GAAGP,oBAAoB,CAAC5B,OAAO,EAAEX,KAAK,EAAEX,IAAI,CAAC;IAC5D,IAAIyD,SAAS,EAAE;MACb9C,KAAK,GAAG8C,SAAS;IACnB,CAAC,MAAM;MACL;IACF;EACF;EAEA,IAAI,CAAC9C,KAAK,EAAE;IACV,MAAM+C,KAAK,CAAC,6BAA6BhE,IAAI,4CAA4C,CAAC;EAC5F;EAEA,OAAOiB,KAAK;AACd;AAEO,SAASgD,sBAAsBA,CAACrC,OAAqB,EAAEZ,QAAgB,EAAmB;EAC/F,MAAM8C,UAAU,GAAGV,SAAS,CAACpC,QAAQ,CAAC;EACtC,MAAMkD,QAAQ,GAAGA,CAACjD,KAAe,EAAEX,IAAY,KAC7CW,KAAK,CAACI,QAAQ,CAACC,IAAI,CAAC,CAAC;IAAEE;EAAQ,CAAC,KAAKA,OAAO,KAAKlB,IAAI,CAAC;EACxD,MAAM;IAAEqD;EAAa,CAAC,GAAG/B,OAAO,CAACgC,eAAe,CAAC,CAAC;EAElD,IAAIO,YAAY,GAAGvC,OAAO,CAAC8B,gBAAgB,CAACC,YAAY,CAACE,SAAS,CAAC;EAEnE,KAAK,MAAMO,aAAa,IAAIN,UAAU,EAAE;IACtC,IAAIK,YAAY,IAAI,CAACD,QAAQ,CAACC,YAAY,EAAEC,aAAa,CAAC,EAAE;MAC1DD,YAAY,CAAC9C,QAAQ,CAACyB,IAAI,CAAC;QACzBtB,OAAO,EAAE4C,aAAa;QACtBrB,KAAK,EAAEnB,OAAO,CAACyC,cAAc,CAACD,aAAa,EAAE,IAAI;MACnD,CAAC,CAAC;IACJ;IACAD,YAAY,GAAGvC,OAAO,CAAC0C,cAAc,CAACF,aAAa,CAAC;EACtD;EACA,OAAOD,YAAY,IAAI,IAAI;AAC7B;;AAEA;AACA;AACA;AACO,SAAS/D,UAAUA,CAACP,WAAmB,EAAgB;EAC5D,MAAM0E,WAAW,GAAGjG,KAAK,CAAD,CAAC,CAACkG,iBAAiB,CAAC3E,WAAW,CAAC;EACxD,MAAM+B,OAAO,GAAG6C,gBAAK,CAAC7C,OAAO,CAAC2C,WAAW,CAAC;EAC1C3C,OAAO,CAAC8C,SAAS,CAAC,CAAC;EACnB,OAAO9C,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS+C,cAAcA,CAAC/C,OAAqB,EAAU;EAC5D,IAAIgD,WAAW,GAAG,gBAAgB;EAClC,IAAI;IACF;IACA;IACA;IACA;IACAA,WAAW,GAAGhD,OAAO,CAACgD,WAAW;EACnC,CAAC,CAAC,MAAM,CAAC;EAET,IAAIA,WAAW,KAAK,gBAAgB,EAAE;IACpC,MAAMC,UAAU,GAAGjD,OAAO,CAACkD,cAAc,CAAC,CAAC,EAAEC,WAAW,EAAEH,WAAW;IACrEA,WAAW,GAAGC,UAAU,IAAID,WAAW;EACzC;EAEA,OAAOA,WAAW;AACpB;AAEO,SAASI,iBAAiBA,CAACpD,OAAqB,EAAE;EACvD,OAAOA,OAAO,CAACqD,iBAAiB,CAAC,CAAC;AACpC;AAEO,SAASC,6BAA6BA,CAACtD,OAAqB,EAA4B;EAC7F,MAAMuD,KAAK,GAAGvD,OAAO,CAACwD,sBAAsB,CAAC,CAAC;EAC9C,OAAOhG,MAAM,CAACiG,OAAO,CAACF,KAAK,CAAC,CAACG,MAAM,CAACC,YAAY,CAAC;AACnD;AAEO,SAASC,+BAA+BA,CAC7C5D,OAAqB,EACrB6D,mBAA2B,EACE;EAC7B,MAAMC,wBAAwB,GAAGR,6BAA6B,CAACtD,OAAO,CAAC;EACvE,MAAM,GAAG+D,iBAAiB,CAAC,GAAGD,wBAAwB,CAACpE,IAAI,CACzD,CAAC,CAACsE,GAAG,CAAC,KAAKA,GAAG,KAAKH,mBACrB,CAA2B;EAE3B,MAAMI,mBAAmB,GAAGF,iBAAiB,CAACE,mBAAmB,CAACC,GAAG,CAAEpG,CAAC,IAAKA,CAAC,CAACqD,KAAK,CAAC;EAErF,OAAO3D,MAAM,CAACiG,OAAO,CAACzD,OAAO,CAACmE,8BAA8B,CAAC,CAAC,CAAC,CAC5DT,MAAM,CAACC,YAAY,CAAC,CACpBD,MAAM,CAACU,aAAa,CAAC,CACrBV,MAAM,CAAC,CAAC,CAACM,GAAG,CAA4B,KAAKC,mBAAmB,CAACI,QAAQ,CAACL,GAAG,CAAC,CAAC;AACpF;AAEO,SAASM,qCAAqCA,CACnDtE,OAAqB,EACrB;EACE6D,mBAAmB;EACnBU;AAC2D,CAAC,EACnC;EAC3B,MAAMC,yBAAyB,GAAGZ,+BAA+B,CAC/D5D,OAAO,EACP6D,mBACF,CAAC,CAACnE,IAAI,CAAE5B,CAAC,IAAK,IAAA2G,oBAAU,EAAC3G,CAAC,CAAC,CAAC,CAAC,CAACY,IAAI,CAAC,KAAK6F,kBAAkB,CAAC;EAC3D,IAAI,CAACC,yBAAyB,EAAE;IAC9B,MAAM,IAAIpC,KAAK,CACb,wBAAwBmC,kBAAkB,qCAAqCV,mBAAmB,GACpG,CAAC;EACH;EACA,OAAOW,yBAAyB;AAClC;AAEO,SAASJ,aAAaA,CAAC,GAAGM,WAAW,CAA4B,EAAW;EACjF,OAAOA,WAAW,CAACC,GAAG,KAAK,sBAAsB;AACnD;AAEO,SAASC,aAAaA,CAAC,GAAGF,WAAW,CAA4B,EAAW;EACjF,OAAO,CAACA,WAAW,CAACG,aAAa,CAACC,SAAS;AAC7C;AAEO,SAASnB,YAAYA,CAAC,CAACK,GAAG,CAIL,EAAW;EACrC,OAAO,CAACA,GAAG,CAACe,QAAQ,CAAC,UAAU,CAAC;AAClC;;AAEA;AACO,SAASC,OAAOA,CAAC7D,KAAa,EAAU;EAC7C;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7BA,KAAK,GAAGE,MAAM,CAACF,KAAK,CAAC;EACvB;EACA,OAAOA,KAAK,CAAC8D,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI9D,KAAK;AAC9C;AAEO,SAAS+D,wBAAwBA,CACtC/D,KAAa,EACbgE,MAAoD,EAC5C;EACR,MAAMC,WAAW,GAAGjE,KAAK,EAAEtC,OAAO,CAAC,2BAA2B,EAAGoG,KAAK,IAAK;IACzE;IACA,MAAM,CAACI,QAAQ,EAAE,GAAGC,eAAe,CAAC,GAAGL,KAAK,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC9D,KAAK,CAAC,GAAG,CAAC;IACpE;IACA,IAAI+D,QAAQ,GAAGL,MAAM,CAACE,QAAQ,CAAC;IAC/B,IAAIG,QAAQ,EAAE;MACZA,QAAQ,GAAGN,wBAAwB,CAACM,QAAQ,EAAEL,MAAM,CAAC;IACvD;IACA,IAAIM,QAAQ,GAAGD,QAAQ;;IAEvB;IACAF,eAAe,CAACI,OAAO,CAAEC,QAAQ,IAAK;MACpC,QAAQA,QAAQ;QACd,KAAK,OAAO;UACV;UACAF,QAAQ,GAAGA,QAAQ,EAAEG,WAAW,CAAC,CAAC;UAClC;QACF,KAAK,OAAO;UACV;UACAH,QAAQ,GAAGA,QAAQ,EAAEI,WAAW,CAAC,CAAC;UAClC;QACF,KAAK,QAAQ;UACX,IAAIJ,QAAQ,EAAE;YACZ;YACAA,QAAQ,GAAGrH,eAAI,CAAC0H,OAAO,CAACL,QAAQ,CAAC;UACnC;UACA;QACF,KAAK,MAAM;UACT,IAAIA,QAAQ,EAAE;YACZ;YACAA,QAAQ,GAAGrH,eAAI,CAACC,QAAQ,CAACoH,QAAQ,CAAC;UACpC;UACA;QACF,KAAK,KAAK;UACR,IAAIA,QAAQ,EAAE;YACZ;YACAA,QAAQ,GAAGrH,eAAI,CAAC2H,OAAO,CAACN,QAAQ,CAAC;UACnC;UACA;QACF,KAAK,MAAM;UACT,IAAIA,QAAQ,EAAE;YACZ;YACA,MAAMO,CAAC,GAAG5H,eAAI,CAACC,QAAQ,CAACoH,QAAQ,CAAC;YACjC,MAAMQ,cAAc,GAAGD,CAAC,CAACE,WAAW,CAAC,GAAG,CAAC;YACzCT,QAAQ,GAAGQ,cAAc,KAAK,CAAC,CAAC,GAAGD,CAAC,GAAGA,CAAC,CAACT,KAAK,CAAC,CAAC,EAAEU,cAAc,CAAC;UACnE;UACA;QACF,KAAK,mBAAmB;UACtB;;UAEA;UACAR,QAAQ,GAAGA,QAAQ,EAAE5G,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;UAClD;UACA;QACF,KAAK,kBAAkB;UACrB;UACA;UACA4G,QAAQ,GAAGA,QAAQ,EAAE5G,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;UAC3C;QACF,KAAK,iBAAiB;UACpB,IAAI4G,QAAQ,EAAE;YACZ;YACA;YACAA,QAAQ,GAAGrH,eAAI,CAAC+H,OAAO,CAACV,QAAQ,CAAC;UACnC;UACA;QACF;UACEA,QAAQ,KAAKE,QAAQ,CAACV,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;UAChD;MACJ;IACF,CAAC,CAAC;IAEF,OAAOC,wBAAwB,CAACO,QAAQ,IAAI,EAAE,EAAEN,MAAM,CAAC;EACzD,CAAC,CAAC;EAEF,IAAIC,WAAW,KAAKjE,KAAK,EAAE;IACzB,OAAO+D,wBAAwB,CAACE,WAAW,EAAED,MAAM,CAAC;EACtD;EACA,OAAOhE,KAAK;AACd", "ignoreList": []}