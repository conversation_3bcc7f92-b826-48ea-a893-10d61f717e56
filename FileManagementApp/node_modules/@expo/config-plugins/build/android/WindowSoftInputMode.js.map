{"version": 3, "file": "WindowSoftInputMode.js", "names": ["_Manifest", "data", "require", "_androidPlugins", "ANDROID_WINDOW_SOFT_INPUT_MODE", "MAPPING", "pan", "resize", "withWindowSoftInputMode", "config", "withAndroidManifest", "modResults", "setWindowSoftInputModeMode", "exports", "androidManifest", "app", "getMainActivityOrThrow", "$", "getWindowSoftInputModeMode", "value", "android", "softwareKeyboardLayoutMode"], "sources": ["../../src/android/WindowSoftInputMode.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { AndroidManifest, getMainActivityOrThrow } from './Manifest';\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withAndroidManifest } from '../plugins/android-plugins';\n\nconst ANDROID_WINDOW_SOFT_INPUT_MODE = 'android:windowSoftInputMode';\n\nconst MAPPING: Record<string, string> = {\n  pan: 'adjustPan',\n  resize: 'adjustResize',\n};\n\nexport const withWindowSoftInputMode: ConfigPlugin = (config) => {\n  return withAndroidManifest(config, async (config) => {\n    config.modResults = setWindowSoftInputModeMode(config, config.modResults);\n    return config;\n  });\n};\n\nexport function setWindowSoftInputModeMode(\n  config: Pick<ExpoConfig, 'android' | 'userInterfaceStyle'>,\n  androidManifest: AndroidManifest\n) {\n  const app = getMainActivityOrThrow(androidManifest);\n  app.$[ANDROID_WINDOW_SOFT_INPUT_MODE] = getWindowSoftInputModeMode(config);\n  return androidManifest;\n}\n\nexport function getWindowSoftInputModeMode(config: Pick<ExpoConfig, 'android'>) {\n  const value = config.android?.softwareKeyboardLayoutMode;\n\n  if (!value) {\n    // Default to `adjustResize` or `resize`.\n    return 'adjustResize';\n  }\n  return MAPPING[value] ?? value;\n}\n"], "mappings": ";;;;;;;;AAEA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,gBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,eAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAMG,8BAA8B,GAAG,6BAA6B;AAEpE,MAAMC,OAA+B,GAAG;EACtCC,GAAG,EAAE,WAAW;EAChBC,MAAM,EAAE;AACV,CAAC;AAEM,MAAMC,uBAAqC,GAAIC,MAAM,IAAK;EAC/D,OAAO,IAAAC,qCAAmB,EAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IACnDA,MAAM,CAACE,UAAU,GAAGC,0BAA0B,CAACH,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IACzE,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACI,OAAA,CAAAL,uBAAA,GAAAA,uBAAA;AAEK,SAASI,0BAA0BA,CACxCH,MAA0D,EAC1DK,eAAgC,EAChC;EACA,MAAMC,GAAG,GAAG,IAAAC,kCAAsB,EAACF,eAAe,CAAC;EACnDC,GAAG,CAACE,CAAC,CAACb,8BAA8B,CAAC,GAAGc,0BAA0B,CAACT,MAAM,CAAC;EAC1E,OAAOK,eAAe;AACxB;AAEO,SAASI,0BAA0BA,CAACT,MAAmC,EAAE;EAC9E,MAAMU,KAAK,GAAGV,MAAM,CAACW,OAAO,EAAEC,0BAA0B;EAExD,IAAI,CAACF,KAAK,EAAE;IACV;IACA,OAAO,cAAc;EACvB;EACA,OAAOd,OAAO,CAACc,KAAK,CAAC,IAAIA,KAAK;AAChC", "ignoreList": []}