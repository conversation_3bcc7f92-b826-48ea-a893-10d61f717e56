{"version": 3, "file": "codeMod.js", "names": ["_commonCodeMod", "data", "require", "_matchBrackets", "findNewInstanceCodeBlock", "contents", "classDeclaration", "language", "isJava", "start", "indexOf", "search", "RegExp", "end", "findMatchingBracketPosition", "nextBrace", "isAnonymousClass", "substring", "match", "code", "appendContentsInsideDeclarationBlock", "srcContents", "declaration", "insertion", "Error", "insertContentsAtOffset", "addImports", "source", "imports", "lines", "split", "lineIndexWithPackageDeclaration", "findIndex", "line", "javaImport", "includes", "importStatement", "splice", "join", "findGradlePluginCodeBlock", "plugin", "pluginStart", "codeBlockStart", "codeBlockEnd", "codeBlock", "appendContentsInsideGradlePluginBlock"], "sources": ["../../src/android/codeMod.ts"], "sourcesContent": ["import { CodeBlock, insertContentsAtOffset } from '../utils/commonCodeMod';\nimport { findMatchingBracketPosition } from '../utils/matchBrackets';\n\n/**\n * Find java or kotlin new class instance code block\n *\n * @param contents source contents\n * @param classDeclaration class declaration or just a class name\n * @param language 'java' | 'kt'\n * @returns `CodeBlock` for start/end offset and code block contents\n */\nexport function findNewInstanceCodeBlock(\n  contents: string,\n  classDeclaration: string,\n  language: 'java' | 'kt'\n): CodeBlock | null {\n  const isJava = language === 'java';\n  let start = isJava\n    ? contents.indexOf(` new ${classDeclaration}(`)\n    : contents.search(new RegExp(` (object\\\\s*:\\\\s*)?${classDeclaration}\\\\(`));\n  if (start < 0) {\n    return null;\n  }\n  // `+ 1` for the prefix space\n  start += 1;\n  let end = findMatchingBracketPosition(contents, '(', start);\n\n  // For anonymous class, should search further to the {} block.\n  // ```java\n  // new Foo() {\n  //   @Override\n  //   protected void interfaceMethod {}\n  // };\n  // ```\n  //\n  // ```kotlin\n  // object : Foo() {\n  //   override fun interfaceMethod {}\n  // }\n  // ```\n  const nextBrace = contents.indexOf('{', end + 1);\n  const isAnonymousClass =\n    nextBrace >= end && !!contents.substring(end + 1, nextBrace).match(/^\\s*$/);\n  if (isAnonymousClass) {\n    end = findMatchingBracketPosition(contents, '{', end);\n  }\n\n  return {\n    start,\n    end,\n    code: contents.substring(start, end + 1),\n  };\n}\n\n/**\n * Append contents to the end of code declaration block, support class or method declarations.\n *\n * @param srcContents source contents\n * @param declaration class declaration or method declaration\n * @param insertion code to append\n * @returns updated contents\n */\nexport function appendContentsInsideDeclarationBlock(\n  srcContents: string,\n  declaration: string,\n  insertion: string\n): string {\n  const start = srcContents.search(new RegExp(`\\\\s*${declaration}.*?[\\\\(\\\\{]`));\n  if (start < 0) {\n    throw new Error(`Unable to find code block - declaration[${declaration}]`);\n  }\n  const end = findMatchingBracketPosition(srcContents, '{', start);\n  return insertContentsAtOffset(srcContents, insertion, end);\n}\n\nexport function addImports(source: string, imports: string[], isJava: boolean): string {\n  const lines = source.split('\\n');\n  const lineIndexWithPackageDeclaration = lines.findIndex((line) => line.match(/^package .*;?$/));\n  for (const javaImport of imports) {\n    if (!source.includes(javaImport)) {\n      const importStatement = `import ${javaImport}${isJava ? ';' : ''}`;\n      lines.splice(lineIndexWithPackageDeclaration + 1, 0, importStatement);\n    }\n  }\n  return lines.join('\\n');\n}\n\n/**\n * Find code block of Gradle plugin block, will return only {} part\n *\n * @param contents source contents\n * @param plugin plugin declaration name, e.g. `plugins` or `pluginManagement`\n * @returns found CodeBlock, or null if not found.\n */\nexport function findGradlePluginCodeBlock(contents: string, plugin: string): CodeBlock | null {\n  const pluginStart = contents.search(new RegExp(`${plugin}\\\\s*\\\\{`, 'm'));\n  if (pluginStart < 0) {\n    return null;\n  }\n  const codeBlockStart = contents.indexOf('{', pluginStart);\n  const codeBlockEnd = findMatchingBracketPosition(contents, '{', codeBlockStart);\n  const codeBlock = contents.substring(codeBlockStart, codeBlockEnd + 1);\n  return {\n    start: codeBlockStart,\n    end: codeBlockEnd,\n    code: codeBlock,\n  };\n}\n\n/**\n * Append contents to the end of Gradle plugin block\n * @param srcContents source contents\n * @param plugin plugin declaration name, e.g. `plugins` or `pluginManagement`\n * @param insertion code to append\n * @returns updated contents\n */\nexport function appendContentsInsideGradlePluginBlock(\n  srcContents: string,\n  plugin: string,\n  insertion: string\n): string {\n  const codeBlock = findGradlePluginCodeBlock(srcContents, plugin);\n  if (!codeBlock) {\n    throw new Error(`Unable to find Gradle plugin block - plugin[${plugin}]`);\n  }\n  return insertContentsAtOffset(srcContents, insertion, codeBlock.end);\n}\n"], "mappings": ";;;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,eAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,cAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,wBAAwBA,CACtCC,QAAgB,EAChBC,gBAAwB,EACxBC,QAAuB,EACL;EAClB,MAAMC,MAAM,GAAGD,QAAQ,KAAK,MAAM;EAClC,IAAIE,KAAK,GAAGD,MAAM,GACdH,QAAQ,CAACK,OAAO,CAAC,QAAQJ,gBAAgB,GAAG,CAAC,GAC7CD,QAAQ,CAACM,MAAM,CAAC,IAAIC,MAAM,CAAC,sBAAsBN,gBAAgB,KAAK,CAAC,CAAC;EAC5E,IAAIG,KAAK,GAAG,CAAC,EAAE;IACb,OAAO,IAAI;EACb;EACA;EACAA,KAAK,IAAI,CAAC;EACV,IAAII,GAAG,GAAG,IAAAC,4CAA2B,EAACT,QAAQ,EAAE,GAAG,EAAEI,KAAK,CAAC;;EAE3D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMM,SAAS,GAAGV,QAAQ,CAACK,OAAO,CAAC,GAAG,EAAEG,GAAG,GAAG,CAAC,CAAC;EAChD,MAAMG,gBAAgB,GACpBD,SAAS,IAAIF,GAAG,IAAI,CAAC,CAACR,QAAQ,CAACY,SAAS,CAACJ,GAAG,GAAG,CAAC,EAAEE,SAAS,CAAC,CAACG,KAAK,CAAC,OAAO,CAAC;EAC7E,IAAIF,gBAAgB,EAAE;IACpBH,GAAG,GAAG,IAAAC,4CAA2B,EAACT,QAAQ,EAAE,GAAG,EAAEQ,GAAG,CAAC;EACvD;EAEA,OAAO;IACLJ,KAAK;IACLI,GAAG;IACHM,IAAI,EAAEd,QAAQ,CAACY,SAAS,CAACR,KAAK,EAAEI,GAAG,GAAG,CAAC;EACzC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASO,oCAAoCA,CAClDC,WAAmB,EACnBC,WAAmB,EACnBC,SAAiB,EACT;EACR,MAAMd,KAAK,GAAGY,WAAW,CAACV,MAAM,CAAC,IAAIC,MAAM,CAAC,OAAOU,WAAW,aAAa,CAAC,CAAC;EAC7E,IAAIb,KAAK,GAAG,CAAC,EAAE;IACb,MAAM,IAAIe,KAAK,CAAC,2CAA2CF,WAAW,GAAG,CAAC;EAC5E;EACA,MAAMT,GAAG,GAAG,IAAAC,4CAA2B,EAACO,WAAW,EAAE,GAAG,EAAEZ,KAAK,CAAC;EAChE,OAAO,IAAAgB,uCAAsB,EAACJ,WAAW,EAAEE,SAAS,EAAEV,GAAG,CAAC;AAC5D;AAEO,SAASa,UAAUA,CAACC,MAAc,EAAEC,OAAiB,EAAEpB,MAAe,EAAU;EACrF,MAAMqB,KAAK,GAAGF,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC;EAChC,MAAMC,+BAA+B,GAAGF,KAAK,CAACG,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACf,KAAK,CAAC,gBAAgB,CAAC,CAAC;EAC/F,KAAK,MAAMgB,UAAU,IAAIN,OAAO,EAAE;IAChC,IAAI,CAACD,MAAM,CAACQ,QAAQ,CAACD,UAAU,CAAC,EAAE;MAChC,MAAME,eAAe,GAAG,UAAUF,UAAU,GAAG1B,MAAM,GAAG,GAAG,GAAG,EAAE,EAAE;MAClEqB,KAAK,CAACQ,MAAM,CAACN,+BAA+B,GAAG,CAAC,EAAE,CAAC,EAAEK,eAAe,CAAC;IACvE;EACF;EACA,OAAOP,KAAK,CAACS,IAAI,CAAC,IAAI,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,yBAAyBA,CAAClC,QAAgB,EAAEmC,MAAc,EAAoB;EAC5F,MAAMC,WAAW,GAAGpC,QAAQ,CAACM,MAAM,CAAC,IAAIC,MAAM,CAAC,GAAG4B,MAAM,SAAS,EAAE,GAAG,CAAC,CAAC;EACxE,IAAIC,WAAW,GAAG,CAAC,EAAE;IACnB,OAAO,IAAI;EACb;EACA,MAAMC,cAAc,GAAGrC,QAAQ,CAACK,OAAO,CAAC,GAAG,EAAE+B,WAAW,CAAC;EACzD,MAAME,YAAY,GAAG,IAAA7B,4CAA2B,EAACT,QAAQ,EAAE,GAAG,EAAEqC,cAAc,CAAC;EAC/E,MAAME,SAAS,GAAGvC,QAAQ,CAACY,SAAS,CAACyB,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;EACtE,OAAO;IACLlC,KAAK,EAAEiC,cAAc;IACrB7B,GAAG,EAAE8B,YAAY;IACjBxB,IAAI,EAAEyB;EACR,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,qCAAqCA,CACnDxB,WAAmB,EACnBmB,MAAc,EACdjB,SAAiB,EACT;EACR,MAAMqB,SAAS,GAAGL,yBAAyB,CAAClB,WAAW,EAAEmB,MAAM,CAAC;EAChE,IAAI,CAACI,SAAS,EAAE;IACd,MAAM,IAAIpB,KAAK,CAAC,+CAA+CgB,MAAM,GAAG,CAAC;EAC3E;EACA,OAAO,IAAAf,uCAAsB,EAACJ,WAAW,EAAEE,SAAS,EAAEqB,SAAS,CAAC/B,GAAG,CAAC;AACtE", "ignoreList": []}