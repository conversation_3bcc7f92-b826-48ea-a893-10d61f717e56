{"formatVersion": "1.1", "component": {"group": "host.exp.exponent", "module": "expo.modules.webbrowser", "version": "14.2.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.13"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.browser", "module": "browser", "version": {"requires": "1.6.0"}}], "files": [{"name": "expo.modules.webbrowser-14.2.0.aar", "url": "expo.modules.webbrowser-14.2.0.aar", "size": 41508, "sha512": "ea89d28e03b29f8e313609ed37bc69da003c6b6fb03130a6f6d234c8a08fa926bf3c5afb78f1f05fc81c8ad9191cb38a902197ab6446c41212ea346f139ba453", "sha256": "3561520f97f7bc99509e52be1dda5e9f52efff4f0a2520c9d8d40fc02e84d47e", "sha1": "69277bf14b003c9d9646a88355087ea74c3d08c7", "md5": "7e6c5265ae9486a79f5bbe63c820764a"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.0.21"}}, {"group": "androidx.core", "module": "core-ktx", "version": {"requires": "1.13.1"}}, {"group": "androidx.browser", "module": "browser", "version": {"requires": "1.6.0"}}], "files": [{"name": "expo.modules.webbrowser-14.2.0.aar", "url": "expo.modules.webbrowser-14.2.0.aar", "size": 41508, "sha512": "ea89d28e03b29f8e313609ed37bc69da003c6b6fb03130a6f6d234c8a08fa926bf3c5afb78f1f05fc81c8ad9191cb38a902197ab6446c41212ea346f139ba453", "sha256": "3561520f97f7bc99509e52be1dda5e9f52efff4f0a2520c9d8d40fc02e84d47e", "sha1": "69277bf14b003c9d9646a88355087ea74c3d08c7", "md5": "7e6c5265ae9486a79f5bbe63c820764a"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.webbrowser-14.2.0-sources.jar", "url": "expo.modules.webbrowser-14.2.0-sources.jar", "size": 6291, "sha512": "cb205a40f7a6a699303dfb8283471b9c20b8f9a98e3f3be3a386b19e2cc71a6742f11900988303751b03ec3c936c489e41062404023fef8c9a5d93664ae2bc0b", "sha256": "226ce0c4b274d09771d41e3e1fb1fee44cebb87a5c8c5304ca799f6889be9e50", "sha1": "badf641b53a4fc051795aaa430b0a119b85a3dab", "md5": "597229c8acbdb0cb66a8cc67bee5d3e2"}]}]}