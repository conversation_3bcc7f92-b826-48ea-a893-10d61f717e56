{"version": 3, "file": "ExpoWebBrowser.web.js", "sourceRoot": "", "sources": ["../src/ExpoWebBrowser.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAGxC,OAAO,EAIL,oBAAoB,GAErB,MAAM,oBAAoB,CAAC;AAE5B,MAAM,WAAW,GAAG,GAAG,CAAC;AACxB,MAAM,YAAY,GAAG,GAAG,CAAC;AAEzB,IAAI,WAAW,GAAkB,IAAI,CAAC;AAEtC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;AAE9B,MAAM,SAAS,GAAG,GAAG,EAAE,CAAC,8BAA8B,CAAC;AACvD,MAAM,kBAAkB,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,4BAA4B,IAAI,EAAE,CAAC;AAChF,MAAM,oBAAoB,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,8BAA8B,IAAI,EAAE,CAAC;AAEpF,MAAM,UAAU,YAAY,CAAC,GAAmB;IAC9C,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IAC5F,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;AAClF,CAAC;AAED,SAAS,YAAY;IACnB,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,OAAO;IACT,CAAC;IACD,WAAW,CAAC,KAAK,EAAE,CAAC;IACpB,IAAI,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;QACjC,MAAM,EAAE,QAAQ,EAAE,oBAAoB,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAClF,aAAa,CAAC,QAAQ,CAAC,CAAC;QACxB,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC/C,oBAAgD,CAAC,MAAM,EAAE,CAAC;QAC3D,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAEhC,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QACxD,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC;YAC5C,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,WAAW,GAAG,IAAI,CAAC;IACrB,CAAC;AACH,CAAC;AAED,eAAe;IACb,KAAK,CAAC,gBAAgB,CACpB,GAAW,EACX,gBAAuC,EAAE;QAEzC,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO,EAAE,IAAI,EAAE,oBAAoB,CAAC,MAAM,EAAE,CAAC;QAC/C,CAAC;QACD,MAAM,EAAE,UAAU,GAAG,QAAQ,EAAE,cAAc,EAAE,GAAG,aAAa,CAAC;QAChE,MAAM,QAAQ,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;QACxD,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QACvC,OAAO,EAAE,IAAI,EAAE,oBAAoB,CAAC,MAAM,EAAE,CAAC;IAC/C,CAAC;IACD,kBAAkB;QAChB,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO;QAC1C,YAAY,EAAE,CAAC;IACjB,CAAC;IACD,wBAAwB,CAAC,EAAE,iBAAiB,EAAmC;QAI7E,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,0DAA0D;aACpE,CAAC;QACJ,CAAC;QACD,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QAExD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,0CAA0C,EAAE,CAAC;QACjF,CAAC;QAED,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QAEjC,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;YAC/B,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC;YAC9E,4FAA4F;YAC5F,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,WAAW,KAAK,UAAU,EAAE,CAAC;gBAC/B,OAAO;oBACL,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,gBAAgB,UAAU,gCAAgC,WAAW,iBAAiB;iBAChG,CAAC;YACJ,CAAC;QACH,CAAC;QAED,uCAAuC;QACvC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;QAE7D,gDAAgD;QAChD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,UAAU,CAClB,0BAA0B,EAC1B,yKAAyK,CAC1K,CAAC;QACJ,CAAC;QACD,2CAA2C;QAC3C,MAAM,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC5E,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;QAEnE,8FAA8F;IAChG,CAAC;IACD,iDAAiD;IACjD,KAAK,CAAC,oBAAoB,CACxB,GAAW,EACX,WAAoB,EACpB,WAAmC;QAEnC,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO,EAAE,IAAI,EAAE,oBAAoB,CAAC,MAAM,EAAE,CAAC;QAEhF,WAAW,GAAG,WAAW,IAAI,+BAA+B,CAAC,GAAG,CAAC,CAAC;QAElE,IAAI,WAAW,IAAI,IAAI,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAG,sBAAsB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACrE,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YAElE,IAAI,WAAW,EAAE,CAAC;gBAChB,IAAI,CAAC;oBACH,WAAW,CAAC,KAAK,EAAE,CAAC;gBACtB,CAAC;gBAAC,MAAM,CAAC,CAAA,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,UAAU,CAClB,yBAAyB,EACzB,gLAAgL,CACjL,CAAC;YACJ,CAAC;QACH,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,8BAA8B,CAAC,GAAG,CAAC,CAAC;QAExD,0BAA0B;QAC1B,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;QAEhD,MAAM,qBAAqB,GAAG,CAAC,GAAG,EAAE;YAClC,IAAI,CAAC,WAAW;gBAAE,OAAO,WAAW,CAAC;YACrC,IAAI,CAAC;gBACH,OAAO,YAAY,CAAC,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;YAC5C,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,WAAW,CAAC;YACrB,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,6CAA6C;QAC7C,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,qBAAqB,CAAC,CAAC;QAEhF,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACnC,qDAAqD;YACrD,MAAM,QAAQ,GAAG,CAAC,KAAmB,EAAE,EAAE;gBACvC,IAAI,CAAC,KAAK,CAAC,SAAS;oBAAE,OAAO;gBAC7B,8BAA8B;gBAC9B,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;oBAC5C,OAAO;gBACT,CAAC;gBACD,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;gBACvB,wCAAwC;gBACxC,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;gBACxD,kDAAkD;gBAClD,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;oBAC/B,YAAY,EAAE,CAAC;oBACf,OAAO,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC9C,CAAC;YACH,CAAC,CAAC;YAEF,wDAAwD;YACxD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEpD,mEAAmE;YACnE,MAAM,gBAAgB,GAAG,CAAC,KAAqB,EAAE,EAAE;gBACjD,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;oBACvB,OAAO;gBACT,CAAC;gBACD,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;gBACxD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;oBACpE,IAAI,GAAG,EAAE,CAAC;wBACR,YAAY,EAAE,CAAC;wBACf,OAAO,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;oBACpC,CAAC;gBACH,CAAC;YACH,CAAC,CAAC;YAEF,MAAM,oBAAoB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YAEnF,oDAAoD;YACpD,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;gBAChC,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;oBACxB,IAAI,OAAO;wBAAE,OAAO,CAAC,EAAE,IAAI,EAAE,oBAAoB,CAAC,OAAO,EAAE,CAAC,CAAC;oBAC7D,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,YAAY,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC,EAAE,IAAI,CAAC,CAAC;YAET,gDAAgD;YAChD,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE;gBAC3B,QAAQ;gBACR,QAAQ;gBACR,oBAAoB;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAC;AAEF,SAAS;AACT,SAAS,iBAAiB;IACxB,IAAI,OAAO,MAAM,KAAK,WAAW;QAAE,OAAO,KAAK,CAAC;IAChD,OAAO,CAAC,CAAE,MAAM,EAAE,MAAc,CAAC;AACnC,CAAC;AAED,SAAS,uBAAuB;IAC9B,IAAI,CAAC,iBAAiB,EAAE;QAAE,OAAO,KAAK,CAAC;IACvC,OAAO,CAAC,CAAE,MAAM,CAAC,MAAM,CAAC,MAAc,CAAC;AACzC,CAAC;AAED,KAAK,UAAU,8BAA8B,CAAC,QAAgB;IAC5D,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC9B,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,OAAO,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;QACvF,oDAAoD;QACpD,OAAO,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;IACxC,CAAC;IACD,0DAA0D;IAC1D,OAAO,MAAM,kBAAkB,EAAE,CAAC;AACpC,CAAC;AAED,SAAS,+BAA+B,CAAC,QAAgB;IACvD,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC9B,IACE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC;QACpC,OAAO,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,QAAQ,EACxD,CAAC;QACD,oEAAoE;QACpE,OAAO,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAE,CAAC;IAC/C,CAAC;IACD,+CAA+C;IAC/C,OAAO,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC;AAC7C,CAAC;AAED,MAAM,OAAO,GAAG,gEAAgE,CAAC;AAEjF,KAAK,UAAU,kBAAkB;IAC/B,IAAI,CAAC,uBAAuB,EAAE,EAAE,CAAC;QAC/B,MAAM,IAAI,UAAU,CAClB,wBAAwB,EACxB,gHAAgH,CACjH,CAAC;IACJ,CAAC;IACD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAElC,MAAM,IAAI,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC;IAChC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACpC,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;IACjE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACvE,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,cAAc,CAAC,IAAY;IAClC,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;IAC/B,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC;QAClC,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IACD,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACzC,IAAI,iBAAiB,EAAE,EAAE,CAAC;QACxB,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;SAAM,CAAC;QACN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IACD,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,cAAc,CAAC,MAAkB;IACxC,IAAI,KAAK,GAAW,EAAE,CAAC;IACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;QACzC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,kBAAkB;AAElB,qCAAqC;AACrC,SAAS,4BAA4B,CACnC,OAA2C;IAE3C,IAAI,cAAc,GAAwB,EAAE,CAAC;IAC7C,0EAA0E;IAC1E,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAChC,uDAAuD;QACvD,MAAM,kBAAkB,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9C,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE,CAAC;YACtC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5C,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;gBACjB,cAAc,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;SAAM,IAAI,OAAO,EAAE,CAAC;QACnB,cAAc,GAAG,OAAO,CAAC;IAC3B,CAAC;IACD,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,gDAAgD;AAChD,SAAS,sBAAsB,CAAC,OAA2C;IACzE,MAAM,cAAc,GAAG,4BAA4B,CAAC,OAAO,CAAC,CAAC;IAE7D,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,IAAI,WAAW,CAAC;IAClD,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,IAAI,YAAY,CAAC;IAErD,MAAM,GAAG,GAAG,cAAc,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;IACrF,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;IAErF,4BAA4B;IAC5B,+EAA+E;IAC/E,OAAO,qBAAqB,CAAC;QAC3B,GAAG,cAAc;QACjB,yDAAyD;QACzD,OAAO,EAAE,cAAc,CAAC,OAAO,IAAI,IAAI;QACvC,OAAO,EAAE,cAAc,CAAC,OAAO,IAAI,IAAI;QACvC,6CAA6C;QAC7C,QAAQ,EAAE,cAAc,CAAC,QAAQ,IAAI,KAAK;QAC1C,SAAS,EAAE,cAAc,CAAC,SAAS,IAAI,KAAK;QAC5C,yEAAyE;QACzE,MAAM,EAAE,cAAc,CAAC,MAAM,IAAI,IAAI;QACrC,UAAU,EAAE,cAAc,CAAC,UAAU,IAAI,KAAK;QAC9C,GAAG;QACH,IAAI;QACJ,KAAK;QACL,MAAM;KACP,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAC,QAA6B;IACjE,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAS,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE;QAC5D,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC9B,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;YAC/B,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/B,CAAC;QACD,IAAI,OAAO,IAAI,KAAK,EAAE,CAAC;YACrB,IAAI,IAAI;gBAAE,IAAI,IAAI,GAAG,CAAC;YACtB,OAAO,GAAG,IAAI,GAAG,OAAO,IAAI,KAAK,EAAE,CAAC;QACtC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC", "sourcesContent": ["import { CodedError } from 'expo-modules-core';\nimport { AppState } from 'react-native';\nimport type { AppStateStatus, NativeEventSubscription } from 'react-native';\n\nimport {\n  WebBrowserAuthSessionResult,\n  WebBrowserOpenOptions,\n  WebBrowserResult,\n  WebBrowserResultType,\n  WebBrowserWindowFeatures,\n} from './WebBrowser.types';\n\nconst POPUP_WIDTH = 500;\nconst POPUP_HEIGHT = 650;\n\nlet popupWindow: Window | null = null;\n\nconst listenerMap = new Map();\n\nconst getHandle = () => 'ExpoWebBrowserRedirectHandle';\nconst getOriginUrlHandle = (hash: string) => `ExpoWebBrowser_OriginUrl_${hash}`;\nconst getRedirectUrlHandle = (hash: string) => `ExpoWebBrowser_RedirectUrl_${hash}`;\n\nexport function normalizeUrl(url: URL | Location) {\n  const origin = url.origin.replace(url.protocol, '').replace(/^\\/+/, '').replace(/\\/+$/, '');\n  return (origin + decodeURI(url.pathname.replace(/\\/{2,}/g, '/'))).toLowerCase();\n}\n\nfunction dismissPopup() {\n  if (!popupWindow) {\n    return;\n  }\n  popupWindow.close();\n  if (listenerMap.has(popupWindow)) {\n    const { listener, appStateSubscription, interval } = listenerMap.get(popupWindow);\n    clearInterval(interval);\n    window.removeEventListener('message', listener);\n    (appStateSubscription as NativeEventSubscription).remove();\n    listenerMap.delete(popupWindow);\n\n    const handle = window.localStorage.getItem(getHandle());\n    if (handle) {\n      window.localStorage.removeItem(getHandle());\n      window.localStorage.removeItem(getOriginUrlHandle(handle));\n      window.localStorage.removeItem(getRedirectUrlHandle(handle));\n    }\n\n    popupWindow = null;\n  }\n}\n\nexport default {\n  async openBrowserAsync(\n    url: string,\n    browserParams: WebBrowserOpenOptions = {}\n  ): Promise<WebBrowserResult> {\n    if (typeof window === 'undefined') {\n      return { type: WebBrowserResultType.CANCEL };\n    }\n    const { windowName = '_blank', windowFeatures } = browserParams;\n    const features = getPopupFeaturesString(windowFeatures);\n    window.open(url, windowName, features);\n    return { type: WebBrowserResultType.OPENED };\n  },\n  dismissAuthSession() {\n    if (typeof window === 'undefined') return;\n    dismissPopup();\n  },\n  maybeCompleteAuthSession({ skipRedirectCheck }: { skipRedirectCheck?: boolean }): {\n    type: 'success' | 'failed';\n    message: string;\n  } {\n    if (typeof window === 'undefined') {\n      return {\n        type: 'failed',\n        message: 'Cannot use expo-web-browser in a non-browser environment',\n      };\n    }\n    const handle = window.localStorage.getItem(getHandle());\n\n    if (!handle) {\n      return { type: 'failed', message: 'No auth session is currently in progress' };\n    }\n\n    const url = window.location.href;\n\n    if (skipRedirectCheck !== true) {\n      const redirectUrl = window.localStorage.getItem(getRedirectUrlHandle(handle));\n      // Compare the original redirect url against the current url with it's query params removed.\n      const currentUrl = normalizeUrl(window.location);\n      if (redirectUrl !== currentUrl) {\n        return {\n          type: 'failed',\n          message: `Current URL \"${currentUrl}\" and original redirect URL \"${redirectUrl}\" do not match.`,\n        };\n      }\n    }\n\n    // Save the link for app state listener\n    window.localStorage.setItem(getOriginUrlHandle(handle), url);\n\n    // Get the window that created the current popup\n    const parent = window.opener ?? window.parent;\n    if (!parent) {\n      throw new CodedError(\n        'ERR_WEB_BROWSER_REDIRECT',\n        `The window cannot complete the redirect request because the invoking window doesn't have a reference to it's parent. This can happen if the parent window was reloaded.`\n      );\n    }\n    // Send the URL back to the opening window.\n    parent.postMessage({ url, expoSender: handle }, parent.location.toString());\n    return { type: 'success', message: `Attempting to complete auth` };\n\n    // Maybe set timer to throw an error if the window is still open after attempting to complete.\n  },\n  // This method should be invoked from user input.\n  async openAuthSessionAsync(\n    url: string,\n    redirectUrl?: string,\n    openOptions?: WebBrowserOpenOptions\n  ): Promise<WebBrowserAuthSessionResult> {\n    if (typeof window === 'undefined') return { type: WebBrowserResultType.CANCEL };\n\n    redirectUrl = redirectUrl ?? getRedirectUrlFromUrlOrGenerate(url);\n\n    if (popupWindow == null || popupWindow?.closed) {\n      const features = getPopupFeaturesString(openOptions?.windowFeatures);\n      popupWindow = window.open(url, openOptions?.windowName, features);\n\n      if (popupWindow) {\n        try {\n          popupWindow.focus();\n        } catch {}\n      } else {\n        throw new CodedError(\n          'ERR_WEB_BROWSER_BLOCKED',\n          'Popup window was blocked by the browser or failed to open. This can happen in mobile browsers when the window.open() method was invoked too long after a user input was fired.'\n        );\n      }\n    }\n\n    const state = await getStateFromUrlOrGenerateAsync(url);\n\n    // Save handle for session\n    window.localStorage.setItem(getHandle(), state);\n\n    const normalizedRedirectUrl = (() => {\n      if (!redirectUrl) return redirectUrl;\n      try {\n        return normalizeUrl(new URL(redirectUrl));\n      } catch {\n        return redirectUrl;\n      }\n    })();\n\n    // Save redirect Url for further verification\n    window.localStorage.setItem(getRedirectUrlHandle(state), normalizedRedirectUrl);\n\n    return new Promise(async (resolve) => {\n      // Create a listener for messages sent from the popup\n      const listener = (event: MessageEvent) => {\n        if (!event.isTrusted) return;\n        // Ensure we trust the sender.\n        if (event.origin !== window.location.origin) {\n          return;\n        }\n        const { data } = event;\n        // Use a crypto hash to invalid message.\n        const handle = window.localStorage.getItem(getHandle());\n        // Ensure the sender is also from expo-web-browser\n        if (data.expoSender === handle) {\n          dismissPopup();\n          resolve({ type: 'success', url: data.url });\n        }\n      };\n\n      // Add a listener for receiving messages from the popup.\n      window.addEventListener('message', listener, false);\n\n      // Create an app state listener as a fallback to the popup listener\n      const appStateListener = (state: AppStateStatus) => {\n        if (state !== 'active') {\n          return;\n        }\n        const handle = window.localStorage.getItem(getHandle());\n        if (handle) {\n          const url = window.localStorage.getItem(getOriginUrlHandle(handle));\n          if (url) {\n            dismissPopup();\n            resolve({ type: 'success', url });\n          }\n        }\n      };\n\n      const appStateSubscription = AppState.addEventListener('change', appStateListener);\n\n      // Check if the window has been closed every second.\n      const interval = setInterval(() => {\n        if (popupWindow?.closed) {\n          if (resolve) resolve({ type: WebBrowserResultType.DISMISS });\n          clearInterval(interval);\n          dismissPopup();\n        }\n      }, 1000);\n\n      // Store the listener and interval for clean up.\n      listenerMap.set(popupWindow, {\n        listener,\n        interval,\n        appStateSubscription,\n      });\n    });\n  },\n};\n\n// Crypto\nfunction isCryptoAvailable(): boolean {\n  if (typeof window === 'undefined') return false;\n  return !!(window?.crypto as any);\n}\n\nfunction isSubtleCryptoAvailable(): boolean {\n  if (!isCryptoAvailable()) return false;\n  return !!(window.crypto.subtle as any);\n}\n\nasync function getStateFromUrlOrGenerateAsync(inputUrl: string): Promise<string> {\n  const url = new URL(inputUrl);\n  if (url.searchParams.has('state') && typeof url.searchParams.get('state') === 'string') {\n    // Ensure we reuse the auth state if it's passed in.\n    return url.searchParams.get('state')!;\n  }\n  // Generate a crypto state for verifying the return popup.\n  return await generateStateAsync();\n}\n\nfunction getRedirectUrlFromUrlOrGenerate(inputUrl: string): string {\n  const url = new URL(inputUrl);\n  if (\n    url.searchParams.has('redirect_uri') &&\n    typeof url.searchParams.get('redirect_uri') === 'string'\n  ) {\n    // Ensure we reuse the redirect_uri if it's passed in the input url.\n    return url.searchParams.get('redirect_uri')!;\n  }\n  // Emulate how native uses Constants.linkingUrl\n  return location.origin + location.pathname;\n}\n\nconst CHARSET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n\nasync function generateStateAsync(): Promise<string> {\n  if (!isSubtleCryptoAvailable()) {\n    throw new CodedError(\n      'ERR_WEB_BROWSER_CRYPTO',\n      `The current environment doesn't support crypto. Ensure you are running from a secure origin (localhost/https).`\n    );\n  }\n  const encoder = new TextEncoder();\n\n  const data = generateRandom(10);\n  const buffer = encoder.encode(data);\n  const hashedData = await crypto.subtle.digest('SHA-256', buffer);\n  const state = btoa(String.fromCharCode(...new Uint8Array(hashedData)));\n  return state;\n}\n\nfunction generateRandom(size: number): string {\n  let arr = new Uint8Array(size);\n  if (arr.byteLength !== arr.length) {\n    arr = new Uint8Array(arr.buffer);\n  }\n  const array = new Uint8Array(arr.length);\n  if (isCryptoAvailable()) {\n    window.crypto.getRandomValues(array);\n  } else {\n    for (let i = 0; i < size; i += 1) {\n      array[i] = (Math.random() * CHARSET.length) | 0;\n    }\n  }\n  return bufferToString(array);\n}\n\nfunction bufferToString(buffer: Uint8Array): string {\n  let state: string = '';\n  for (let i = 0; i < buffer.byteLength; i += 1) {\n    const index = buffer[i] % CHARSET.length;\n    state += CHARSET[index];\n  }\n  return state;\n}\n\n// Window Features\n\n// Ensure feature string is an object\nfunction normalizePopupFeaturesString(\n  options?: WebBrowserWindowFeatures | string\n): Record<string, any> {\n  let windowFeatures: Record<string, any> = {};\n  // This should be avoided because it adds extra time to the popup command.\n  if (typeof options === 'string') {\n    // Convert string of `key=value,foo=bar` into an object\n    const windowFeaturePairs = options.split(',');\n    for (const pair of windowFeaturePairs) {\n      const [key, value] = pair.trim().split('=');\n      if (key && value) {\n        windowFeatures[key] = value;\n      }\n    }\n  } else if (options) {\n    windowFeatures = options;\n  }\n  return windowFeatures;\n}\n\n// Apply default values to the input feature set\nfunction getPopupFeaturesString(options?: WebBrowserWindowFeatures | string): string {\n  const windowFeatures = normalizePopupFeaturesString(options);\n\n  const width = windowFeatures.width ?? POPUP_WIDTH;\n  const height = windowFeatures.height ?? POPUP_HEIGHT;\n\n  const top = windowFeatures.top ?? Math.max(0, (window.screen.height - height) * 0.5);\n  const left = windowFeatures.left ?? Math.max(0, (window.screen.width - width) * 0.5);\n\n  // Create a reasonable popup\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/open#Window_features\n  return featureObjectToString({\n    ...windowFeatures,\n    // Toolbar buttons (Back, Forward, Reload, Stop buttons).\n    toolbar: windowFeatures.toolbar ?? 'no',\n    menubar: windowFeatures.menubar ?? 'no',\n    // Shows the location bar or the address bar.\n    location: windowFeatures.location ?? 'yes',\n    resizable: windowFeatures.resizable ?? 'yes',\n    // If this feature is on, then the new secondary window has a status bar.\n    status: windowFeatures.status ?? 'no',\n    scrollbars: windowFeatures.scrollbars ?? 'yes',\n    top,\n    left,\n    width,\n    height,\n  });\n}\n\nexport function featureObjectToString(features: Record<string, any>): string {\n  return Object.keys(features).reduce<string>((prev, current) => {\n    let value = features[current];\n    if (typeof value === 'boolean') {\n      value = value ? 'yes' : 'no';\n    }\n    if (current && value) {\n      if (prev) prev += ',';\n      return `${prev}${current}=${value}`;\n    }\n    return prev;\n  }, '');\n}\n"]}