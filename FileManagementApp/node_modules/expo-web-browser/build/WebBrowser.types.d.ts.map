{"version": 3, "file": "WebBrowser.types.d.ts", "sourceRoot": "", "sources": ["../src/WebBrowser.types.ts"], "names": [], "mappings": "AAAA,MAAM,MAAM,aAAa,GAAG;IAC1B,GAAG,EAAE,MAAM,CAAC;CACb,CAAC;AAGF,MAAM,MAAM,wBAAwB,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC;AAGjF,MAAM,MAAM,qBAAqB,GAAG;IAClC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;;;OAIG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB;;OAEG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B;;;OAGG;IACH,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B;;;OAGG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;;OAGG;IACH,0BAA0B,CAAC,EAAE,OAAO,CAAC;IACrC;;;;;OAKG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB;;;;;OAKG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB;;;OAGG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB;;;OAGG;IACH,kBAAkB,CAAC,EAAE,MAAM,GAAG,OAAO,GAAG,QAAQ,CAAC;IACjD;;;OAGG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB;;;;;OAKG;IACH,iBAAiB,CAAC,EAAE,2BAA2B,CAAC;IAChD;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB;;;OAGG;IACH,cAAc,CAAC,EAAE,MAAM,GAAG,wBAAwB,CAAC;CACpD,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,sBAAsB,GAAG,qBAAqB,GAAG;IAC3D;;;;;;;OAOG;IACH,sBAAsB,CAAC,EAAE,OAAO,CAAC;CAClC,CAAC;AAEF,MAAM,MAAM,2BAA2B,GAAG,wBAAwB,GAAG,gBAAgB,CAAC;AAGtF,MAAM,MAAM,2BAA2B,GAAG;IACxC;;;OAGG;IACH,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B;;;;;;OAMG;IACH,uBAAuB,CAAC,EAAE,MAAM,CAAC;IACjC;;;OAGG;IACH,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B;;;;OAIG;IACH,eAAe,EAAE,MAAM,EAAE,CAAC;CAC3B,CAAC;AAGF,oBAAY,oBAAoB;IAC9B;;OAEG;IACH,MAAM,WAAW;IACjB;;OAEG;IACH,OAAO,YAAY;IACnB;;OAEG;IACH,MAAM,WAAW;IACjB,MAAM,WAAW;CAClB;AAGD;;;;GAIG;AACH,oBAAY,2BAA2B;IACrC;;OAEG;IACH,WAAW,eAAe;IAC1B;;OAEG;IACH,UAAU,cAAc;IACxB;;OAEG;IACH,UAAU,cAAc;IACxB;;OAEG;IACH,eAAe,mBAAmB;IAClC;;OAEG;IACH,gBAAgB,mBAAmB;IACnC;;OAEG;IACH,oBAAoB,uBAAuB;IAC3C;;OAEG;IACH,OAAO,YAAY;IACnB;;;;;OAKG;IACH,SAAS,cAAc;CACxB;AAGD,MAAM,MAAM,gBAAgB,GAAG;IAC7B;;OAEG;IACH,IAAI,EAAE,oBAAoB,CAAC;CAC5B,CAAC;AAGF,MAAM,MAAM,wBAAwB,GAAG;IACrC;;OAEG;IACH,IAAI,EAAE,SAAS,CAAC;IAChB,GAAG,EAAE,MAAM,CAAC;CACb,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG;IAChC,cAAc,CAAC,EAAE,MAAM,CAAC;CACzB,CAAC;AAEF,MAAM,MAAM,8BAA8B,GAAG,mBAAmB,CAAC;AACjE,MAAM,MAAM,sBAAsB,GAAG,mBAAmB,CAAC;AACzD,MAAM,MAAM,wBAAwB,GAAG,mBAAmB,CAAC;AAG3D,MAAM,MAAM,oCAAoC,GAAG;IACjD;;OAEG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;CAC7B,CAAC;AAGF,MAAM,MAAM,mCAAmC,GAAG;IAChD;;OAEG;IACH,IAAI,EAAE,SAAS,GAAG,QAAQ,CAAC;IAC3B;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC"}