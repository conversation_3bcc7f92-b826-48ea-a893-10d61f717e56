{"version": 3, "file": "SymbolView.js", "sourceRoot": "", "sources": ["../src/SymbolView.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AACvE,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAI5C,MAAM,UAAU,GACd,wBAAwB,CAAC,cAAc,CAAC,CAAC;AAE3C,MAAM,UAAU,UAAU,CAAC,KAAsB;IAC/C,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;QAC9B,OAAO,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC;IAC/B,CAAC;IACD,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;IAC1C,OAAO,CAAC,UAAU,CAAC,IAAI,WAAW,CAAC,EAAG,CAAC;AACzC,CAAC;AAED,SAAS,cAAc,CAAC,KAAsB;IAC5C,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/F,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC;IAChD,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,YAAY,CAAC;IACxC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;IAC9B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK;QACvB,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC;QAC9C,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;IAElC,OAAO;QACL,GAAG,KAAK;QACR,KAAK;QACL,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC;QACnC,QAAQ;QACR,IAAI;KACL,CAAC;AACJ,CAAC", "sourcesContent": ["import { Platform, requireNativeViewManager } from 'expo-modules-core';\nimport { processColor } from 'react-native';\n\nimport { NativeSymbolViewProps, SymbolViewProps } from './SymbolModule.types';\n\nconst NativeView: React.ComponentType<NativeSymbolViewProps> =\n  requireNativeViewManager('SymbolModule');\n\nexport function SymbolView(props: SymbolViewProps) {\n  if (Platform.OS === 'android') {\n    return <>{props.fallback}</>;\n  }\n  const nativeProps = getNativeProps(props);\n  return <NativeView {...nativeProps} />;\n}\n\nfunction getNativeProps(props: SymbolViewProps): NativeSymbolViewProps {\n  const colors = Array.isArray(props.colors) ? props.colors : props.colors ? [props.colors] : [];\n  const animated = !!props.animationSpec || false;\n  const type = props.type || 'monochrome';\n  const size = props.size || 24;\n  const style = props.style\n    ? [{ width: size, height: size }, props.style]\n    : { width: size, height: size };\n\n  return {\n    ...props,\n    style,\n    colors: colors.map((c) => processColor(c)),\n    tint: processColor(props.tintColor),\n    animated,\n    type,\n  };\n}\n"]}