{"version": 3, "file": "SymbolModule.types.js", "sourceRoot": "", "sources": ["../src/SymbolModule.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ColorValue, ProcessedColorValue, ViewProps } from 'react-native';\nimport type { SFSymbol } from 'sf-symbols-typescript';\n\nexport type SymbolViewProps = {\n  /**\n   * The name of the symbol. Symbols can be viewed in the [Apple SF Symbols app](https://developer.apple.com/sf-symbols/).\n   */\n  name: SFSymbol;\n  /**\n   * Fallback to render on Android and Web where `SF Symbols` are not available.\n   */\n  fallback?: React.ReactNode;\n  /**\n   * Determines the symbol variant to use.\n   * @default 'monochrome'\n   */\n  type?: SymbolType;\n  /**\n   * The scale of the symbol to render.\n   * @default 'unspecified'\n   */\n  scale?: SymbolScale;\n  /**\n   * The weight of the symbol to render.\n   * @default 'unspecified'\n   */\n  weight?: SymbolWeight;\n  /**\n   * An array of colors to use when the {@link SymbolType} is `palette`.\n   */\n  colors?: ColorValue | ColorValue[];\n  /**\n   * The size of the symbol.\n   * @default 24\n   */\n  size?: number;\n  /**\n   * The tint color to apply to the symbol.\n   */\n  tintColor?: ColorValue;\n  /**\n   * Determines how the image should be resized to fit its container.\n   * @default 'scaleToAspectFit'\n   */\n  resizeMode?: ContentMode;\n  /**\n   * The animation configuration to apply to the symbol.\n   */\n  animationSpec?: AnimationSpec;\n} & ViewProps;\n\n/**\n * It narrows down some props to types expected by the native/web side.\n * @hidden\n */\nexport interface NativeSymbolViewProps extends ViewProps {\n  name: string;\n  type: SymbolType;\n  scale?: SymbolScale;\n  weight?: SymbolWeight;\n  animated: boolean;\n  colors: (ProcessedColorValue | null | undefined)[];\n  tint: ProcessedColorValue | null | undefined;\n  resizeMode?: ContentMode;\n  animationSpec?: AnimationSpec;\n}\n\n/**\n * The weight of the symbol to render.\n */\nexport type SymbolWeight =\n  | 'unspecified'\n  | 'ultraLight'\n  | 'thin'\n  | 'light'\n  | 'regular'\n  | 'medium'\n  | 'semibold'\n  | 'bold'\n  | 'heavy'\n  | 'black';\n\n/**\n * The scale of the symbol to render.\n */\nexport type SymbolScale = 'default' | 'unspecified' | 'small' | 'medium' | 'large';\n\n/**\n * Determines how the image should be resized to fit its container.\n */\nexport type ContentMode =\n  | 'scaleToFill'\n  | 'scaleAspectFit'\n  | 'scaleAspectFill'\n  | 'redraw'\n  | 'center'\n  | 'top'\n  | 'bottom'\n  | 'left'\n  | 'right'\n  | 'topLeft'\n  | 'topRight'\n  | 'bottomLeft'\n  | 'bottomRight';\n\n/**\n * The animation configuration to apply to the symbol.\n */\nexport type AnimationSpec = {\n  /**\n   * The effect to apply to the symbol.\n   */\n  effect?: AnimationEffect;\n  /**\n   * If the animation should repeat.\n   */\n  repeating?: boolean;\n  /**\n   * The number of times the animation should repeat.\n   */\n  repeatCount?: number;\n  /**\n   * The duration of the animation in seconds.\n   */\n  speed?: number;\n  /**\n   * An object that specifies how the symbol’s layers should animate.\n   */\n  variableAnimationSpec?: VariableAnimationSpec;\n};\n\nexport type AnimationEffect = {\n  /**\n   * The type of animation to apply to the symbol.\n   */\n  type: AnimationType;\n  /**\n   * Whether the entire symbol should animate or just the individual layers.\n   * @default false\n   */\n  wholeSymbol?: boolean;\n  /**\n   * The direction of the animation.\n   */\n  direction?: 'up' | 'down';\n};\n\n/**\n * The type of animation to apply to the symbol.\n */\nexport type AnimationType = 'bounce' | 'pulse' | 'scale';\n\n/**\n * A variable color animation draws attention to a symbol by changing the opacity of the symbol’s layers.\n * You can choose to apply the effect to layers either cumulatively or iteratively.\n * For cumulative animations, each layer’s opacity remains changed until the end of the animation cycle.\n * For iterative animations, each layer’s opacity changes briefly before returning to its original state.\n * These effects are compounding, each value set to `true` will add an additional effect.\n */\nexport type VariableAnimationSpec = {\n  /**\n   * An effect that reverses each time it repeats.\n   */\n  reversing?: boolean;\n  /**\n   * An effect that doesn’t reverse each time it repeats.\n   */\n  nonReversing?: boolean;\n  /**\n   * This effect enables each successive variable layer, and the layer remains enabled until the end of the animation cycle. This effect cancels the iterative variant.\n   */\n  cumulative?: boolean;\n  /**\n   * An effect that momentarily enables each layer of a symbol in sequence.\n   */\n  iterative?: boolean;\n  /**\n   * An effect that hides inactive layers of a symbol.\n   * This effect hides inactive layers completely, rather than drawing them with reduced, but nonzero, opacity.\n   */\n  hideInactiveLayers?: boolean;\n  /**\n   * An effect that dims inactive layers of a symbol.\n   * This effect draws inactive layers with reduced, but nonzero, opacity.\n   */\n  dimInactiveLayers?: boolean;\n};\n\n/**\n * Determines the symbol variant to use.\n *\n * - `'monochrome'` - Creates a color configuration that specifies that the symbol image uses its monochrome variant.\n *\n * - `'hierarchical'` - Creates a color configuration with a color scheme that originates from one color.\n *\n * - `'palette'` - Creates a color configuration with a color scheme from a palette of multiple colors.\n *\n * - `'multicolor'` - Creates a color configuration that specifies that the symbol image uses its multicolor variant, if one exists.\n */\nexport type SymbolType = 'monochrome' | 'hierarchical' | 'palette' | 'multicolor';\n"]}